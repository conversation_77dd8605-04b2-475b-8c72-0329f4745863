var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSpeedDownhillOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M10.3176 25.2906C10.9648 25.2906 11.4971 25.7824 11.5611 26.4128L11.5676 26.5406L11.567 38.2905L14.4249 34.862C14.8374 34.367 15.5515 34.2716 16.0761 34.6204L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.9321 3.91945 35.1439 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.862L9.067 38.2905L9.0676 26.5406C9.0676 25.9365 9.49608 25.4325 10.0657 25.316L10.1898 25.297L10.3176 25.2906ZM40.9818 17.6027L43.3727 16.872C44.8896 21.8355 43.6943 27.0563 40.0625 30.8638L39.8111 31.1212L38.0433 29.3534C41.128 26.2687 42.2085 22.0325 41.0783 17.9346L40.9818 17.6027ZM35.0312 6.73675L35.3638 6.8566L34.4873 9.19791C30.0881 7.55101 24.8855 8.58509 21.5594 11.9111C16.9622 16.5083 16.8856 23.723 21.3295 28.4074L21.5594 28.6434L19.7916 30.4111C14.1402 24.7597 14.1402 15.7948 19.7916 10.1434C23.7389 6.1961 29.8034 4.92483 35.0312 6.73675ZM36.3531 10.8046L41.3199 8.39509L38.5206 13.2002C35.4823 18.3879 33.456 21.634 32.3351 23.0794L32.0866 23.3882L31.9426 23.5534L31.8128 23.6902C29.9947 25.5083 27.1149 25.5083 25.2968 23.6902C23.4787 21.8722 23.4787 18.7406 25.2968 16.9225C26.0529 16.1664 28.3763 14.8138 32.4137 12.763L33.9577 11.9862L36.3531 10.8046ZM33.7479 16.3321L34.9873 14.2681L33.1718 15.185C29.6514 16.9836 27.5738 18.181 27.0646 18.6902C26.2228 19.532 26.2228 21.0807 27.0646 21.9225C27.9064 22.7642 29.2032 22.7642 30.045 21.9225C30.4163 21.5512 31.1706 20.4675 32.2842 18.7028L32.8095 17.8628L33.7479 16.3321Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSpeedDownhillOutline);
export default __webpack_public_path__ + "static/media/speed_downhill_outline.6af02e04.svg";
export { ForwardRef as ReactComponent };