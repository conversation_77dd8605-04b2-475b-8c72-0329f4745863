import React from 'react';
import makeStyles from '@mui/styles/makeStyles';

type TimeProps = {
  classes?: Record<string, string>;
  time: Date | number;
};

const useStyles = makeStyles({
  root: {},
});

export const formatDate = (time: Date): string => {
  const workoutYear = time.getFullYear();
  const nowYear = new Date().getFullYear();

  return new Intl.DateTimeFormat(undefined, {
    year: workoutYear === nowYear ? undefined : 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  }).format(time);
};

function Time(props: TimeProps): React.ReactElement {
  const classes = useStyles(props);
  const { time: rawTime } = props;
  const date = typeof rawTime === 'number' ? new Date(rawTime) : rawTime;
  return (
    <time className={classes.root} dateTime={date.toISOString()}>
      {formatDate(date)}
    </time>
  );
}

export default Time;
