import { ButtonBase, IconButton } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import classNames from 'classnames';
import React from 'react';
import { renderDotsItem } from '../AliceCarousel/DotsItem';
import { AmplitudeEvent, useAmplitude } from '../Amplitude/Amplitude';
import { ReactComponent as ArrowLeft } from '../images/icons/ui-navigation/arrow_left_fill.svg';
import { ReactComponent as ArrowRight } from '../images/icons/ui-navigation/arrow_right_fill.svg';
import { Photo as PhotoType } from '../types/WorkoutPayload';
import Photo from './Photo';
import Theatre from './Theatre/Theatre';
import AliceCarousel from '../AliceCarousel';

type PhotosProps = {
  classes?: Record<string, string>;
  photos: PhotoType[];
};

const IMAGE_ASPECT_RATIO = 82 / 125;

const useStyles = makeStyles(
  (theme) => ({
    root: {
      '& .alice-carousel__dots': {
        minHeight: theme.spacing(4.5),
        marginRight: theme.spacing(4.5),
        marginLeft: theme.spacing(4.5),
        marginTop: 0,
        marginBottom: 0,
        paddingTop: theme.spacing(1),
      },
      display: 'flex',
      flexWrap: 'wrap',
      [['& .alice-carousel__prev-btn', '& .alice-carousel__next-btn'].join(',')]: {
        padding: 0,
        bottom: 0,
        position: 'absolute',
        width: 'auto',
      },
      '& .alice-carousel__prev-btn': {
        left: 0,
      },
      '& .alice-carousel__next-btn': {
        right: 0,
      },
    },
    icon: {
      width: theme.spacing(2),
      height: theme.spacing(2),
    },
    itemCommon: {
      margin: 'auto auto',
      maxWidth: 500,
      width: '100%',
      flexShrink: 0,
      flexGrow: 1,
    },
    item: {},
    imgButton: {
      width: '100%',
      backgroundColor: theme.palette.action.disabledBackground,
      paddingTop: `${IMAGE_ASPECT_RATIO * 100}%`,
    },
  }),
  { name: 'Photos' },
);

const IMAGE_KEY_NAME = 'imageId';

const updateQueryString = (selectedPhoto: string | undefined | null) => {
  const urlParams = new URLSearchParams(window.location.search);

  if (selectedPhoto !== urlParams.get(IMAGE_KEY_NAME)) {
    if (!selectedPhoto) {
      urlParams.delete(IMAGE_KEY_NAME);
    } else {
      urlParams.set(IMAGE_KEY_NAME, selectedPhoto);
    }
    const search = urlParams.toString();

    window.history.replaceState(
      null,
      document.title,
      (search ? '?' + search : window.location.pathname) + window.location.hash,
    );
  }
};

const PHOTO_SIZE: [number, number] = [500, IMAGE_ASPECT_RATIO * 500];

function Photos(props: PhotosProps): React.ReactElement {
  const classes = useStyles(props);
  const { photos } = props;
  const [selectedPhotoKey, setSelectedPhotoKey] = React.useState<string | undefined | null>(null);
  const { logEvent } = useAmplitude();
  const selectedPhoto = photos.find(({ key }) => selectedPhotoKey === key);

  React.useEffect(() => {
    const imageKey = new URLSearchParams(window.location.search).get(IMAGE_KEY_NAME);
    if (imageKey && photos.find(({ key }) => imageKey === key)) {
      setSelectedPhotoKey(imageKey);
    }
  }, []);

  React.useEffect(() => {
    if (selectedPhoto) {
      logEvent(AmplitudeEvent.SharedWorkoutAttachedMediaViewed, {
        MediaType: 'Photo',
      });
    }
  }, [selectedPhoto]);

  React.useEffect(() => {
    updateQueryString(selectedPhotoKey);
  }, [selectedPhotoKey, photos]);

  const handleClose = () => setSelectedPhotoKey(null);

  const renderPhoto = (photo: PhotoType) => (
    <div key={photo.key} className={classNames(classes.itemCommon, classes.item)}>
      <ButtonBase
        className={classes.imgButton}
        focusRipple
        onClick={() => setSelectedPhotoKey(photo.key)}
      >
        <Photo photo={photo} size={PHOTO_SIZE} />
      </ButtonBase>
    </div>
  );

  return (
    <>
      <Theatre
        open={!!selectedPhotoKey}
        onClose={handleClose}
        photos={photos}
        selectedPhoto={selectedPhoto}
        onChange={(photo) => setSelectedPhotoKey(photo?.key)}
      />
      <div className={classes.root}>
        {photos.length === 1 && renderPhoto(photos[0])}
        {photos.length > 1 && (
          <React.Suspense fallback={renderPhoto(photos[0])}>
            <AliceCarousel
              keyboardNavigation
              infinite
              renderDotsItem={renderDotsItem}
              renderNextButton={({ isDisabled }) => (
                <IconButton disabled={isDisabled} size="large">
                  <ArrowRight className={classes.icon} />
                </IconButton>
              )}
              renderPrevButton={({ isDisabled }) => (
                <IconButton disabled={isDisabled} size="large">
                  <ArrowLeft className={classes.icon} />
                </IconButton>
              )}
              items={photos.map(renderPhoto)}
            />
          </React.Suspense>
        )}
      </div>
    </>
  );
}

export default Photos;
