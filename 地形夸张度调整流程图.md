# 地形夸张度调整流程图

本文档通过流程图详细展示了 WorkoutMap 中地形夸张度调整的完整逻辑，基于地形变化图像 [dem_std_zoom_4_512.png](https://maps.suunto.com/static/media/dem_std_zoom_4_512.7908dc22.png) 进行处理。

## 1. 整体流程图

```mermaid
flowchart TD
    A[地图位置/缩放变化] --> B[触发地形更新]
    B --> C{Canvas 上下文存在?}
    
    C -->|否| D[使用默认夸张度 1.35]
    C -->|是| E[获取地形变化图像尺寸]
    
    E --> F[获取当前地图中心坐标]
    F --> G[地理坐标转墨卡托坐标]
    G --> H[墨卡托坐标转像素坐标]
    
    H --> I[从图像中采样像素数据]
    I --> J[提取蓝色通道值]
    J --> K[数值归一化 0-255 to 0-1]
    
    K --> L[计算地形夸张度]
    L --> M[应用夸张度到地形设置]
    
    D --> N[地形更新完成]
    M --> N
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style C fill:#fff3e0
    style L fill:#e8f5e8
```

## 2. 图像加载和初始化流程

```mermaid
flowchart TD
    A[WorkoutMap 构造函数] --> B[创建 Image 对象]
    B --> C[设置图像加载事件]
    C --> D[开始加载地形变化图像]
    
    D --> E{图像加载成功?}
    E -->|否| F[使用默认设置]
    E -->|是| G[创建 Canvas 元素]
    
    G --> H[设置 Canvas 尺寸]
    H --> I[获取 Canvas 上下文]
    I --> J[将图像绘制到 Canvas]
    J --> K[缓存 Canvas 上下文]
    
    K --> L[等待地图样式加载]
    L --> M[执行首次地形更新]
    
    F --> N[初始化完成]
    M --> N
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style E fill:#fff3e0
    style K fill:#e8f5e8
```

## 3. 坐标转换详细流程

```mermaid
flowchart TD
    A[当前地图中心坐标] --> B[地理坐标格式]
    B --> C[经度: -180 到 +180]
    B --> D[纬度: -90 到 +90]
    
    C --> E[MercatorCoordinate.fromLngLat]
    D --> E
    
    E --> F[墨卡托投影坐标]
    F --> G[X: 0-1 范围]
    F --> H[Y: 0-1 范围]
    
    G --> I[像素 X 坐标计算]
    H --> J[像素 Y 坐标计算]
    
    I --> K[X = xy.x * imageWidth]
    J --> L[Y = xy.y * imageHeight]
    
    K --> M[最终像素坐标]
    L --> M
    
    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style E fill:#e8f5e8
```

## 4. 像素数据采样流程

```mermaid
flowchart TD
    A[像素坐标] --> B[getImageData 调用]
    B --> C[参数 x, y, 1, 1]
    
    C --> D[获取 1x1 像素区域]
    D --> E[返回 ImageData 对象]
    
    E --> F[ImageData.data 数组]
    F --> G[RGBA 四个通道]
    
    G --> H[红色通道 data0]
    G --> I[绿色通道 data1]
    G --> J[蓝色通道 data2 - 使用这个]
    G --> K[透明度通道 data3]
    
    J --> L[提取蓝色通道值]
    L --> M[数值范围 0-255]
    M --> N[除以 255 归一化]
    N --> O[地形变化值 0-1]
    
    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style J fill:#e8f5e8
```

## 5. 地形变化图像说明

### 5.1 图像基本信息
- **文件路径**: [dem_std_zoom_4_512.png](https://maps.suunto.com/static/media/dem_std_zoom_4_512.7908dc22.png)
- **尺寸**: 512x512 像素
- **格式**: PNG
- **数据编码**: 蓝色通道表示地形变化程度
- **覆盖范围**: 全球地形

### 5.2 颜色编码含义

```mermaid
flowchart LR
    A[地形变化图像] --> B[颜色分析]
    
    B --> C[深蓝色区域]
    B --> D[中等蓝色区域]
    B --> E[浅蓝色区域]
    
    C --> F[地形变化大 高山峡谷]
    D --> G[地形变化中等 丘陵山地]
    E --> H[地形变化小 平原海洋]
    
    F --> I[夸张度约等于1.7 最大夸张]
    G --> J[夸张度约等于1.35 中等夸张]
    H --> K[夸张度约等于1.0 最小夸张]
    
    style A fill:#e1f5fe
    style I fill:#ffcdd2
    style J fill:#fff9c4
    style K fill:#c8e6c9
```

## 6. 夸张度计算流程

```mermaid
flowchart TD
    A[地形变化值 variance] --> B{数值范围检查}
    
    B --> C[variance = 0]
    B --> D[variance = 0.5]
    B --> E[variance = 1]
    
    C --> F[地形变化最大<br/>高山地区]
    D --> G[地形变化中等<br/>丘陵地区]
    E --> H[地形变化最小<br/>平原地区]
    
    F --> I[计算: 1 + 1-0 * 0.7]
    G --> J[计算: 1 + 1-0.5 * 0.7]
    H --> K[计算: 1 + 1-1 * 0.7]
    
    I --> L[exaggeration = 1.7<br/>放大 70%]
    J --> M[exaggeration = 1.35<br/>放大 35%]
    K --> N[exaggeration = 1.0<br/>无放大]
    
    L --> O[应用到地形设置]
    M --> O
    N --> O
    
    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style L fill:#ffcdd2
    style M fill:#fff9c4
    style N fill:#e8f5e8
```

## 7. 地形设置应用流程

```mermaid
flowchart TD
    A[计算得到的夸张度] --> B{数据源是否变化?}
    
    B -->|否| C[跳过地形更新]
    B -->|是| D[准备地形设置参数]
    
    D --> E[设置数据源]
    E --> F[设置夸张度插值]
    
    F --> G[缩放级别 8: exaggeration + zoomFactor]
    F --> H[缩放级别 12: exaggeration]
    F --> I[中间级别: 线性插值]
    
    G --> J[调用 setTerrain]
    H --> J
    I --> J
    
    J --> K[更新 demSource 标记]
    K --> L[地形设置完成]
    
    C --> M[地形更新完成]
    L --> M
    
    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style B fill:#fff3e0
    style J fill:#e8f5e8
```

## 8. 性能优化流程

```mermaid
flowchart TD
    A[地形更新请求] --> B{Canvas 上下文检查}
    
    B -->|不存在| C[使用默认夸张度<br/>避免复杂计算]
    B -->|存在| D[执行完整计算流程]
    
    D --> E[单像素采样<br/>最小计算量]
    E --> F[缓存 Canvas 上下文<br/>避免重复创建]
    F --> G[条件更新<br/>只在数据源变化时更新]
    
    C --> H[快速完成]
    G --> H
    
    H --> I[性能优化完成]
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style B fill:#fff3e0
    style C fill:#e8f5e8
```

## 9. 完整的数据流图

```mermaid
flowchart TD
    A[地形变化图像<br/>512x512 PNG] --> B[Canvas 缓存]
    B --> C[像素采样系统]
    
    C --> D[地理坐标输入]
    D --> E[坐标转换系统]
    E --> F[像素坐标输出]
    
    F --> G[蓝色通道提取]
    G --> H[地形变化值<br/>0-1 范围]
    
    H --> I[夸张度计算器]
    I --> J[夸张度输出<br/>1.0-1.7 范围]
    
    J --> K[地形渲染系统]
    K --> L[3D 地形显示]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style H fill:#e8f5e8
    style J fill:#fff9c4
```

## 10. 错误处理流程

```mermaid
flowchart TD
    A[地形更新开始] --> B{图像是否加载?}
    
    B -->|否| C[使用默认夸张度 1.35]
    B -->|是| D{Canvas 上下文是否可用?}
    
    D -->|否| E[重新初始化 Canvas]
    D -->|是| F{坐标是否有效?}
    
    E --> G[重试图像加载]
    F -->|否| H[使用地图中心坐标]
    F -->|是| I[执行正常计算流程]
    
    G --> J{重试成功?}
    J -->|否| C
    J -->|是| I
    
    C --> K[地形更新完成]
    H --> K
    I --> K
    
    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style B fill:#fff3e0
    style C fill:#e8f5e8
```

## 11. 总结

通过以上流程图，我们可以看到地形夸张度调整系统的工作流程：

### 11.1 核心步骤
1. **图像加载**: 预加载地形变化图像到 Canvas
2. **坐标转换**: 地理坐标 → 墨卡托坐标 → 像素坐标
3. **像素采样**: 从图像中提取蓝色通道值
4. **数值计算**: 根据地形变化值计算夸张度
5. **地形应用**: 将夸张度应用到地图地形设置

### 11.2 技术特点
- **数据驱动**: 基于真实地形数据计算夸张度
- **性能优化**: 使用缓存和条件更新
- **错误处理**: 完善的异常情况处理
- **动态适应**: 根据地形特征自动调整

### 11.3 应用效果
- **山地**: 高夸张度突出地形特征
- **平原**: 低夸张度保持真实性
- **混合地形**: 动态调整提供最佳视觉效果

这个系统通过精密的计算和优化，为用户提供了既美观又实用的运动轨迹地图体验。 