import { Typography } from '@mui/material';
import React, { FunctionComponent, ReactElement } from 'react';
import classNames from 'classnames';
import makeStyles from '@mui/styles/makeStyles';
import { useTranslation } from 'react-i18next';
import namespaces from '../../../../i18n/namespaces';
import summaryItemPhraseKeys from '../../summaryItemPhraseKeys';

type SummaryItemProps = {
  classes?: Record<string, string>;
  Icon?: FunctionComponent | null;
  unit?: string;
  name: string;
  value: string | number | ReactElement;
  small?: boolean;
};

const iconWidth = 2.0;
const useStyles = makeStyles(
  (theme) => ({
    root: {
      display: 'flex',
    },
    small: {},
    icon: {
      marginTop: '1.1rem',
      fontSize: '1.7rem',
      fontFamily: 'Suunto Icons',
      width: `${iconWidth}rem`,
      height: `${iconWidth}rem`,
    },
    left: {
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'center',
      width: `${iconWidth * 3}rem`,
      flexGrow: 0,
    },
    right: {
      flexGrow: 1,
    },
    value: {
      fontWeight: 'bold',
      '&$small': {
        fontWeight: 'normal',
      },
      /* since value can be svg icon */
      '& svg': {
        width: '1.7rem',
        height: '1.7rem',
        marginTop: '-0.2rem',
        marginBottom: '-0.4rem',
      },
    },
    unit: {},
    label: {
      lineHeight: 3 / 4,
      fontWeight: 'bold',
      '&$small': {
        lineHeight: 1.05,
        paddingTop: theme.spacing(1),
        fontWeight: 'normal',
      },
    },
  }),
  { name: 'SummaryItemView' },
);

const UNIT_VALUE = 'h5';
function SummaryItemView(props: SummaryItemProps): React.ReactElement {
  const classes = useStyles(props);
  const { t } = useTranslation([namespaces.PHRASES, namespaces.TRANSLATIONS]);
  const { unit, value, name, small = false, Icon } = props;
  return (
    <div className={classes.root}>
      {!small && (
        <div className={classes.left}>
          {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
          {/* @ts-ignore */}
          {Icon && <Icon className={classes.icon} />}
        </div>
      )}
      <div className={classes.right}>
        <div>
          <Typography
            component="span"
            variant={small ? UNIT_VALUE : 'h1'}
            className={classNames(classes.value, { [classes.small]: small })}
          >
            <strong>{value}</strong>
          </Typography>{' '}
          {!!unit && (
            <Typography
              component="span"
              variant={UNIT_VALUE}
              className={classNames(classes.unit, { [classes.small]: small })}
            >
              <strong>{unit}</strong>
            </Typography>
          )}
        </div>
        <Typography
          component="div"
          variant={small ? 'body2' : 'h6'}
          className={classNames(classes.label, { [classes.small]: small })}
        >
          {t(summaryItemPhraseKeys[name], { defaultValue: name })}
        </Typography>
      </div>
    </div>
  );
}

export default SummaryItemView;
