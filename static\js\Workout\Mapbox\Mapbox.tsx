import React, { ReactElement, useContext } from 'react';
import mapboxgl from '!mapbox-gl';
import { useTranslation } from 'react-i18next';
import { Workout } from '../../models/Workout';
import { MeasurementSystemContext } from '../../MeasurementSystem/MeasurementSystem';
import { Text } from '../../theme/theme';
import { PositionContext } from '../usePosition';
import { TWO_PANEL_BREAKPOINT } from '../workoutConfig';
import { MapClasses, WorkoutMap } from '../WorkoutMap';
import { useTheme } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import 'mapbox-gl/dist/mapbox-gl.css';
import CrossFadeOverlay from './CrossFadeOverlay';

const stickLength = 80;

const useMapStyles = makeStyles((theme) => ({
  labelContainer: {
    top: -stickLength + 'px !important',
  },
  labelWrapper: {
    transition: 'opacity 300ms ease-in',
    opacity: 1,
  },
  labelStick: {
    position: 'absolute',
    top: 4,
    width: 1,
    height: stickLength - 4,
    backgroundColor: theme.palette.background.default,
  },
  label: {
    position: 'absolute',
    borderRadius: 3,
    borderBottomLeftRadius: 0,
    padding: 3,
    color: theme.palette.text.primary,
    backgroundColor: theme.palette.background.default,
  },
  labelValue: {
    fontSize: '1.4rem',
    lineHeight: 1,
    fontWeight: 'bold',
  },
  labelTitle: {
    whiteSpace: 'nowrap',
    fontWeight: 'bold',
    fontSize: '0.8rem',
    lineHeight: 9 / 8,
    letterSpacing: 0.5,
    display: 'block',
    textTransform: 'uppercase',
  },
}));

const useStyles = makeStyles(
  (theme) => ({
    root: {
      zIndex: 0,
      height: 'fill-available',
      width: '100%',
      fallbacks: [{ height: '100vh' }],
      '& .mapboxgl-marker': {
        color: Text.dark,
        fontSize: '4rem',
      },
      '&.mapboxgl-map': {
        position: 'fixed',
      },
      '& .mapboxgl-ctrl-attrib.mapboxgl-compact.mapboxgl-compact-show': {
        maxWidth: 150,
        paddingTop: '9rem',
      },
      '& .mapboxgl-ctrl-attrib.mapboxgl-compact.mapboxgl-compact-show .mapboxgl-ctrl-attrib-inner a':
        {
          whiteSpace: 'nowrap',
        },
    },
    crossFade: {
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
    [theme.breakpoints.up(TWO_PANEL_BREAKPOINT)]: {
      root: {
        height: '100vh',
        '&.mapboxgl-map': {
          position: 'relative',
        },
      },
    },
  }),
  { name: 'Mapbox' },
);

mapboxgl.accessToken = process.env.REACT_APP_MAP_BOX_ACCESS_TOKEN || '';

interface MapBoxProps {
  workout: Workout;
  graph: string | null;
  style: string;
}

const makeClasses = (classes: MapClasses) => ({
  labelContainer: classes.labelContainer,
  labelWrapper: classes.labelWrapper,
  labelStick: classes.labelStick,
  label: classes.label,
  labelValue: classes.labelValue,
  labelTitle: classes.labelTitle,
});

function Mapbox({ workout, graph, style }: MapBoxProps): ReactElement {
  const mapContainer = React.useRef<HTMLDivElement>(null);
  const [map, setMap] = React.useState<WorkoutMap | null>(null);
  const [top, setTop] = React.useState<number>(0);
  const [isScreenshotTaken, setIsScreenshotTaken] = React.useState<boolean>(false);
  const mapClasses = useMapStyles();
  const classes = useStyles();
  const theme = useTheme();
  const { t } = useTranslation();
  const [measurementSystem] = useContext(MeasurementSystemContext);
  const { position, isPlaying, setIsPlaying, isMapInitialized, setIsMapInitialized } =
    React.useContext(PositionContext);

  function checkMapInteraction(event: mapboxgl.MapboxEvent & mapboxgl.EventData): void {
    if (!event.automatic) setIsPlaying(false);
  }

  React.useEffect(() => {
    const onScroll = () => {
      setTop(window.scrollY * -0.25);
    };
    onScroll();
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, []);

  React.useLayoutEffect(() => {
    if (!mapContainer.current) {
      /* this should never happen since the root div is always rendered */
      return;
    }
    const map = new WorkoutMap(
      mapContainer.current,
      workout,
      style,
      position,
      measurementSystem,
      makeClasses(mapClasses),
      () => setIsMapInitialized(true),
    );
    map.mapbox.on('mousedown', checkMapInteraction);
    map.mapbox.on('touchstart', checkMapInteraction);
    map.mapbox.on('zoomstart', checkMapInteraction);
    setMap(map);

    return () => {
      map.destructor();
    };
  }, []);

  React.useEffect(() => {
    if (map && graph) {
      map.setMeasurementSystem(measurementSystem);
      map.setClasses(makeClasses(mapClasses));
      const labels = { max: t('TXT_MAX'), min: t('TXT_MIN') };
      map.setGraph(graph, labels);
    }
  }, [graph, map, theme, measurementSystem, t]);

  React.useEffect(() => {
    if (map && graph) {
      map.setStyle(style);
      // Graph also needs reset after style change.
      const labels = { max: t('TXT_MAX'), min: t('TXT_MIN') };
      map.setGraph(graph, labels);
    }
  }, [style, map]);

  React.useEffect(() => {
    if (isScreenshotTaken || !isPlaying) {
      map?.setPosition(position);
    }
  }, [position, map, isPlaying, isScreenshotTaken]);

  React.useEffect(() => {
    if (isMapInitialized) {
      map?.setPlaying(isPlaying);
    }
  }, [isPlaying, map, isMapInitialized]);

  return (
    <div ref={mapContainer} className={classes.root} style={{ transform: `translateY(${top}px)` }}>
      {map && (
        <CrossFadeOverlay
          setIsScreenshotTaken={setIsScreenshotTaken}
          isMapInitialized={isMapInitialized}
          isPlaying={isPlaying}
          map={map}
          classes={{ root: classes.crossFade }}
        />
      )}
    </div>
  );
}

export default Mapbox;
