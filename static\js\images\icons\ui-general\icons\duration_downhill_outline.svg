var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgDurationDownhillOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.29L14.4249 34.8619C14.8668 34.3316 15.6551 34.2599 16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.29L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM27 4H32C32.6904 4 33.25 4.55964 33.25 5.25C33.25 5.89721 32.7582 6.42953 32.1278 6.49355L32 6.5H30.75L30.7506 8.10829C37.5807 8.74755 43 14.5643 43 21.55C43 28.9571 36.9071 35.05 29.5 35.05C22.0929 35.05 16 28.9571 16 21.55C16 14.564 21.4199 8.74698 28.2504 8.10819L28.25 6.5H27C26.3528 6.5 25.8205 6.00813 25.7565 5.37781L25.75 5.25C25.75 4.60279 26.2419 4.07047 26.8722 4.00645L27 4H32H27ZM29.5 10.55C23.4736 10.55 18.5 15.5236 18.5 21.55C18.5 27.5764 23.4736 32.55 29.5 32.55C35.5264 32.55 40.5 27.5764 40.5 21.55C40.5 15.5236 35.5264 10.55 29.5 10.55ZM29.5 12.75C34.3325 12.75 38.25 16.6675 38.25 21.5C38.25 23.8575 37.3116 26.0695 35.6742 27.7001C35.1851 28.1873 34.3936 28.1856 33.9065 27.6964C33.4193 27.2073 33.421 26.4158 33.9102 25.9287C35.0811 24.7626 35.75 23.1859 35.75 21.5C35.75 18.0482 32.9518 15.25 29.5 15.25C28.8097 15.25 28.25 14.6904 28.25 14C28.25 13.3096 28.8097 12.75 29.5 12.75ZM29.5 17.75C31.5711 17.75 33.25 19.4289 33.25 21.5C33.25 22.512 32.8456 23.463 32.1416 24.1617C31.6516 24.648 30.8601 24.645 30.3738 24.155C29.9199 23.6977 29.8923 22.9778 30.289 22.4884L30.4752 22.2822C30.6518 22.0629 30.75 21.7904 30.75 21.5C30.75 20.8528 30.2582 20.3205 29.6278 20.2565L29.3722 20.2435C28.7419 20.1795 28.25 19.6472 28.25 19C28.25 18.3096 28.8097 17.75 29.5 17.75ZM40.0324 7.77499L40.1339 7.86612L43.6339 11.3661C44.1221 11.8543 44.1221 12.6457 43.6339 13.1339C43.1783 13.5895 42.4585 13.6199 41.9676 13.225L41.8661 13.1339L38.3661 9.63388C37.878 9.14573 37.878 8.35427 38.3661 7.86612C38.8218 7.4105 39.5416 7.38013 40.0324 7.77499Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgDurationDownhillOutline);
export default __webpack_public_path__ + "static/media/duration_downhill_outline.dc0c2e3a.svg";
export { ForwardRef as ReactComponent };