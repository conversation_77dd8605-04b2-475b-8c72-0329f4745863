var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgDownArrowOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M23.8723 4.00645L24.0001 4C24.6473 4 25.1796 4.49187 25.2437 5.12219L25.2501 5.25L25.25 37.778L33.2231 26.2874C33.5904 25.758 34.2934 25.6001 34.8466 25.9014L34.9627 25.973C35.4921 26.3403 35.65 27.0433 35.3487 27.5965L35.2771 27.7126L24.0173 43.9405L12.7241 27.7141C12.3298 27.1474 12.4694 26.3684 13.0361 25.974C13.5649 25.606 14.2788 25.7031 14.6923 26.1783L14.7761 26.2859L22.75 37.743L22.7501 5.25C22.7501 4.60279 23.242 4.07047 23.8723 4.00645L24.0001 4L23.8723 4.00645Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgDownArrowOutline);
export default __webpack_public_path__ + "static/media/down_arrow_outline.17983360.svg";
export { ForwardRef as ReactComponent };