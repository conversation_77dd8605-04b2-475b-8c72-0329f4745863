# Suunto 核心模块详细导读

## 概述

本文档详细分析 Suunto 地图应用中的五个核心模块：Developer（开发者工具）、helpers（工具函数）、MeasurementSystem（测量系统）、models（数据模型）和 types（类型定义）。这些模块构成了应用的基础架构，提供了开发调试、数据处理、单位转换、业务建模和类型安全等核心功能。

---

## 1. Developer 模块 - 开发者工具系统

### 文件结构
```
static/js/Developer/
└── Developer.tsx                    # 开发者模式服务
```

### 核心功能

#### DeveloperContext 上下文
```typescript
export const DeveloperContext = React.createContext<boolean>(false);
DeveloperContext.displayName = 'DeveloperContext';
```

#### DeveloperService 服务组件
```typescript
export const DeveloperService = ({ children }: DebugServiceProps): ReactElement => {
  const [isDeveloper, setIsDeveloper] = React.useState<boolean>(getIsDeveloper());

  React.useEffect(() => {
    // 将开发者切换函数暴露到全局 window 对象
    window.isDeveloper = setIsDeveloperToLocalStorage;
    return () => {
      delete window.isDeveloper;
    };
  }, []);

  // 监听 localStorage 变化
  React.useEffect(() => {
    const storageListener = () => {
      setIsDeveloper(getIsDeveloper());
    };
    window.addEventListener('storage', storageListener);
    return () => {
      window.removeEventListener('storage', storageListener);
    };
  }, []);

  return <DeveloperContext.Provider value={isDeveloper}>{children}</DeveloperContext.Provider>;
};
```

### 使用方式

#### 激活开发者模式
```javascript
// 在浏览器控制台中执行
localStorage.setItem('isDeveloper', 'true');
// 或使用全局函数
window.isDeveloper(true);
```

#### 在组件中使用
```typescript
const isDeveloper = React.useContext(DeveloperContext);

if (isDeveloper) {
  // 显示开发者专用功能
  return <DebugPanel />;
}
```

### 设计特点
- **全局状态管理**: 通过 localStorage 持久化开发者状态
- **跨标签页同步**: 使用 storage 事件实现多标签页状态同步
- **安全清理**: 组件卸载时清理全局变量和事件监听器
- **开发体验**: 提供控制台快捷切换功能

---

## 2. helpers 模块 - 工具函数库

### 文件结构
```
static/js/helpers/
├── math.ts                          # 数学工具函数
├── noOp.tsx                         # 空操作函数
├── photos.ts                        # 照片处理工具
├── useLoading.tsx                   # 加载状态 Hook
├── usePrevious.tsx                  # 前值记录 Hook
└── useToggle.tsx                    # 切换状态 Hook
```

### 核心工具函数

#### 数学工具 (math.ts)
```typescript
export const scale = (
  input: number,
  inputRange: [number, number],
  targetRange: [number, number],
): number => {
  const [targetMin, targetMax] = targetRange;
  const [inputMin, inputMax] = inputRange;
  const percent = (input - inputMin) / (inputMax - inputMin);
  return percent * (targetMax - targetMin) + targetMin;
};
```

**应用场景**:
- 地图缩放级别转换
- 数据可视化的值域映射
- 动画进度计算

#### 空操作函数 (noOp.tsx)
```typescript
export default (): void => undefined;
```

**使用场景**:
- 默认回调函数
- 可选函数参数的默认值
- 防止空指针异常

#### 照片处理工具 (photos.ts)
```typescript
export const getPhotoUrl = (photo: PhotoType, photoSize: [number, number] | PhotoSize): string => {
  const size = [...(photo.sizes || [])].sort(smallerComparator).find(makeComparator(photoSize));
  return size?.url || photo.url;
};
```

**功能特点**:
- 智能尺寸选择算法
- 支持多种尺寸规格
- 降级处理机制

### React Hooks 工具

#### useLoading Hook
```typescript
export const useLoading = (initialState = false): [boolean, () => () => void] => {
  const [loading, setLoadingLocal] = React.useState<boolean>(initialState);

  const setLoading = () => {
    setLoadingLocal(true);
    return () => setLoadingLocal(false); // 返回清理函数
  };

  return [loading, setLoading];
};
```

**使用模式**:
```typescript
const [loading, setLoading] = useLoading();

const handleSubmit = async () => {
  const cleanup = setLoading(); // 开始加载
  try {
    await api.submit();
  } finally {
    cleanup(); // 结束加载
  }
};
```

#### usePrevious Hook
```typescript
export function usePrevious<T>(value: T): MutableRefObject<T | undefined>['current'] {
  const ref = React.useRef<T>();
  React.useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref.current;
}
```

**应用场景**:
- 比较前后值变化
- 动画过渡效果
- 性能优化判断

#### useToggle Hook
```typescript
const useToggle = (
  initialState = false,
  onValue = true,
  offValue = false,
): [boolean, () => void, () => void] => {
  const [on, setState] = React.useState(initialState);
  const handleOff = React.useCallback(() => setState(offValue), [offValue]);
  const handleOn = React.useCallback(() => setState(onValue), [onValue]);
  return [on, handleOn, handleOff];
};
```

**使用示例**:
```typescript
const [isVisible, show, hide] = useToggle(false);
const [isPlaying, play, pause] = useToggle(false);
```

---

## 3. MeasurementSystem 模块 - 测量单位系统

### 文件结构
```
static/js/MeasurementSystem/
├── MeasurementSystem.ts             # 测量系统核心逻辑
└── MeasurementSystemService.tsx     # 测量系统服务组件
```

### 核心枚举定义
```typescript
export enum MeasurementSystem {
  metric = 'METRIC',      // 公制系统
  imperial = 'IMPERIAL',  // 英制系统
}
```

### 智能检测算法

#### 基于语言的检测
```typescript
export const getMeasurementSystemByLanguage = (language: string): MeasurementSystem => {
  if (language === 'en-US') return MeasurementSystem.imperial;
  return MeasurementSystem.metric;
};
```

#### 基于国家的检测
```typescript
export const getMeasurementSystemByCountry = (country: string): MeasurementSystem => {
  if (['United States', 'Bahamas'].includes(country)) return MeasurementSystem.imperial;
  return MeasurementSystem.metric;
};
```

#### 综合检测策略
```typescript
export const getPreferredMeasurementSystem = (
  culture: Culture | null = null,
): MeasurementSystem => {
  let measurementSystem: MeasurementSystem | undefined;
  if (culture?.AutoDetectedCountry) {
    // 优先使用服务器检测的国家信息
    measurementSystem = getMeasurementSystemByCountry(culture.AutoDetectedCountry);
  } else {
    // 降级使用浏览器语言设置
    const { language } = navigator;
    measurementSystem = getMeasurementSystemByLanguage(language);
  }
  return measurementSystem;
};
```

### 服务组件实现
```typescript
function MeasurementSystemService(props: MeasurementServiceProps): React.ReactElement {
  const { children } = props;
  const [measurementSystem, setMeasurementSystem] = React.useState<MeasurementSystem>(
    getPreferredMeasurementSystem(),
  );

  React.useEffect(() => {
    // 异步加载文化信息并更新测量系统
    loadCulture().then(getPreferredMeasurementSystem).then(setMeasurementSystem).catch(noOp);
  }, []);

  const measurementSystemValue = React.useMemo<MeasurementSystemContextType>(
    () => [measurementSystem, setMeasurementSystem],
    [measurementSystem],
  );

  return (
    <MeasurementSystemContext.Provider value={measurementSystemValue}>
      {children}
    </MeasurementSystemContext.Provider>
  );
}
```

### 使用方式
```typescript
// 在组件中使用
const [measurementSystem, setMeasurementSystem] = React.useContext(MeasurementSystemContext);

// 根据测量系统显示不同单位
const distanceUnit = measurementSystem === MeasurementSystem.metric ? 'km' : 'mi';
const speedUnit = measurementSystem === MeasurementSystem.metric ? 'km/h' : 'mph';
```

---

## 4. models 模块 - 数据模型

### 文件结构
```
static/js/models/
└── Workout.ts                       # 运动数据模型
```

### 核心接口定义

#### POI (兴趣点) 接口
```typescript
export interface POI {
  label: string;    // 标签文本
  value: number;    // 数值
  lon: number;      // 经度
  lat: number;      // 纬度
  position: number; // 在轨迹中的位置 (0-1)
}
```

#### Route (路线) 接口
```typescript
export interface Route {
  poi: POI[];                                           // 兴趣点数组
  pts: [number, number, number | null, number, number?][]; // 轨迹点数组
  // [经度, 纬度, 海拔, 数值, 可选的额外数值]
}
```

### Workout 类核心实现

#### 构造函数
```typescript
constructor(
  workout: WorkoutPayload,
  cameraPayload: CameraPayload,
  public user: string,
  public id: string,
) {
  this.workout = workout;
  this.center = [workout.centerPosition.x, workout.centerPosition.y];
  this.cameraPath = CameraPath.create(this, cameraPayload);
}
```

#### 扩展数据获取方法
```typescript
// 获取位置数据流
getLocation(): LocationStreamExtension | undefined {
  return Workout.getExtension<LocationStreamExtension>(
    this.workout.extensions,
    WorkoutExtensionType.LocationStreamExtension,
  );
}

// 获取心率数据流
getHeartrate(): HeartrateStreamExtension | undefined {
  return Workout.getExtension<HeartrateStreamExtension>(
    this.workout.extensions,
    WorkoutExtensionType.HeartrateStreamExtension,
  );
}

// 获取速度数据流
getSpeed(): SpeedStreamExtension | undefined {
  return Workout.getExtension<SpeedStreamExtension>(
    this.workout.extensions,
    WorkoutExtensionType.SpeedStreamExtension,
  );
}

// 获取海拔数据流
getAltitude(): AltitudeStreamExtension | undefined {
  return Workout.getExtension<AltitudeStreamExtension>(
    this.workout.extensions,
    WorkoutExtensionType.AltitudeStreamExtension,
  );
}
```

#### 静态路线生成方法
```typescript
static getRoute(graph: string, workout: Workout, labels: object): Route {
  // 1. 获取位置数据
  const locations = workout.getLocation();
  if (!locations?.locationPoints?.length) {
    return { poi: [], pts: [] };
  }

  // 2. 获取对应的数值扩展
  const valueExtension = PathConfiguration[graph]?.getExtension(workout);
  
  // 3. 生成轨迹点数组
  const pts = locations.locationPoints.map((point, index) => {
    const value = valueExtension?.points?.[index]?.value || 0;
    return [
      point.longitude,
      point.latitude, 
      point.altitude,
      value
    ];
  });

  // 4. 生成兴趣点
  const poi = generatePOIFromData(pts, labels);

  return { poi, pts };
}
```

### 扩展系统架构

#### 通用扩展获取器
```typescript
static getExtension<T extends Extension>(
  extensions: Extensions,
  type: WorkoutExtensionType | string,
): T | undefined {
  return extensions.find((extension) => extension.type === type) as T | undefined;
}
```

#### 专用运动类型扩展
```typescript
// 游泳扩展
getSwimmingHeaderExtension(): SwimmingHeaderExtension | undefined {
  return Workout.getExtension<SwimmingHeaderExtension>(
    this.workout.extensions,
    WorkoutExtensionType.SwimmingHeaderExtension,
  );
}

// 滑雪扩展
getSkiExtension(): SkiExtension | undefined {
  return Workout.getExtension<SkiExtension>(
    this.workout.extensions,
    WorkoutExtensionType.SkiExtension,
  );
}

// 潜水扩展
getDiveHeaderExtension(): DiveHeaderExtension | undefined {
  return Workout.getExtension<DiveHeaderExtension>(
    this.workout.extensions,
    WorkoutExtensionType.DiveHeaderExtension,
  );
}
```

---

## 5. types 模块 - 类型定义系统

### 文件结构
```
static/js/types/
└── WorkoutPayload.ts                # 运动数据类型定义
```

### 基础类型定义

#### 位置类型
```typescript
export type WorkoutPosition = {
  x: number; // 经度
  y: number; // 纬度
};

export interface Point {
  timestamp: number; // 时间戳
}

export interface ValuePoint extends Point {
  value: number; // 数值
}
```

#### 位置点详细信息
```typescript
export interface LocationPoint {
  altitude: number;   // 海拔
  bearing: null;      // 方向（当前为空）
  ehpe: null;         // 水平精度估计（当前为空）
  latitude: number;   // 纬度
  longitude: number;  // 经度
  speed: number;      // 速度
  timestamp: number;  // 时间戳
}
```

### 扩展系统类型

#### 扩展类型枚举
```typescript
export enum WorkoutExtensionType {
  AltitudeStreamExtension = 'AltitudeStreamExtension',
  HeartrateStreamExtension = 'HeartrateStreamExtension',
  LocationStreamExtension = 'LocationStreamExtension',
  SpeedStreamExtension = 'SpeedStreamExtension',
  SummaryExtension = 'SummaryExtension',
  FitnessExtension = 'FitnessExtension',
  SwimmingHeaderExtension = 'SwimmingHeaderExtension',
  SkiExtension = 'SkiExtension',
  DiveHeaderExtension = 'DiveHeaderExtension',
  MultisportMarkerExtension = 'MultisportMarkerExtension',
  // ... 更多扩展类型
}
```

#### 基础扩展接口
```typescript
export interface Extension {
  type: WorkoutExtensionType;
}

export interface ValuePointExtension extends Extension {
  points: ValuePoint[];
  timestamps?: number[];  // 可选的时间戳数组
  values?: number[];      // 可选的数值数组
}
```

#### 具体扩展实现
```typescript
// 心率数据扩展
export type HeartRateStreamPoint = ValuePoint;
export interface HeartrateStreamExtension extends ValuePointExtension {
  type: WorkoutExtensionType.HeartrateStreamExtension;
  points: HeartRateStreamPoint[];
}

// 位置数据扩展
export interface LocationStreamExtension extends ValuePointExtension {
  type: WorkoutExtensionType.LocationStreamExtension;
  locationPoints: LocationPoint[];
}

// 海拔数据扩展
export type AltitudeStreamPoint = ValuePoint;
export interface AltitudeStreamExtension extends ValuePointExtension {
  type: WorkoutExtensionType.AltitudeStreamExtension;
  points: AltitudeStreamPoint[];
}
```

### 复杂业务类型

#### 汇总扩展
```typescript
export interface SummaryExtension extends Extension {
  [key: string]: unknown;
  type: WorkoutExtensionType.SummaryExtension;
  avgSpeed: number | null;        // 平均速度
  avgPower: number | null;        // 平均功率
  maxPower: number | null;        // 最大功率
  avgCadence: number | null;      // 平均步频
  maxCadence: number | null;      // 最大步频
  ascent: number | null;          // 上升高度
  descent: number | null;         // 下降高度
  ascentTime: number | null;      // 上升时间
  descentTime: number | null;     // 下降时间
  pte: number | null;             // 训练效果
  peakEpoc: number | null;        // 峰值过量氧耗
  performanceLevel: number | null; // 表现水平
  weather: unknown | null;        // 天气信息
  minTemperature: number | null;  // 最低温度
  avgTemperature: number | null;  // 平均温度
  workoutType: unknown;           // 运动类型
  feeling: number;                // 感受评分
  gear: Gear;                     // 装备信息
  exerciseId: string | null;      // 练习ID
  recoveryTime: number | null;    // 恢复时间
  apps: Record<string, unknown>[]; // 应用信息
}
```

#### 照片类型
```typescript
export enum PhotoSize {
  small = 's',
  medium = 'm',
  large = 'l',
}

export interface PhotoSizeItem {
  size: PhotoSize;
  url: string;
  height: number;
  width: number;
}

export interface Photo {
  coverImage: boolean;      // 是否为封面图片
  description: string;      // 描述
  height: number;          // 高度
  key: string;             // 唯一标识
  location: null;          // 位置信息（当前为空）
  timestamp: number;       // 时间戳
  totalTime: number;       // 总时间
  url: string;             // 原始URL
  username: string;        // 用户名
  width: number;           // 宽度
  workoutKey: string;      // 运动记录标识
  sizes: PhotoSizeItem[];  // 不同尺寸的图片
}
```

#### 主要运动数据载荷
```typescript
export interface WorkoutPayload {
  username: string;                    // 用户名
  sharingFlags: number;               // 分享标志
  activityId: number;                 // 活动类型ID
  key: string;                        // 唯一标识
  description: string;                // 描述
  startTime: number;                  // 开始时间
  stopTime: number;                   // 结束时间
  totalTime: number;                  // 总时间
  totalDistance: number;              // 总距离
  totalAscent: number;                // 总上升
  totalDescent: number;               // 总下降
  startPosition: WorkoutPosition;     // 开始位置
  stopPosition: WorkoutPosition;      // 结束位置
  centerPosition: WorkoutPosition;    // 中心位置
  maxSpeed: number;                   // 最大速度
  polyline: string;                   // 轨迹编码
  visibilityGroups: boolean;          // 群组可见性
  visibilityExplore: boolean;         // 探索可见性
  visibilityFriends: boolean;         // 好友可见性
  visibilityFacebook: boolean;        // Facebook可见性
  visibilityTwitter: boolean;         // Twitter可见性
  stepCount: number;                  // 步数
  recoveryTime: number;               // 恢复时间
  cumulativeRecoveryTime: number;     // 累积恢复时间
  rankings: WorkoutRankings;          // 排名信息
  reactionCount: number;              // 反应数量
  isManuallyAdded: boolean;           // 是否手动添加
  hrdata: HrData;                     // 心率数据
  energyConsumption: number;          // 能量消耗
  commentCount: number;               // 评论数量
  viewCount: number;                  // 查看次数
  extensions: Extensions;             // 扩展数据数组
  minAltitude: number;                // 最低海拔
  maxAltitude: number;                // 最高海拔
  avgPace: number;                    // 平均配速
  avgSpeed: number;                   // 平均速度
  userPhoto: string | null;           // 用户头像
  fullname: string;                   // 全名
  photos: Photo[];                    // 照片数组
  availableExtensions: WorkoutExtensionType[]; // 可用扩展类型
}
```

## 模块间协作关系

### 数据流向
```
types (类型定义) 
  ↓ 
models (数据建模) 
  ↓ 
MeasurementSystem (单位转换) 
  ↓ 
helpers (工具处理) 
  ↓ 
Developer (调试输出)
```

### 依赖关系
- **models** 依赖 **types** 进行类型约束
- **MeasurementSystem** 使用 **helpers** 的工具函数
- **Developer** 为所有模块提供调试支持
- **helpers** 为其他模块提供通用工具

### 使用场景
1. **数据获取**: types → models → 业务组件
2. **单位转换**: MeasurementSystem → 显示组件
3. **工具支持**: helpers → 各种组件
4. **开发调试**: Developer → 全局调试

这五个核心模块构成了 Suunto 地图应用的基础架构，提供了类型安全、数据建模、工具支持和开发调试等关键能力，是整个应用稳定运行的基石。

## 深度分析和最佳实践

### 1. Developer 模块的高级用法

#### 条件渲染开发工具
```typescript
const DebugPanel = () => {
  const isDeveloper = React.useContext(DeveloperContext);

  if (!isDeveloper) return null;

  return (
    <div style={{ position: 'fixed', top: 0, right: 0, background: 'red', color: 'white' }}>
      <h3>Debug Info</h3>
      <button onClick={() => console.log('Current state:', store.getState())}>
        Log State
      </button>
      <button onClick={() => window.location.reload()}>
        Reload
      </button>
    </div>
  );
};
```

#### 开发者专用 API 调用
```typescript
const useDebugAPI = () => {
  const isDeveloper = React.useContext(DeveloperContext);

  return React.useMemo(() => ({
    logEvent: isDeveloper ? console.log : () => {},
    debugFetch: isDeveloper ?
      (url: string) => fetch(url + '?debug=true') :
      (url: string) => fetch(url),
    showPerformanceMetrics: isDeveloper
  }), [isDeveloper]);
};
```

### 2. helpers 模块的扩展模式

#### 自定义 Hook 组合
```typescript
// 组合多个 hooks 创建复杂状态管理
const useWorkoutPlayer = (workout: Workout) => {
  const [isPlaying, play, pause] = useToggle(false);
  const [loading, setLoading] = useLoading();
  const [position, setPosition] = React.useState(0);
  const previousPosition = usePrevious(position);

  const playWorkout = React.useCallback(async () => {
    const cleanup = setLoading();
    try {
      play();
      // 播放逻辑
    } finally {
      cleanup();
    }
  }, [play, setLoading]);

  return {
    isPlaying,
    loading,
    position,
    previousPosition,
    playWorkout,
    pause
  };
};
```

#### 数学工具的实际应用
```typescript
// 在地图组件中使用 scale 函数
const MapComponent = ({ workout }: { workout: Workout }) => {
  const mapRef = useRef<Map>();

  const updateProgress = (progress: number) => {
    // 将进度 (0-1) 映射到轨迹点索引
    const pointIndex = Math.floor(scale(
      progress,
      [0, 1],
      [0, workout.getLocation()?.locationPoints?.length - 1]
    ));

    // 将心率值映射到颜色强度
    const heartRate = workout.getHeartrate()?.points[pointIndex]?.value || 0;
    const colorIntensity = scale(heartRate, [60, 200], [0, 1]);

    updateMapVisualization(pointIndex, colorIntensity);
  };
};
```

### 3. MeasurementSystem 的国际化集成

#### 动态单位转换
```typescript
const useMeasurementConverter = () => {
  const [measurementSystem] = React.useContext(MeasurementSystemContext);

  return React.useMemo(() => ({
    distance: (meters: number) => {
      if (measurementSystem === MeasurementSystem.imperial) {
        const miles = meters * 0.000621371;
        return { value: miles, unit: 'mi' };
      }
      const km = meters / 1000;
      return { value: km, unit: 'km' };
    },

    speed: (mps: number) => {
      if (measurementSystem === MeasurementSystem.imperial) {
        const mph = mps * 2.23694;
        return { value: mph, unit: 'mph' };
      }
      const kmh = mps * 3.6;
      return { value: kmh, unit: 'km/h' };
    },

    elevation: (meters: number) => {
      if (measurementSystem === MeasurementSystem.imperial) {
        const feet = meters * 3.28084;
        return { value: feet, unit: 'ft' };
      }
      return { value: meters, unit: 'm' };
    }
  }), [measurementSystem]);
};
```

#### 格式化显示组件
```typescript
const MeasurementDisplay = ({
  value,
  type
}: {
  value: number;
  type: 'distance' | 'speed' | 'elevation'
}) => {
  const converter = useMeasurementConverter();
  const converted = converter[type](value);

  return (
    <span>
      {converted.value.toFixed(2)} {converted.unit}
    </span>
  );
};
```

### 4. models 模块的数据处理模式

#### 数据缓存和优化
```typescript
class WorkoutCache {
  private static cache = new Map<string, Workout>();

  static get(key: string): Workout | undefined {
    return this.cache.get(key);
  }

  static set(key: string, workout: Workout): void {
    // 限制缓存大小
    if (this.cache.size >= 10) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, workout);
  }

  static generateRoute(workout: Workout, graph: string): Route {
    const cacheKey = `${workout.id}-${graph}`;
    let route = this.routeCache.get(cacheKey);

    if (!route) {
      route = Workout.getRoute(graph, workout, {});
      this.routeCache.set(cacheKey, route);
    }

    return route;
  }
}
```

#### 数据流处理管道
```typescript
const processWorkoutData = (workout: Workout) => {
  return {
    // 基础信息提取
    basic: {
      duration: workout.workout.totalTime,
      distance: workout.workout.totalDistance,
      startTime: new Date(workout.workout.startTime)
    },

    // 传感器数据处理
    sensors: {
      heartRate: workout.getHeartrate()?.points.map(p => ({
        timestamp: p.timestamp,
        value: p.value,
        zone: calculateHeartRateZone(p.value)
      })),

      location: workout.getLocation()?.locationPoints.map(p => ({
        lat: p.latitude,
        lng: p.longitude,
        alt: p.altitude,
        timestamp: p.timestamp
      }))
    },

    // 统计数据计算
    statistics: calculateWorkoutStatistics(workout),

    // 可视化数据生成
    visualization: generateVisualizationData(workout)
  };
};
```

### 5. types 模块的类型安全实践

#### 类型守卫函数
```typescript
// 扩展类型守卫
export const isLocationExtension = (
  extension: Extension
): extension is LocationStreamExtension => {
  return extension.type === WorkoutExtensionType.LocationStreamExtension;
};

export const isHeartrateExtension = (
  extension: Extension
): extension is HeartrateStreamExtension => {
  return extension.type === WorkoutExtensionType.HeartrateStreamExtension;
};

// 使用类型守卫
const processExtensions = (extensions: Extensions) => {
  extensions.forEach(extension => {
    if (isLocationExtension(extension)) {
      // TypeScript 现在知道这是 LocationStreamExtension
      console.log(`Location points: ${extension.locationPoints.length}`);
    } else if (isHeartrateExtension(extension)) {
      // TypeScript 现在知道这是 HeartrateStreamExtension
      console.log(`Heart rate points: ${extension.points.length}`);
    }
  });
};
```

#### 泛型工具类型
```typescript
// 提取扩展数据的工具类型
type ExtractExtensionData<T extends Extension> = T extends ValuePointExtension
  ? T['points']
  : never;

// 扩展映射类型
type ExtensionMap = {
  [WorkoutExtensionType.LocationStreamExtension]: LocationStreamExtension;
  [WorkoutExtensionType.HeartrateStreamExtension]: HeartrateStreamExtension;
  [WorkoutExtensionType.AltitudeStreamExtension]: AltitudeStreamExtension;
  [WorkoutExtensionType.SpeedStreamExtension]: SpeedStreamExtension;
};

// 类型安全的扩展获取器
const getTypedExtension = <T extends keyof ExtensionMap>(
  extensions: Extensions,
  type: T
): ExtensionMap[T] | undefined => {
  return extensions.find(ext => ext.type === type) as ExtensionMap[T] | undefined;
};
```

#### 条件类型和映射类型
```typescript
// 根据运动类型确定可用扩展
type ActivityExtensions<T extends number> =
  T extends 21 ? SwimmingHeaderExtension | LocationStreamExtension :
  T extends 15 ? SkiExtension | LocationStreamExtension :
  LocationStreamExtension | HeartrateStreamExtension;

// 运动特定的数据处理
const processActivitySpecificData = <T extends number>(
  activityId: T,
  extensions: Extensions
): ActivityExtensions<T>[] => {
  return extensions.filter(ext => {
    switch (activityId) {
      case 21: // Swimming
        return ext.type === WorkoutExtensionType.SwimmingHeaderExtension ||
               ext.type === WorkoutExtensionType.LocationStreamExtension;
      case 15: // Skiing
        return ext.type === WorkoutExtensionType.SkiExtension ||
               ext.type === WorkoutExtensionType.LocationStreamExtension;
      default:
        return ext.type === WorkoutExtensionType.LocationStreamExtension ||
               ext.type === WorkoutExtensionType.HeartrateStreamExtension;
    }
  }) as ActivityExtensions<T>[];
};
```

## 性能优化策略

### 1. 内存管理
```typescript
// 使用 WeakMap 避免内存泄漏
const workoutProcessors = new WeakMap<Workout, ProcessedWorkoutData>();

const getProcessedWorkout = (workout: Workout): ProcessedWorkoutData => {
  let processed = workoutProcessors.get(workout);
  if (!processed) {
    processed = processWorkoutData(workout);
    workoutProcessors.set(workout, processed);
  }
  return processed;
};
```

### 2. 计算优化
```typescript
// 使用 useMemo 缓存昂贵计算
const WorkoutAnalysis = ({ workout }: { workout: Workout }) => {
  const statistics = React.useMemo(() => {
    return calculateComplexStatistics(workout);
  }, [workout.id]); // 只依赖 ID，避免不必要的重计算

  const visualizationData = React.useMemo(() => {
    return generateVisualizationData(workout);
  }, [workout.id, statistics]);

  return <AnalysisChart data={visualizationData} />;
};
```

### 3. 异步处理
```typescript
// 使用 Web Workers 处理大量数据
const processLargeWorkout = async (workout: Workout): Promise<ProcessedData> => {
  if (workout.getLocation()?.locationPoints.length > 10000) {
    // 使用 Web Worker 处理大数据集
    const worker = new Worker('/workers/workout-processor.js');
    return new Promise((resolve) => {
      worker.postMessage(workout);
      worker.onmessage = (e) => resolve(e.data);
    });
  }

  // 小数据集直接处理
  return processWorkoutData(workout);
};
```

这些核心模块通过精心设计的架构和最佳实践，为 Suunto 地图应用提供了坚实的技术基础，确保了应用的可维护性、可扩展性和性能表现。
