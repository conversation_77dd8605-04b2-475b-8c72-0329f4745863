# WorkoutMap 地形夸张度调整策略详解

本文档详细分析了 WorkoutMap 类中地形夸张度的调整策略，包括其工作原理、实现方式和优化效果。

## 1. 地形夸张度概述

### 1.1 什么是地形夸张度

地形夸张度（Terrain Exaggeration）是指在地图渲染中，为了增强地形的视觉效果，将实际的地形高度进行放大显示的技术。在 WorkoutMap 中，地形夸张度用于：

- **增强视觉效果**：让地形特征更加明显
- **改善用户体验**：提供更好的地形感知
- **适应不同地形**：根据地形特征动态调整

### 1.2 夸张度的数值含义

```typescript
// 夸张度计算公式
exaggeration = 1 + (1 - variance) * 0.7
```

- **exaggeration = 1.0**：无夸张，显示真实地形高度
- **exaggeration = 1.35**：默认夸张度，地形高度放大 35%
- **exaggeration = 1.7**：最大夸张度，地形高度放大 70%

## 2. 地形夸张度调整策略

### 2.1 基础夸张度设置

**代码实现**：
```typescript
updateTerrain(): void {
  const center = this.mapbox.getCenter();
  let exaggeration = 1.35; // 默认地形夸张度
  let source = 'mapbox-dem'; // 默认使用 Mapbox DEM
}
```

**策略说明**：
- 设置基础夸张度为 1.35，提供适度的地形增强
- 这个值经过测试，在大多数地形下都能提供良好的视觉效果
- 作为所有地形类型的基准夸张度

### 2.2 基于地形变化的动态调整

**代码实现**：
```typescript
// 根据地形变化图像调整夸张度
if (this.elevationContext) {
  const size = this.elevationImage.width;
  const xy = MercatorCoordinate.fromLngLat(center);

  // 获取当前位置的地形变化值（0-1）
  const variance =
    this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1).data[2] / 255;
  // 根据地形变化调整夸张度
  exaggeration = 1 + (1 - variance) * 0.7;
}
```

**策略说明**：
- **地形变化图像**：使用预加载的地形变化图像 `varianceMap`
- **像素采样**：从图像中获取当前位置的地形变化值
- **动态计算**：根据地形变化程度调整夸张度

### 2.3 地形变化值的含义

```typescript
// 地形变化值计算
const variance = this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1).data[2] / 255;
```

**数值含义**：
- **variance = 0**：地形变化最大（山地、丘陵）
- **variance = 1**：地形变化最小（平原、海洋）
- **variance = 0.5**：中等地形变化

**夸张度调整逻辑**：
- **山地/丘陵**（variance ≈ 0）：exaggeration ≈ 1.7（最大夸张）
- **平原**（variance ≈ 0.5）：exaggeration ≈ 1.35（中等夸张）
- **海洋**（variance ≈ 1）：exaggeration ≈ 1.0（最小夸张）

## 3. 地形变化图像系统

### 3.1 图像加载和缓存

**代码实现**：
```typescript
constructor(...args) {
  // 初始化地形变化图像
  const img = this.elevationImage;

  img.onload = () => {
    // 创建 Canvas 来处理地形变化图像
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    const gc = canvas.getContext('2d');
    if (gc) {
      gc.drawImage(img, 0, 0);
      this.elevationContext = gc; // 缓存 Canvas 上下文
    }
    // 等待样式加载完成后更新地形
    this.styleLoadPromise.then(() => this.updateTerrain());
  };

  img.src = varianceMap; // 预加载地形变化图像
}
```

**系统特点**：
- **预加载**：在构造函数中预加载地形变化图像
- **Canvas 缓存**：将图像绘制到 Canvas 中，便于像素采样
- **异步处理**：等待图像加载完成后进行地形更新

### 3.2 坐标转换和像素采样

**代码实现**：
```typescript
const size = this.elevationImage.width;
const xy = MercatorCoordinate.fromLngLat(center);

// 获取当前位置的地形变化值（0-1）
const variance =
  this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1).data[2] / 255;
```

**处理流程**：
1. **坐标转换**：将地理坐标转换为墨卡托坐标
2. **像素映射**：将墨卡托坐标映射到图像像素位置
3. **颜色采样**：获取指定像素的 RGB 值
4. **数值提取**：使用蓝色通道（data[2]）作为地形变化值

## 4. 缩放级别自适应

### 4.1 缩放级别插值

**代码实现**：
```typescript
// 缩放因子（当前设为 0，等待 Mapbox 修复相关问题）
// 相关 issue: https://github.com/mapbox/mapbox-gl-js/issues/11044
const zoomFactor = 0;

// 如果数据源发生变化，更新地形设置
if (source != this.demSource) {
  this.mapbox.setTerrain({
    source,
    exaggeration: [
      'interpolate', // 插值函数
      ['linear'], // 线性插值
      ['zoom'], // 基于缩放级别
      8, // 缩放级别 8
      exaggeration + zoomFactor, // 对应的夸张度
      12, // 缩放级别 12
      exaggeration, // 对应的夸张度
    ],
  });
  this.demSource = source;
}
```

**插值策略**：
- **缩放级别 8**：使用 `exaggeration + zoomFactor`
- **缩放级别 12**：使用 `exaggeration`
- **中间级别**：线性插值计算

### 4.2 缩放级别的影响

| 缩放级别 | 夸张度效果 | 适用场景 |
|---------|-----------|----------|
| 8-10 | 较高夸张度 | 概览视图，强调地形特征 |
| 10-12 | 中等夸张度 | 详细视图，平衡视觉效果 |
| 12+ | 较低夸张度 | 精细视图，接近真实地形 |

## 5. 数据源相关的夸张度策略

### 5.1 MML DEM vs Mapbox DEM

**代码实现**：
```typescript
// 在缩放级别 >= 10 时，检查是否在芬兰境内
if (this.mapbox.getZoom() >= 10) {
  for (const box of DEM_SOURCE_MML_BOUNDS_COORDINATES) {
    if (
      center.lat >= box[0] &&
      center.lat <= box[2] &&
      center.lng >= box[1] &&
      center.lng <= box[3]
    ) {
      source = 'mml-dem'; // 在芬兰境内使用 MML DEM
      break;
    }
  }
}
```

**策略差异**：
- **MML DEM**：芬兰地区高精度数据，支持更精细的夸张度调整
- **Mapbox DEM**：全球标准数据，使用统一的夸张度策略

### 5.2 数据源切换时的夸张度处理

```typescript
// 如果数据源发生变化，更新地形设置
if (source != this.demSource) {
  this.mapbox.setTerrain({
    source,
    exaggeration: [
      'interpolate',
      ['linear'],
      ['zoom'],
      8,
      exaggeration + zoomFactor,
      12,
      exaggeration,
    ],
  });
  this.demSource = source;
}
```

**处理逻辑**：
- 只在数据源真正变化时才更新地形设置
- 保持夸张度计算的连续性
- 避免频繁的地形重新计算

## 6. 性能优化策略

### 6.1 缓存机制

**代码实现**：
```typescript
// 缓存 Canvas 上下文
if (gc) {
  gc.drawImage(img, 0, 0);
  this.elevationContext = gc; // 缓存 Canvas 上下文
}
```

**优化效果**：
- **避免重复加载**：地形变化图像只加载一次
- **快速像素采样**：直接从缓存的 Canvas 中读取像素数据
- **减少内存分配**：重用 Canvas 上下文对象

### 6.2 条件更新

**代码实现**：
```typescript
// 如果数据源发生变化，更新地形设置
if (source != this.demSource) {
  this.mapbox.setTerrain({
    source,
    exaggeration: [...],
  });
  this.demSource = source;
}
```

**优化效果**：
- **避免无效更新**：只在数据源变化时更新地形
- **减少计算开销**：避免重复的地形设置操作
- **提升响应性能**：减少不必要的渲染开销

## 7. 地形夸张度效果分析

### 7.1 不同地形的夸张度效果

| 地形类型 | 地形变化值 | 夸张度 | 视觉效果 |
|---------|-----------|--------|----------|
| 高山 | 0.1 | 1.63 | 山峰更加突出 |
| 丘陵 | 0.3 | 1.49 | 地形起伏明显 |
| 平原 | 0.7 | 1.21 | 轻微地形增强 |
| 海洋 | 0.9 | 1.07 | 接近真实高度 |

### 7.2 用户体验优化

**视觉层次**：
- **高夸张度**：突出重要地形特征
- **中等夸张度**：平衡视觉效果和真实性
- **低夸张度**：保持地形细节的准确性

**交互体验**：
- **流畅过渡**：夸张度变化平滑自然
- **响应迅速**：地形更新及时响应
- **性能稳定**：不影响地图交互性能

## 8. 技术实现细节

### 8.1 坐标系统转换

```typescript
// 地理坐标到墨卡托坐标的转换
const xy = MercatorCoordinate.fromLngLat(center);

// 墨卡托坐标到像素坐标的映射
const pixelX = xy.x * size;
const pixelY = xy.y * size;
```

**转换流程**：
1. **地理坐标**（经度、纬度）
2. **墨卡托坐标**（x, y）
3. **像素坐标**（pixelX, pixelY）

### 8.2 像素数据解析

```typescript
// 获取像素数据
const imageData = this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1);

// 解析 RGB 值
const r = imageData.data[0]; // 红色通道
const g = imageData.data[1]; // 绿色通道
const b = imageData.data[2]; // 蓝色通道（用作地形变化值）
const a = imageData.data[3]; // 透明度通道
```

**数据含义**：
- **蓝色通道**：表示地形变化程度
- **数值范围**：0-255，转换为 0-1
- **归一化**：`variance = b / 255`

## 9. 总结

WorkoutMap 的地形夸张度调整策略是一个复杂而精密的系统，具有以下特点：

### 9.1 核心优势

1. **动态适应**：根据地形特征自动调整夸张度
2. **性能优化**：使用缓存和条件更新提升性能
3. **用户体验**：提供流畅的视觉效果和交互体验
4. **数据驱动**：基于真实地形数据计算夸张度

### 9.2 技术特色

1. **智能采样**：从地形变化图像中智能提取地形信息
2. **多级插值**：支持缩放级别和地形变化的双重插值
3. **数据源适配**：针对不同数据源优化夸张度策略
4. **缓存机制**：高效的图像缓存和像素采样机制

### 9.3 应用价值

这个地形夸张度调整策略为运动轨迹地图提供了：
- **更好的地形感知**：帮助用户理解运动路线的地形特征
- **增强的视觉效果**：提供更加生动和直观的地图显示
- **优化的性能表现**：在保证视觉效果的同时维持高性能
- **灵活的自适应能力**：能够适应不同地形和缩放级别

通过这种精细的地形夸张度调整策略，WorkoutMap 为用户提供了既美观又实用的运动轨迹地图体验。 