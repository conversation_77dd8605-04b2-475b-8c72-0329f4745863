var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgCaloriesOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M15.8161 29.5909C14.0978 33.4095 13.9796 36.9656 15.4184 40.3227L16.8876 43.7509L13.6493 41.9004C6.74981 37.9579 6.427 28.92 11.2835 21.6351C13.6773 18.0446 19.3514 11.8978 19.9205 11.1297L19.9622 11.0669C20.481 10.0295 20.6208 8.49146 20.3293 6.45135L19.9791 4L22.6073 5.41752C26.7386 7.68585 29.4902 9.82512 30.8771 11.9055C32.5461 14.409 33.2963 17.019 33.2005 19.7478C33.9972 19.2822 34.4249 18.4151 34.4249 17.4638V14.446L36.5588 16.5799C38.6192 18.6403 40.2698 24.8367 39.8398 30.2121C39.3691 36.0961 35.7956 41.8302 32.0862 42.7575L27.3067 43.9524L30.9383 40.6234C33.2391 38.5144 33.4992 36.3031 32.072 32.4973C31.6106 31.2667 30.8786 30.029 29.8377 28.6269L29.4223 28.0795L29.1112 27.6826L27.4628 25.6419L27.1452 25.2304C26.7066 24.6482 26.4328 24.2128 26.2534 23.7886C25.8663 22.8737 25.7341 21.8125 25.8399 20.6061C24.4927 21.1092 23.2031 21.9736 21.9642 23.2125C19.7435 25.4332 19.3298 27.7083 20.2524 29.5534L21.1569 31.3624H19.1343C17.8589 31.3624 16.8686 30.6829 16.1165 29.4235L15.9933 29.2082L15.8161 29.5909ZM11.9638 36.6383C11.698 33.0799 12.7708 29.3734 15.1554 25.558L16.7146 23.0633L17.4701 26.082C17.8253 24.5092 18.7215 22.9196 20.1964 21.4447C22.3251 19.316 24.6756 18.0504 27.2278 17.6858L29.0802 17.4212L28.6154 19.2337C28.2116 20.8087 28.2116 22.0009 28.5558 22.8145C28.6553 23.0497 28.8942 23.4088 29.3171 23.9544L29.5933 24.3046L31.0735 26.1335L31.5577 26.7553C32.8773 28.4829 33.8119 30.0168 34.4129 31.6194C35.0708 33.3739 35.4274 34.9323 35.4496 36.3473C36.4325 34.531 37.1658 32.2879 37.3478 30.0127C37.6166 26.6521 36.9493 22.8962 36.0098 20.4859C35.1465 21.7431 33.6993 22.6057 31.783 22.6057H30.3074L30.55 21.1502C31.0117 18.3797 30.4768 15.812 28.797 13.2923C27.8426 11.8606 25.8999 10.2516 22.9784 8.50889C22.9644 9.93743 22.7093 11.1629 22.1983 12.1849L22.0163 12.5484C21.8099 12.9467 21.6212 13.2078 20.7593 14.1549L18.2566 16.8627C16.3183 18.9798 14.8531 20.7877 13.3635 23.0221C10.2244 27.7309 9.61244 33.1119 11.8458 36.7904L11.9989 37.0331L11.9638 36.6383Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgCaloriesOutline);
export default __webpack_public_path__ + "static/media/calories_outline.cb3560f4.svg";
export { ForwardRef as ReactComponent };