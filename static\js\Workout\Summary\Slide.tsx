import classNames from 'classnames';
import React, { ReactNode } from 'react';
import { makeStyles } from '@mui/styles';
import { usePrevious } from '../../helpers/usePrevious';

type SlideProps = {
  children: ReactNode;
  classes?: Record<string, string>;
  className?: string;
  duration?: number;
  onRowLengthChanged: (rowLength: number) => void;
};

const DEFAULT_DURATION = 800;
const useStyles = makeStyles(
  {
    root: ({ duration = DEFAULT_DURATION }: SlideProps) => ({
      position: 'relative',
      transition: `max-height ${duration}ms ease-out`,
      overflow: 'hidden',
    }),
    shadowChildren: { visibility: 'hidden', position: 'absolute' },
  },
  { name: 'Slide' },
);

function getRowLength(parent: HTMLDivElement | null): number {
  if (!parent) return 0;
  const firstRowTop = (parent.firstChild as HTMLDivElement).offsetTop;
  const children = Array.from(parent.children);

  let rowLength = 0;
  while ((children[rowLength] as HTMLDivElement)?.offsetTop === firstRowTop) {
    rowLength++;
  }
  return rowLength;
}
function Slide(props: SlideProps): React.ReactElement {
  const { children, className, duration = DEFAULT_DURATION, onRowLengthChanged } = props;
  const classes = useStyles(props);
  const prevChildren = usePrevious(children);
  const [isTransitioning, setIsTransitioning] = React.useState<boolean>(false);
  const prevChildrenCount = React.Children.count(prevChildren);
  const childrenCountDelta = React.Children.count(children) - React.Children.count(prevChildren);
  const shadowChildrenRef = React.useRef<HTMLDivElement>(null);
  const visibleChildrenRef = React.useRef<HTMLDivElement>(null);
  const [forceChildren, setForceChildren] = React.useState<ReactNode>(null);

  React.useEffect(() => {
    if (shadowChildrenRef.current && ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        const rowLength = getRowLength(shadowChildrenRef.current);
        onRowLengthChanged(rowLength);
      });
      resizeObserver.observe(shadowChildrenRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [shadowChildrenRef.current, onRowLengthChanged]);

  React.useEffect(() => {
    if (prevChildrenCount && childrenCountDelta) {
      /* start transition */
      setIsTransitioning(true);
      setTimeout(() => setIsTransitioning(false), duration);

      if (childrenCountDelta < 0) {
        setForceChildren(prevChildren);
        setTimeout(() => setForceChildren(null), duration);
      }
    }
  }, [prevChildrenCount, childrenCountDelta]);

  let wrapperMaxHeight: number | undefined = undefined;

  if (prevChildren && childrenCountDelta) {
    /* when children count changes, lets immediately set max height to current state */
    wrapperMaxHeight = visibleChildrenRef.current?.offsetHeight;
  }

  if (isTransitioning) {
    /* when transitioning it should have max height of target height */
    wrapperMaxHeight = shadowChildrenRef.current?.offsetHeight;
  }

  const visibleChildren =
    forceChildren || (prevChildren && childrenCountDelta < 0 ? prevChildren : children);

  return (
    <div className={classes.root} style={{ maxHeight: wrapperMaxHeight }}>
      <div
        className={classNames(className, classes.shadowChildren)}
        ref={shadowChildrenRef}
        aria-hidden="true"
      >
        {children}
      </div>
      <div className={className} ref={visibleChildrenRef}>
        {visibleChildren}
      </div>
    </div>
  );
}

export default Slide;
