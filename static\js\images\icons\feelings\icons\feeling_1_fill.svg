var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgFeeling1Fill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24 42C14.0588 42 6 33.9412 6 24C6 14.0588 14.0588 6 24 6C33.9412 6 42 14.0588 42 24C42 33.9412 33.9412 42 24 42ZM17.7278 30.9012C17.1677 30.7509 16.8356 30.175 16.9859 29.6149C18.8879 22.5283 29.1131 22.5283 31.0141 29.615C31.1644 30.1751 30.8322 30.751 30.2721 30.9012C29.712 31.0515 29.1361 30.7192 28.9859 30.1591C27.6413 25.1469 20.3594 25.1469 19.0141 30.1593C18.8638 30.7193 18.2879 31.0515 17.7278 30.9012ZM19.3288 18.0981C18.8098 19.3765 16.94 19.3399 16.4748 18.0457C16.3347 17.6559 15.9051 17.4534 15.5153 17.5936C15.1255 17.7337 14.9231 18.1632 15.0632 18.553C15.9942 21.1431 19.6822 21.2153 20.7178 18.6645L20.7509 18.5837C20.9079 18.2004 20.7245 17.7624 20.3412 17.6053C19.9579 17.4483 19.5199 17.6317 19.3629 18.015L19.3288 18.0981ZM28.6093 18.0457C29.0745 19.3399 30.9448 19.3765 31.4633 18.0981L31.4973 18.015C31.6544 17.6317 32.0924 17.4483 32.4757 17.6053C32.859 17.7624 33.0424 18.2004 32.8854 18.5837L32.8523 18.6643C31.8177 21.2153 28.1287 21.1432 27.1977 18.553C27.0576 18.1632 27.26 17.7337 27.6498 17.5936C28.0396 17.4534 28.4692 17.6559 28.6093 18.0457Z",
    fill: "#FF3333"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgFeeling1Fill);
export default __webpack_public_path__ + "static/media/feeling_1_fill.1e98d3b7.svg";
export { ForwardRef as ReactComponent };