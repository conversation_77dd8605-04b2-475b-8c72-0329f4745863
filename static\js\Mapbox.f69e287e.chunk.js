(this["webpackJsonpbig-screen"]=this["webpackJsonpbig-screen"]||[]).push([[2,10],{684:function(t){t.exports=JSON.parse('{"1":"TXT_VIBES_POOR","2":"TXT_VIBES_AVERAGE","3":"TXT_VIBES_GOOD","4":"TXT_VIBES_VERYGOOD","5":"TXT_VIBES_EXCELLENT"}')},695:function(t,e,a){"use strict";a.r(e),a.d(e,"convertOrigValueToSiValue",(function(){return K}));var i,r=a(29),n=a(11),o=a(682),s=a.n(o),l=a(693),u=a(0),c=a.n(u),h=a(665),f=a(684),m=a(207),p=a(60),d=["title","titleId"];function v(){return(v=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}).apply(this,arguments)}function g(t,e){if(null==t)return{};var a,i,r=function(t,e){if(null==t)return{};var a,i,r={},n=Object.keys(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||(r[a]=t[a]);return r}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function b(t,e){var a=t.title,r=t.titleId,n=g(t,d);return u.createElement("svg",v({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":r},n),a?u.createElement("title",{id:r},a):null,i||(i=u.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 42C14.0588 42 6 33.9412 6 24C6 14.0588 14.0588 6 24 6C33.9412 6 42 14.0588 42 24C42 33.9412 33.9412 42 24 42ZM17.7278 30.9012C17.1677 30.7509 16.8356 30.175 16.9859 29.6149C18.8879 22.5283 29.1131 22.5283 31.0141 29.615C31.1644 30.1751 30.8322 30.751 30.2721 30.9012C29.712 31.0515 29.1361 30.7192 28.9859 30.1591C27.6413 25.1469 20.3594 25.1469 19.0141 30.1593C18.8638 30.7193 18.2879 31.0515 17.7278 30.9012ZM19.3288 18.0981C18.8098 19.3765 16.94 19.3399 16.4748 18.0457C16.3347 17.6559 15.9051 17.4534 15.5153 17.5936C15.1255 17.7337 14.9231 18.1632 15.0632 18.553C15.9942 21.1431 19.6822 21.2153 20.7178 18.6645L20.7509 18.5837C20.9079 18.2004 20.7245 17.7624 20.3412 17.6053C19.9579 17.4483 19.5199 17.6317 19.3629 18.015L19.3288 18.0981ZM28.6093 18.0457C29.0745 19.3399 30.9448 19.3765 31.4633 18.0981L31.4973 18.015C31.6544 17.6317 32.0924 17.4483 32.4757 17.6053C32.859 17.7624 33.0424 18.2004 32.8854 18.5837L32.8523 18.6643C31.8177 21.2153 28.1287 21.1432 27.1977 18.553C27.0576 18.1632 27.26 17.7337 27.6498 17.5936C28.0396 17.4534 28.4692 17.6559 28.6093 18.0457Z",fill:"#FF3333"})))}var y,x=u.forwardRef(b),C=(a.p,["title","titleId"]);function O(){return(O=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}).apply(this,arguments)}function w(t,e){if(null==t)return{};var a,i,r=function(t,e){if(null==t)return{};var a,i,r={},n=Object.keys(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||(r[a]=t[a]);return r}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function P(t,e){var a=t.title,i=t.titleId,r=w(t,C);return u.createElement("svg",O({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":i},r),a?u.createElement("title",{id:i},a):null,y||(y=u.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 24C6 33.9415 14.0592 42 24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24ZM16.5 19.65C16.5 20.3807 17.1044 20.9843 17.85 20.9843C18.5956 20.9843 19.2 20.3807 19.2 19.65V18.1483C19.2 17.4036 18.5956 16.8 17.85 16.8C17.1044 16.8 16.5 17.4036 16.5 18.1483V19.65ZM28.8 19.65V18.1483C28.8 17.4036 29.4044 16.8 30.15 16.8C30.8956 16.8 31.5 17.4036 31.5 18.1483V19.65C31.5 20.3807 30.8956 20.9843 30.15 21C29.4044 20.9843 28.8 20.3807 28.8 19.65ZM30.6 27C30.6 27.6627 30.1016 28.2 29.4867 28.2H18.5133C17.8984 28.2 17.4 27.6627 17.4 27C17.4 26.3373 17.8984 25.8 18.5133 25.8H29.4867C30.1016 25.8 30.6 26.3373 30.6 27Z",fill:"#FFAA00"})))}var M,S=u.forwardRef(P),k=(a.p,["title","titleId"]);function A(){return(A=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}).apply(this,arguments)}function I(t,e){if(null==t)return{};var a,i,r=function(t,e){if(null==t)return{};var a,i,r={},n=Object.keys(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||(r[a]=t[a]);return r}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function E(t,e){var a=t.title,i=t.titleId,r=I(t,k);return u.createElement("svg",A({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":i},r),a?u.createElement("title",{id:i},a):null,M||(M=u.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24C6 33.9415 14.0592 42 24.0004 42ZM32.5978 26.7468C32.9309 27.2215 32.8161 27.8764 32.3413 28.2094C27.3504 31.711 20.6713 31.711 15.6804 28.2094C15.2057 27.8764 15.0908 27.2215 15.4239 26.7468C15.7569 26.2721 16.4118 26.1573 16.8865 26.4903C21.1535 29.484 26.8682 29.484 31.1352 26.4903C31.61 26.1573 32.2648 26.2721 32.5978 26.7468ZM28.8 19.65V18.1483C28.8 17.4036 29.4044 16.8 30.15 16.8C30.8956 16.8 31.5 17.4036 31.5 18.1483V19.65C31.5 20.3807 30.8956 20.9843 30.15 21C29.4044 20.9843 28.8 20.3807 28.8 19.65ZM16.5 19.65C16.5 20.3807 17.1044 20.9843 17.85 20.9843C18.5956 20.9843 19.2 20.3807 19.2 19.65V18.1483C19.2 17.4036 18.5956 16.8 17.85 16.8C17.1044 16.8 16.5 17.4036 16.5 18.1483V19.65Z",fill:"#FFDD33"})))}var j,T=u.forwardRef(E),L=(a.p,["title","titleId"]);function R(){return(R=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}).apply(this,arguments)}function D(t,e){if(null==t)return{};var a,i,r=function(t,e){if(null==t)return{};var a,i,r={},n=Object.keys(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||(r[a]=t[a]);return r}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function F(t,e){var a=t.title,i=t.titleId,r=D(t,L);return u.createElement("svg",R({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":i},r),a?u.createElement("title",{id:i},a):null,j||(j=u.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24C6 33.9415 14.0592 42 24.0004 42ZM28.8 18.1483V19.65C28.8 20.3807 29.4044 20.9843 30.15 21C30.8956 20.9843 31.5 20.3807 31.5 19.65V18.1483C31.5 17.4036 30.8956 16.8 30.15 16.8C29.4044 16.8 28.8 17.4036 28.8 18.1483ZM17.85 20.9843C17.1044 20.9843 16.5 20.3807 16.5 19.65V18.1483C16.5 17.4036 17.1044 16.8 17.85 16.8C18.5956 16.8 19.2 17.4036 19.2 18.1483V19.65C19.2 20.3807 18.5956 20.9843 17.85 20.9843ZM32.8661 25.5551C33.4093 25.7581 33.6851 26.3631 33.482 26.9063C30.2489 35.5568 17.999 35.5568 14.7667 26.9062C14.5637 26.363 14.8396 25.758 15.3828 25.5551C15.926 25.3521 16.5309 25.6279 16.7339 26.1712C19.2861 33.0019 28.9619 33.0019 31.5149 26.1711C31.718 25.6279 32.3229 25.3521 32.8661 25.5551Z",fill:"#ADE539"})))}var _,z=u.forwardRef(F),B=(a.p,["title","titleId"]);function V(){return(V=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}).apply(this,arguments)}function N(t,e){if(null==t)return{};var a,i,r=function(t,e){if(null==t)return{};var a,i,r={},n=Object.keys(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||(r[a]=t[a]);return r}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(i=0;i<n.length;i++)a=n[i],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function G(t,e){var a=t.title,i=t.titleId,r=N(t,B);return u.createElement("svg",V({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":i},r),a?u.createElement("title",{id:i},a):null,_||(_=u.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 24C6 33.9415 14.0592 42 24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24ZM31.4099 19.8386C30.9487 18.5321 29.1114 18.4955 28.5971 19.7851L28.5634 19.8689C28.4087 20.2532 27.9718 20.4393 27.5876 20.2846C27.2033 20.13 27.0172 19.6931 27.1718 19.3089L27.2047 19.2273C28.2311 16.6535 31.902 16.7266 32.8244 19.3392C32.9623 19.7298 32.7574 20.1582 32.3669 20.2961C31.9763 20.434 31.5479 20.2292 31.4099 19.8386ZM16.5709 19.7851C17.0847 18.4956 18.9226 18.5322 19.3838 19.8386C19.5217 20.2292 19.9501 20.434 20.3407 20.2961C20.7313 20.1582 20.9361 19.7298 20.7982 19.3392C19.8758 16.7266 16.2039 16.6535 15.1784 19.2275L15.1457 19.3089C14.991 19.6931 15.1772 20.13 15.5614 20.2846C15.9457 20.4393 16.3826 20.2532 16.5372 19.8689L16.5709 19.7851ZM15.8336 26.8655C15.7754 26.5921 15.9979 26.3346 16.2785 26.3346H31.6807C31.9612 26.3346 32.1837 26.5921 32.1255 26.8655C31.3293 30.6321 27.9843 33.4705 23.9796 33.4705C19.974 33.4705 16.629 30.6321 15.8336 26.8655Z",fill:"#11DD55"})))}var U=u.forwardRef(G),Z=(a.p,{FEELING_1_FILL:x,FEELING_2_FILL:S,FEELING_3_FILL:T,FEELING_4_FILL:z,FEELING_5_FILL:U}),H=a(124),X=a(677),Y=a(685),W=a(686),q=a(3),J=f,Q=s.a,K=function(t,e){var a=e;switch(t){case"HeartRate":case"AvgHeartrate":case"MaxHeartrate":var i=e;if("number"===typeof i){var r=Q.com.suunto.sim.formatting,n=r.Units.BPM.convert(i,r.Units.HZ);n instanceof r.ConversionSuccess&&(a=n.value)}break;case"Energy":var o=e;if("number"===typeof o){var s=Q.com.suunto.sim.formatting,l=s.Units.KCAL.convert(o,s.Units.J);l instanceof s.ConversionSuccess&&(a=l.value)}}return a},$=function(t,e,a,i){var r,n=Q.com.suunto.sim.formatting,o=l[t];switch(o){case l.Feeling:var s=Z["FEELING_".concat(e,"_FILL")];s&&(r=new n.FormatSuccess({},Object(q.jsx)(s,{title:a(J[e])}),{}));break;default:var u=[o.ValueFormat,o.ValueFormatStyle].join(""),c=new n.FormattingOptions(i===H.a.imperial?n.MeasurementSystem.IMPERIAL:n.MeasurementSystem.METRIC,!1);try{r=Q.com.suunto.sim.formatting.formatWithStyle(u,e,c)}catch(h){console.error(h)}}return r};e.default=function(t){var e=Object(h.a)([p.a.PHRASES,p.a.UNITS,p.a.TRANSLATIONS]).t,a=t.value,i=t.name,o=t.small,s=void 0!==o&&o,l=t.classes,u=c.a.useContext(H.b),f=Object(n.a)(u,1)[0],d=c.a.useContext(m.a),v=K(i,a),g={classes:l,small:s,name:i};if(!Object(X.a)(i,v))return d?Object(q.jsx)(W.a,Object(r.a)(Object(r.a)({},g),{},{value:a||"unknown"})):null;var b=$(i,v,e,f);return b instanceof Q.com.suunto.sim.formatting.FormatSuccess?Object(q.jsx)(W.a,Object(r.a)(Object(r.a)({},g),{},{Icon:Y.a["ICON_".concat(b.icon.name)],value:b.value,unit:b.unit.id&&e("".concat(p.a.UNITS,":").concat(b.unit.id),{defaultValue:""})})):Object(q.jsx)(W.a,Object(r.a)(Object(r.a)({},g),{},{value:v}))}},788:function(t,e,a){"use strict";a.r(e);var i=a(11),r=a(20),n=a(0),o=a.n(n),s=a(161),l=a.n(s),u=a(665),c=a(124),h=a(192),f=a(214),m=a(676),p=a(692),d=a(53),v=a(54),g=a(682),b=a.n(g),y=a(713),x=(a(683),a(273)),C=a(194),O=a(65),w=function(t,e,a){var r=Object(i.a)(a,2),n=r[0],o=r[1],s=Object(i.a)(e,2),l=s[0];return(t-l)/(s[1]-l)*(o-n)+n},P=a(268),M=a(751),S=a(750);function k(t,e,a,i){return t.concat(e,a,i)}function A(t,e){var a=Object(i.a)(e,4),r=a[0],n=a[1],o=a[2],s=a[3];return[r*t[0]+n*t[4]+o*t[8]+s*t[12],r*t[1]+n*t[5]+o*t[9]+s*t[13],r*t[2]+n*t[6]+o*t[10]+s*t[14],r*t[3]+n*t[7]+o*t[11]+s*t[15]]}var I=function(){function t(e,a,i){Object(d.a)(this,t),this.program=void 0,this.buffer=void 0,this.distBuffer=void 0,this.uMatrix=void 0,this.uCamera=void 0,this.uOffset=void 0,this.uThickness=void 0,this.uAlpha=void 0,this.uDist=void 0,this.aPos=void 0,this.aDist=void 0,this.count=0;var r=e.createShader(e.VERTEX_SHADER),n=e.createShader(e.FRAGMENT_SHADER);if(!r||!n)throw new Error;if(e.shaderSource(r,a),e.compileShader(r),!e.getShaderParameter(r,e.COMPILE_STATUS))throw new Error(""+e.getShaderInfoLog(r));if(e.shaderSource(n,i),e.compileShader(n),!e.getShaderParameter(n,e.COMPILE_STATUS))throw new Error(""+e.getShaderInfoLog(n));if(this.program=e.createProgram(),!this.program)throw new Error;if(e.attachShader(this.program,r),e.attachShader(this.program,n),e.linkProgram(this.program),!e.getProgramParameter(this.program,e.LINK_STATUS))throw new Error(""+e.getProgramInfoLog(this.program));e.validateProgram(this.program),this.uMatrix=e.getUniformLocation(this.program,"uMatrix"),this.uCamera=e.getUniformLocation(this.program,"uCamera"),this.uOffset=e.getUniformLocation(this.program,"uOffset"),this.uThickness=e.getUniformLocation(this.program,"uThickness"),this.uAlpha=e.getUniformLocation(this.program,"uAlpha"),this.uDist=e.getUniformLocation(this.program,"uDist"),this.aPos=e.getAttribLocation(this.program,"aPos"),this.aDist=e.getAttribLocation(this.program,"aDist"),this.buffer=e.createBuffer(),this.distBuffer=e.createBuffer()}return Object(v.a)(t,[{key:"update",value:function(t,e,a){t.bindBuffer(t.ARRAY_BUFFER,this.buffer),t.bufferData(t.ARRAY_BUFFER,e,t.STATIC_DRAW),this.count=a}},{key:"updateDist",value:function(t,e){t.bindBuffer(t.ARRAY_BUFFER,this.distBuffer),t.bufferData(t.ARRAY_BUFFER,e,t.STATIC_DRAW)}}]),t}(),E=function(t){Object(M.a)(a,t);var e=Object(S.a)(a);function a(t){Object(d.a)(this,a);return e.call(this,t,"\n    precision highp float;\n\n    uniform mat4 uMatrix;\n    uniform float uThickness;\n    uniform vec4 uCamera;\n    uniform vec2 uOffset;\n    attribute vec4 aPos;\n    attribute float aDist;\n    varying float vItem;\n    varying float vAlpha;\n    varying float vDist;\n\n    const vec3 alt = vec3(0.0, 0.0, 0.0000002);\n\n    void main() {\n      vec3 pos = aPos.xyz;\n\n      float item = aPos.w;\n      float side = -1.0;\n\n      if(item < 3.0) {\n        item -= 2.0;\n      } else {\n        side = 1.0;\n        item -= 3.0;\n      }\n\n      vItem = item;\n      vDist = aDist;\n\n      pos += alt;\n      pos += normalize(uCamera.xyz - pos) * 0.000001;\n\n      vec4 xyPos = uMatrix * vec4(pos, 1.0);\n\n      xyPos += vec4(uOffset, 0.0, 0.0) * side * xyPos.w * uThickness;\n      vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), length(uCamera.xyz - pos) / uCamera.z);\n\n      gl_Position = xyPos;\n    }\n    ","\n    precision highp float;\n\n    uniform float uAlpha;\n    uniform float uDist;\n    varying float vItem;\n    varying float vAlpha;\n    varying float vDist;\n\n    void main() {\n      if(vDist > uDist) discard;\n      if(vItem == 1.0) {\n        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;\n      } else {\n        gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;\n      }\n    }\n    ")}return Object(v.a)(a,[{key:"render",value:function(t,e,a,i,r){var n=this.count;t.useProgram(this.program),t.uniformMatrix4fv(this.uMatrix,!1,e),t.uniform1f(this.uThickness,a),r&&t.uniform4fv(this.uCamera,r),t.enableVertexAttribArray(this.aPos),t.enableVertexAttribArray(this.aDist),t.bindBuffer(t.ARRAY_BUFFER,this.buffer),t.vertexAttribPointer(this.aPos,4,t.FLOAT,!1,0,0),t.bindBuffer(t.ARRAY_BUFFER,this.distBuffer),t.vertexAttribPointer(this.aDist,1,t.FLOAT,!1,0,0),t.uniform1f(this.uAlpha,1),t.uniform1f(this.uDist,i),t.uniform2f(this.uOffset,Math.SQRT2/2,Math.SQRT2/2),t.drawArrays(t.TRIANGLE_STRIP,0,2*n),t.uniform2f(this.uOffset,Math.SQRT2/2,-Math.SQRT2/2),t.drawArrays(t.TRIANGLE_STRIP,0,2*n),t.uniform2f(this.uOffset,1,0),t.drawArrays(t.TRIANGLE_STRIP,0,2*n),t.uniform2f(this.uOffset,0,1),t.drawArrays(t.TRIANGLE_STRIP,0,2*n),t.disable(t.DEPTH_TEST),t.uniform1f(this.uAlpha,.125),t.drawArrays(t.TRIANGLE_STRIP,0,2*n),t.enable(t.DEPTH_TEST)}}]),a}(I),j=function(t){Object(M.a)(a,t);var e=Object(S.a)(a);function a(t){Object(d.a)(this,a);return e.call(this,t,"\n    precision highp float;\n\n    uniform mat4 uMatrix;\n    uniform float uThickness;\n    uniform vec4 uCamera;\n    uniform vec2 uOffset;\n    attribute vec4 aPos;\n    attribute float aDist;\n\n    varying float vItem;\n    varying float vAlpha;\n    varying vec2 vDelta;\n    varying float vDist;\n\n    const vec3 alt = vec3(0.0, 0.0, 0.0000002);\n\n    void main() {\n      vec3 pos = aPos.xyz;\n\n      float item = aPos.w;\n      vec2 delta;\n\n      if(item < 3.0) {\n        item -= 2.0;\n        delta = vec2(-1.0, -1.0);\n      } else if(item < 4.0) {\n        item -= 3.0;\n        delta = vec2(-1.0, 1.0);\n      } else if(item < 5.0) {\n        item -= 4.0;\n        delta = vec2(1.0, -1.0);\n      } else {\n        item -= 5.0;\n        delta = vec2(1.0, 1.0);\n      }\n\n      vItem = item;\n      vDelta = delta;\n      vDist = aDist;\n\n      pos += alt;\n      pos += normalize(uCamera.xyz - pos) * 0.000001;\n\n      vec4 xyPos = uMatrix * vec4(pos, 1.0);\n      vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), length(uCamera.xyz - pos) / uCamera.z);\n\n      xyPos += vec4(uOffset * delta, 0.0, 0.0) * xyPos.w * uThickness;\n\n      gl_Position = xyPos;\n    }\n    ","\n    precision highp float;\n\n    uniform float uAlpha;\n    uniform float uDist;\n\n    varying float vItem;\n    varying float vAlpha;\n    varying float vDist;\n    varying vec2 vDelta;\n\n    void main() {\n      if(vDist > uDist) discard;\n      if(length(vDelta) > 1.0) discard;\n      if(vItem == 1.0) {\n        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;\n      } else {\n        gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;\n      }\n    }\n    ")}return Object(v.a)(a,[{key:"render",value:function(t,e,a,i,r){var n=this.count;t.useProgram(this.program),t.uniformMatrix4fv(this.uMatrix,!1,e),t.uniform1f(this.uThickness,a),r&&t.uniform4fv(this.uCamera,r),t.enableVertexAttribArray(this.aPos),t.enableVertexAttribArray(this.aDist),t.bindBuffer(t.ARRAY_BUFFER,this.buffer),t.vertexAttribPointer(this.aPos,4,t.FLOAT,!1,0,0),t.bindBuffer(t.ARRAY_BUFFER,this.distBuffer),t.vertexAttribPointer(this.aDist,1,t.FLOAT,!1,0,0),t.uniform1f(this.uAlpha,1),t.uniform1f(this.uDist,i),t.uniform2f(this.uOffset,1,1),t.drawArrays(t.TRIANGLE_STRIP,0,6*n)}}]),a}(I),T=function(){function t(e,a){Object(d.a)(this,t),this.pts=a,this.id=void 0,this.type="custom",this.renderingMode="3d",this.tick=0,this.renderedTick=0,this.position=0,this.progressed=!1,this.map=void 0,this.lineRenderer=void 0,this.pointRenderer=void 0,this.lineData=void 0,this.pointData=void 0,this.lineDistData=void 0,this.pointDistData=void 0,this.xyzMin=new s.MercatorCoordinate(0,0),this.xyzMax=this.xyzMin,this.id=e,this.pts=a}return Object(v.a)(t,[{key:"onAdd",value:function(t,e){this.map=t,this.lineData=new Float32Array(2*this.pts.length*4),this.pointData=new Float32Array(6*this.pts.length*4),this.lineDistData=new Float32Array(2*this.pts.length),this.pointDistData=new Float32Array(6*this.pts.length);var a,r=1/0,n=-1/0,o=1/0,l=-1/0,u=Object(p.a)(this.pts);try{for(u.s();!(a=u.n()).done;){var c=a.value,h=Object(i.a)(c,2),f=h[0],m=h[1];m<o&&(o=m),m>l&&(l=m),f<r&&(r=f),f>n&&(n=f)}}catch(d){u.e(d)}finally{u.f()}this.xyzMin=s.MercatorCoordinate.fromLngLat({lng:o,lat:r}),this.xyzMax=s.MercatorCoordinate.fromLngLat({lng:l,lat:n}),this.lineRenderer=new E(e),this.pointRenderer=new j(e),this.updateData(!0)}},{key:"updateData",value:function(t){var e=this.lineData,a=this.lineDistData,i=this.pointData,r=this.pointDistData,n=this.map;if(e&&a&&i&&r&&n){var o=this.pts.length,l=++this.tick;o<100&&(t=!0);for(var u=[],c=t?1:30,h=n.getCenter(),f=0;f<c;++f){for(var m=1/0,p=-1/0,d=1/0,v=-1/0,g=~~(o*f/c),b=~~(o*(f+1)/c),y=g;y<b;++y){var x=this.pts[y];x[0]<m&&(m=x[0]),x[0]>p&&(p=x[0]),x[1]<d&&(d=x[1]),x[1]>v&&(v=x[1])}var C=h.lat-(m+p)/2,O=h.lng-(d+v)/2,w=C*C+O*O;u.push({dist:w,first:g,last:b})}u.sort((function(t,e){return t.dist-e.dist}));for(var P=!1,M=0;M<3;++M){var S=0;if(1==M){if(t)break;S=1+l%4}else 2==M&&(S=5+l%(c-5));for(var k=u[S],A=k.first,I=k.last,E=8*A,j=24*A,T=A;T<I;++T){var L=this.pts[T],R={lng:L[1],lat:L[0]},D=n.queryTerrainElevation(R,{exaggerated:!0});t&&(!D||D<=0)&&(D=1);for(var F=s.MercatorCoordinate.fromLngLat(R,D||0),_=0;_<2;++_)a[2*T+_]=L[4],r[6*T+_]=L[4],e[E++]=F.x-this.xyzMin.x,e[E++]=F.y-this.xyzMin.y,"number"==typeof D?e[E++]=F.z||0:++E,e[E++]=L[2]/255+2+_;i[j++]=F.x-this.xyzMin.x,i[j++]=F.y-this.xyzMin.y,"number"==typeof D?(i[j]!=(F.z||0)&&(P=!0),i[j++]=F.z||0):++j,i[j++]=L[2]/255+2;for(var z=0;z<4;++z)r[6*T+2+z]=L[4],i[j++]=F.x-this.xyzMin.x,i[j++]=F.y-this.xyzMin.y,"number"==typeof D?i[j++]=F.z||0:++j,i[j++]=L[2]/255+z;i[j++]=F.x-this.xyzMin.x,i[j++]=F.y-this.xyzMin.y,"number"==typeof D?i[j++]=F.z||0:++j,i[j++]=L[2]/255+5}}n&&P&&n.triggerRepaint()}}},{key:"render",value:function(t,e){var a,i;a=e,i=k([1,0,0,0],[0,1,0,0],[0,0,1,0],[this.xyzMin.x,this.xyzMin.y,0,1]),e=k(A(a,i.slice(0,4)),A(a,i.slice(4,8)),A(a,i.slice(8,12)),A(a,i.slice(12,16)));for(var r=1/Math.max.apply(null,e.map(Math.abs)),n=0;n<16;++n)e[n]*=r;var o,s=this.map&&this.map.getZoom(),l=this.map&&this.map.getFreeCameraOptions().position,u=this.map&&Math.tan(this.map.getPitch()/180*Math.PI);l&&(o=[l.x-this.xyzMin.x,l.y-this.xyzMin.y,l.z||0,u||0]);var c=s?w(s,[14,16],[1/256,1/128]):.01;if(c<.005&&(c=.005),c>.01&&(c=.01),this.lineData&&this.lineDistData&&this.pointData&&this.pointDistData&&this.lineRenderer&&this.pointRenderer){this.tick>this.renderedTick&&(this.lineRenderer.update(t,this.lineData,this.pts.length),this.lineRenderer.updateDist(t,this.lineDistData),this.pointRenderer.update(t,this.pointData,this.pts.length),this.pointRenderer.updateDist(t,this.pointDistData),this.renderedTick=this.tick);var h=this.progressed?this.position:this.pts[this.pts.length-1][4];this.lineRenderer.render(t,e,c,h,o),this.pointRenderer.render(t,e,c,h,o)}}},{key:"setPosition",value:function(t){this.position=t,t&&(this.progressed=!0)}}]),t}(),L=a(696),R=a(695),D=a.p+"static/media/dem_std_zoom_4_512.7908dc22.png",F=b.a,_="route",z=[[61.8237,24.7277,64.6913,29.7954],[65.946,23.9773,68.5443,28.3443],[64.6598,25.5232,66.3274,29.3682],[60.9131,21.8551,61.907,28.1602],[61.7958,21.8698,63.197,24.8014],[63.1637,22.7243,63.6386,29.9574],[67.313,23.9249,67.8967,29.3682],[68.5319,26.1015,69.6914,28.3149],[63.912,24.1827,64.2597,30.1195],[66.2919,28.2412,68.0844,28.8747],[63.1371,23.6376,64.009,24.8309],[62.2176,29.6775,63.3692,30.4583],[61.347,28.0129,61.9348,29.0883],[68.2023,23.2546,68.6008,24.6394],[69.0378,28.1197,69.7259,28.6721],[62.5386,30.3994,63.1571,30.9886],[69.6863,26.5581,69.8772,28.1491],[68.5336,25.3465,68.8122,26.1567],[68.8866,21.1775,69.2325,21.6636],[68.7455,21.5089,69.0004,21.9582],[68.5147,22.1203,68.6545,22.9526],[69.3798,28.6279,69.6709,29.0183],[68.4241,22.7832,68.6076,23.262],[68.6411,21.7888,68.8388,22.1866]],B=function(){function t(e,a,i){var r=this,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0,l=arguments.length>6?arguments[6]:void 0;Object(d.a)(this,t),this.position=n,this.measurementSystem=o,this.classes=s,this.readyCallback=l,this.point=void 0,this.pts=[],this.mapbox=void 0,this.graph=null,this.minMaxMarkers=[],this.workout=void 0,this.styleLoadPromise=Promise.resolve(void 0),this.idlePromise=Promise.resolve(void 0),this.locationCenterMarker=void 0,this.cameraPath=void 0,this.playing=!1,this.elevationImage=new Image,this.elevationContext=void 0,this.lineLayer=void 0,this.demSource="",this.style=void 0,this.updateDataIntervalHandle=void 0,this.div=void 0,this.workout=a,this.cameraPath=a.cameraPath;var u=P.a.getRoute("",a,{min:"",max:""});this.pts=u.pts,this.div=e,this.mapbox=this.setStyle(i);var c=this.elevationImage;c.onload=function(){var t=document.createElement("canvas");t.width=c.width,t.height=c.height;var e=t.getContext("2d");e&&(e.drawImage(c,0,0),r.elevationContext=e),r.styleLoadPromise.then((function(){return r.updateTerrain()}))},c.src=D}return Object(v.a)(t,[{key:"createMap",value:function(t,e){var a=this,i=this.workout,r=new s.Map({container:t,style:e,center:[i.workout.centerPosition.x,i.workout.centerPosition.y],pitch:45,bearing:0,zoom:12,interactive:!0,attributionControl:!1,logoPosition:"top-right"}),n=i.getLocation(),o=n?n.locationPoints:[];if(o.length>1){var l=C.a.getBounds(o),u=l.lonMax-l.lonMin,c=l.latMax-l.latMin,h=1/6;r.fitBounds([[l.lonMin-u*h,l.latMin-c*h],[l.lonMax+u*h,l.latMax+c*h]])}return this.styleLoadPromise=new Promise((function(t){return r.on("style.load",t)})),this.idlePromise=new Promise((function(t){return a.styleLoadPromise.then((function(){return r.once("idle",t)}))})),r.on("idle",(function(){return a.updateTerrain()})),r.addControl(new s.AttributionControl,"top-right"),this.styleLoadPromise.then((function(){r.addSource("mml-dem",{type:"raster-dem",tiles:["https://tileserver-test.sports-tracker.com/mml-dem/{z}/{x}/{y}.png"],tileSize:512,maxzoom:14,encoding:"mapbox"}),r.addSource("mapbox-dem",{type:"raster-dem",url:"mapbox://mapbox.mapbox-terrain-dem-v1",tileSize:512,maxzoom:14}),a.updateTerrain(),a.updateDataIntervalHandle=window.setInterval((function(){a.lineLayer&&a.lineLayer.updateData()}),100),a.moveProgressMarker([i.workout.centerPosition.x,i.workout.centerPosition.y,0])})),r}},{key:"destructor",value:function(){window.clearInterval(this.updateDataIntervalHandle)}},{key:"updateTerrain",value:function(){var t=this.mapbox.getCenter(),e=1.35,a="mapbox-dem";if(this.mapbox.getZoom()>=10){var i,r=Object(p.a)(z);try{for(r.s();!(i=r.n()).done;){var n=i.value;if(t.lat>=n[0]&&t.lat<=n[2]&&t.lng>=n[1]&&t.lng<=n[3]){a="mml-dem";break}}}catch(u){r.e(u)}finally{r.f()}}if(this.elevationContext){var o=this.elevationImage.width,l=s.MercatorCoordinate.fromLngLat(t);e=1+.7*(1-this.elevationContext.getImageData(l.x*o,l.y*o,1,1).data[2]/255)}a!=this.demSource&&(this.mapbox.setTerrain({source:a,exaggeration:["interpolate",["linear"],["zoom"],8,e+0,12,e]}),this.demSource=a)}},{key:"setClasses",value:function(t){this.classes=t}},{key:"setStyle",value:function(t){if((t=t!=L.a.satellite&&t?"https://maps.suunto.com/api/style/"+t:"mapbox://styles/asdigital/ckw3gar2y19y614p40q2bz53a")==this.style)return this.mapbox;this.style=t,this.demSource="",this.point=void 0,this.mapbox&&this.mapbox.remove();var e=this.createMap(this.div,t);return this.mapbox=e,e}},{key:"setGraph",value:function(t,e){var a,i=this;this.graph=t,null===(a=this.locationCenterMarker)||void 0===a||a.remove(),this.locationCenterMarker=void 0,this.minMaxMarkers.forEach((function(t){return t.marker.remove()})),this.idlePromise.then((function(){var a,r=P.a.getRoute(t,i.workout,e),n=r.poi,o=r.pts,s=Object(p.a)(n);try{for(s.s();!(a=s.n()).done;){var l=a.value;i.addMarker(l)}}catch(v){s.e(v)}finally{s.f()}i.pts=o;var u=new T(_,o);i.mapbox.getLayer(_)&&i.mapbox.removeLayer(_),i.lineLayer=u,i.moveProgressMarker(i.getProgressMarkerPositionByLineString());var c,h=i.mapbox.getStyle().layers;if(h){var f,m=Object(p.a)(h);try{for(m.s();!(f=m.n()).done;){var d=f.value;if("symbol"==d.type){c=d.id;break}}}catch(v){m.e(v)}finally{m.f()}}i.mapbox.addLayer(u,c),i.mapbox.once("idle",(function(){u.updateData(!0),i.readyCallback()}))}))}},{key:"setMeasurementSystem",value:function(t){this.measurementSystem=t}},{key:"formatValue",value:function(t){var e;if(!this.graph)return t.toString();var a=null===(e=y[this.graph])||void 0===e?void 0:e.DefaultStyle;if(a){var i=F.com.suunto.sim.formatting,r=new i.FormattingOptions(this.measurementSystem===c.a.imperial?i.MeasurementSystem.IMPERIAL:i.MeasurementSystem.METRIC,!1);"number"==typeof t&&(t=Object(R.convertOrigValueToSiValue)(this.graph,t));var n=[this.graph,a].join(""),o=F.com.suunto.sim.formatting.formatWithStyle(n,t,r);if(o instanceof i.FormatSuccess)return o.value.toString()}return t.toString()}},{key:"addMarker",value:function(t){var e=this.classes,a=document.createElement("div");a.className=e.labelContainer;var i=document.createElement("div");i.className=e.labelWrapper,a.appendChild(i);var r=document.createElement("div");r.className=e.labelStick,i.appendChild(r);var n=document.createElement("div");n.className=e.label,i.appendChild(n);var o=document.createElement("label");o.className=e.labelTitle,o.innerText=t.label,n.appendChild(o);var l=document.createElement("div");l.className=e.labelValue,l.innerText=this.formatValue(t.value),n.appendChild(l);var u=new s.Marker(a).setLngLat([t.lon,t.lat]).addTo(this.mapbox);return this.minMaxMarkers.push({marker:u,div:i,position:t.position}),u}},{key:"setPlaying",value:function(t){this.playing=t}},{key:"getProgressMarkerPositionByLineString",value:function(){var t,e=this.pts,a=e[e.length-1],i=Math.min(1,x.b*this.position),r=w(i,[0,1],[0,a[4]]),n=e[0],o=Object(p.a)(e);try{for(o.s();!(t=o.n()).done;){var s=t.value,l=s[4],u=n[4];if(l>=r){var c=(r-u)/(l-u||1);return[n[1]+(s[1]-n[1])*c,n[0]+(s[0]-n[0])*c,r]}n=s}}catch(h){o.e(h)}finally{o.f()}return[a[1],a[0],r]}},{key:"moveProgressMarker",value:function(t){this.point?this.point.setLngLat([t[0],t[1]]):this.point=(new s.Marker).setLngLat([t[0],t[1]]).addTo(this.mapbox);var e,a=Object(p.a)(this.minMaxMarkers);try{for(a.s();!(e=a.n()).done;){var i,r=e.value,n=r.div,o=r.position;n.style.opacity=o<=t[2]||!(null===(i=this.lineLayer)||void 0===i?void 0:i.progressed)?"1":"0"}}catch(l){a.e(l)}finally{a.f()}this.lineLayer&&this.lineLayer.setPosition(t[2])}},{key:"calculateNewPosition",value:function(){var t=this.cameraPath,e=t.cameraPath,a=this.position,i=this.getProgressMarkerPositionByLineString(),r=this.mapbox.queryTerrainElevation({lng:i[0],lat:i[1]},{exaggerated:!0}),n=t.cameraEasing(a),o=this.mapbox.getCanvas(),s=w(o.width/o.height,[1.25,.5],[t.lookAtPointCenteringBehaviour.getValue(n),1]);s<0&&(s=0),s>1&&(s=1);var l=t.lookAtCurve.getPoint(n),u=e.getPoint(n),c=u.subtract(l).normalize(),h=180+Object(O.b)(Math.atan2(c.x,c.y));if(null!==r){var f=t.narrowLookAtCurve.getPoint(n),m=u.subtract(f).normalize(),p=180+Object(O.b)(Math.atan2(m.x,m.y));p>h+90&&(p-=360),p<h-90&&(p+=360),h=w(s,[0,1],[h,p])}var d=t.getLatLonAltFromPseudoCartesianCoordinates(u);return{bearing:h,pitch:Object(O.b)(Math.atan(Math.sqrt(c.x*c.x+c.y*c.y)/c.z)),cameraPosition:d,markerCoordinates:i}}},{key:"isCameraAtCurrentPosition",value:function(){var t=this.calculateNewPosition().cameraPosition;if(!t)return!1;var e=s.MercatorCoordinate.fromLngLat({lng:t.lon,lat:t.lat},t.alt),a=this.mapbox.getFreeCameraOptions().position;if(!a)return!1;var i=e.x-a.x,r=e.y-a.y;return i*i+r*r<Math.pow(2,-14-this.mapbox.getZoom())}},{key:"setPosition",value:function(t){this.position=t;var e=this.calculateNewPosition(),a=e.cameraPosition,i=e.bearing,r=e.pitch,n=e.markerCoordinates;if(a&&i&&r&&n){this.moveProgressMarker(n);var o=this.mapbox.getFreeCameraOptions();o.position=s.MercatorCoordinate.fromLngLat({lng:a.lon,lat:a.lat},a.alt),o.setPitchBearing(r,i),this.mapbox.setFreeCameraOptions(o,{automatic:!0})}}}]),t}(),V=a(551),N=a(661),G=a(667),U=a.n(G),Z=a(3),H=Object(N.a)({root:{transition:["opacity","filter"].map((function(t){return"".concat(t," ").concat(1200,"ms ease-in")})).join(","),zIndex:10,opacity:1,filter:"blur(0px)",display:"none","&$isFading":{opacity:0,filter:"blur(64px)"},"&$isPresent":{display:"block"}},isFading:{},isPresent:{}},{name:"CrossFadeOverlay"});var X=function(t){var e,a=t.isMapInitialized,n=t.isPlaying,s=t.map,l=t.setIsScreenshotTaken,u=H(t),c=o.a.createRef(),h=o.a.useState(!1),f=Object(i.a)(h,2),m=f[0],p=f[1],d=o.a.useState(!1),v=Object(i.a)(d,2),g=v[0],b=v[1];return o.a.useEffect((function(){a&&n&&c.current&&(s.isCameraAtCurrentPosition()?l(!0):function(t,e){return new Promise((function(a,i){t.once("render",(function(){var r=t.getCanvas();e.width=r.width,e.height=r.height;var n=e.getContext("2d");n?(n.drawImage(r,0,0,r.width,r.height,0,0,e.width,e.height),a()):i()})),t.setBearing(t.getBearing())}))}(s.mapbox,c.current).then((function(){p(!0),requestAnimationFrame((function(){b(!0),l(!0),setTimeout((function(){p(!1),b(!1)}),1200)}))}))),n||l(!1)}),[a,n]),Object(Z.jsx)("canvas",{ref:c,className:U()(u.root,(e={},Object(r.a)(e,u.isFading,g),Object(r.a)(e,u.isPresent,m),e))})},Y=Object(N.a)((function(t){return{labelContainer:{top:"-80px !important"},labelWrapper:{transition:"opacity 300ms ease-in",opacity:1},labelStick:{position:"absolute",top:4,width:1,height:76,backgroundColor:t.palette.background.default},label:{position:"absolute",borderRadius:3,borderBottomLeftRadius:0,padding:3,color:t.palette.text.primary,backgroundColor:t.palette.background.default},labelValue:{fontSize:"1.4rem",lineHeight:1,fontWeight:"bold"},labelTitle:{whiteSpace:"nowrap",fontWeight:"bold",fontSize:"0.8rem",lineHeight:9/8,letterSpacing:.5,display:"block",textTransform:"uppercase"}}})),W=Object(N.a)((function(t){return Object(r.a)({root:{zIndex:0,height:"fill-available",width:"100%",fallbacks:[{height:"100vh"}],"& .mapboxgl-marker":{color:h.a.dark,fontSize:"4rem"},"&.mapboxgl-map":{position:"fixed"},"& .mapboxgl-ctrl-attrib.mapboxgl-compact.mapboxgl-compact-show":{maxWidth:150,paddingTop:"9rem"},"& .mapboxgl-ctrl-attrib.mapboxgl-compact.mapboxgl-compact-show .mapboxgl-ctrl-attrib-inner a":{whiteSpace:"nowrap"}},crossFade:{position:"absolute",width:"100%",height:"100%"}},t.breakpoints.up(m.a),{root:{height:"100vh","&.mapboxgl-map":{position:"relative"}}})}),{name:"Mapbox"});l.a.accessToken="pk.eyJ1IjoiYXNkaWdpdGFsIiwiYSI6ImNrcTloMGg5ejAwbGMyb3B0dDRncHBlYTIifQ.XxD8GnrgEGrzkc7TiRUE-A";var q=function(t){return{labelContainer:t.labelContainer,labelWrapper:t.labelWrapper,labelStick:t.labelStick,label:t.label,labelValue:t.labelValue,labelTitle:t.labelTitle}};e.default=function(t){var e=t.workout,a=t.graph,r=t.style,s=o.a.useRef(null),l=o.a.useState(null),h=Object(i.a)(l,2),m=h[0],p=h[1],d=o.a.useState(0),v=Object(i.a)(d,2),g=v[0],b=v[1],y=o.a.useState(!1),x=Object(i.a)(y,2),C=x[0],O=x[1],w=Y(),P=W(),M=Object(V.a)(),S=Object(u.a)().t,k=Object(n.useContext)(c.b),A=Object(i.a)(k,1)[0],I=o.a.useContext(f.a),E=I.position,j=I.isPlaying,T=I.setIsPlaying,L=I.isMapInitialized,R=I.setIsMapInitialized;function D(t){t.automatic||T(!1)}return o.a.useEffect((function(){var t=function(){b(-.25*window.scrollY)};return t(),window.addEventListener("scroll",t),function(){window.removeEventListener("scroll",t)}}),[]),o.a.useLayoutEffect((function(){if(s.current){var t=new B(s.current,e,r,E,A,q(w),(function(){return R(!0)}));return t.mapbox.on("mousedown",D),t.mapbox.on("touchstart",D),t.mapbox.on("zoomstart",D),p(t),function(){t.destructor()}}}),[]),o.a.useEffect((function(){if(m&&a){m.setMeasurementSystem(A),m.setClasses(q(w));var t={max:S("TXT_MAX"),min:S("TXT_MIN")};m.setGraph(a,t)}}),[a,m,M,A,S]),o.a.useEffect((function(){if(m&&a){m.setStyle(r);var t={max:S("TXT_MAX"),min:S("TXT_MIN")};m.setGraph(a,t)}}),[r,m]),o.a.useEffect((function(){!C&&j||null===m||void 0===m||m.setPosition(E)}),[E,m,j,C]),o.a.useEffect((function(){L&&(null===m||void 0===m||m.setPlaying(j))}),[j,m,L]),Object(Z.jsx)("div",{ref:s,className:P.root,style:{transform:"translateY(".concat(g,"px)")},children:m&&Object(Z.jsx)(X,{setIsScreenshotTaken:O,isMapInitialized:L,isPlaying:j,map:m,classes:{root:P.crossFade}})})}}}]);
//# sourceMappingURL=Mapbox.f69e287e.chunk.js.map