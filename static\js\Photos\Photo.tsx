import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { getPhotoUrl } from '../helpers/photos';
import { Photo as PhotoType, PhotoSize } from '../types/WorkoutPayload';

type PhotoProps = {
  classes?: Record<string, string>;
  photo: PhotoType;
  size: [number, number] | PhotoSize;
};

const useStyles = makeStyles({
  root: {
    objectFit: 'cover',
    objectPosition: 'center',
    maxHeight: '100%',
    maxWidth: '100%',
    position: 'absolute',
    top: 0,
  },
});

function Photo(props: PhotoProps): React.ReactElement {
  const classes = useStyles(props);
  const { photo, size } = props;

  return <img className={classes.root} src={getPhotoUrl(photo, size)} alt={photo.description} />;
}

export default Photo;
