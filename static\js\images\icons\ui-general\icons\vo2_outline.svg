var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgVo2Outline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M13.0751 30.9971H13.2138L13.5742 28.2814L15.0435 18.1945C15.2376 16.8644 15.3485 15.9499 15.3485 14.9246V12.9848H17.8713V14.9246C17.8713 16.1993 17.7881 17.0861 17.4832 18.8873L14.6831 35.0152H11.5504L8.7503 18.8873C8.41762 16.9475 8.38989 16.227 8.38989 14.9246V12.9848H10.9127V14.9246C10.9127 15.9499 10.9682 16.9475 11.1622 18.1945L12.7147 28.2537L13.0751 30.9971ZM24.913 35.32H24.3031C20.6159 35.32 19.8396 33.4911 19.8396 31.2465V16.7535C19.8396 14.5089 20.6159 12.68 24.3031 12.68H24.913C28.628 12.68 29.3765 14.5089 29.3765 16.7535V31.2465C29.3765 33.4911 28.628 35.32 24.913 35.32ZM24.2754 32.9923H24.913C26.6319 32.9923 26.8537 32.2995 26.8537 30.5814V17.4186C26.8537 15.7005 26.6596 15.0077 24.913 15.0077H24.2754C22.5011 15.0077 22.3625 15.7005 22.3625 17.4186V30.5814C22.3625 32.2995 22.5288 32.9923 24.2754 32.9923ZM31.8439 27.4223V25.7319C31.8439 24.8729 31.8993 24.3464 32.5647 23.4596L36.1687 18.8042C36.7232 18.0837 36.9172 17.2523 36.9172 16.6427V15.7559C36.9172 14.7583 36.5846 14.6752 35.7529 14.6752H35.3925C34.7271 14.6752 34.339 14.7583 34.2281 15.4788L34.1172 16.3102H31.6221L31.733 15.2017C31.927 13.539 33.1746 12.7631 35.4479 12.7631H35.8083C38.0539 12.7631 39.3292 13.2896 39.3292 15.3957V16.5041C39.3292 17.8066 38.8579 18.8319 37.8321 20.1066L34.5053 24.2355C34.2558 24.5404 34.2004 24.9837 34.2004 25.344H39.6064V27.4223H31.8439Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgVo2Outline);
export default __webpack_public_path__ + "static/media/vo2_outline.2f361155.svg";
export { ForwardRef as ReactComponent };