# static/lib/analysis_event 文件夹详细导读

## 概述

`static/lib/analysis_event` 文件夹包含了 Suunto 地图应用的用户行为分析和事件追踪系统。这是一个完整的事件分析 SDK，负责收集用户行为数据、设备信息、性能指标等，并安全地发送到 Suunto 的分析服务器。

## 文件结构

```
static/lib/analysis_event/
└── index.js                    # 事件分析 SDK 主文件
```

## 核心功能架构

### 1. AnalysisEvent 主类

```javascript
var AnalysisEvent = function (options) {
    if (options) {
        this.globalConfig = { ...this.globalConfig, ...options };
        this.eventConfig.client = options.client || '';
        this.loaded = true;
    } else {
        this.loaded = false;
    }
};
```

**初始化特点**:
- 支持构造函数初始化和延迟初始化
- 配置合并机制
- 加载状态管理

### 2. 工具函数集合 (utils)

#### 语言和地区检测
```javascript
getLanguageFromQuery() {
    var queryString = window.location.search;
    var langRegex = /\b(lang|locale)=([a-zA-Z]+)(?:[_-]([a-zA-Z]+))?/gi;
    // 解析 URL 参数中的语言设置
}

getLanguageFromNavigator() {
    var lang = navigator.userLanguage || navigator.language;
    // 从浏览器设置获取语言信息
}

getLanguageAndRegion: function () {
    let language = this.getLanguageFromQuery() || this.getLanguageFromNavigator();
    return language || { language: '', country: '' };
}
```

**语言检测策略**:
1. **优先级**: URL 参数 > 浏览器设置
2. **格式支持**: `lang=en-US`, `locale=zh_CN` 等
3. **降级处理**: 无法获取时返回空值

#### 设备信息收集
```javascript
getDeviceInfo: function () {
    return new UAParser().getResult();
}
```

**收集的设备信息**:
- 浏览器名称和版本
- 操作系统信息
- 设备类型（桌面/移动）
- 引擎信息

#### JWT Token 生成
```javascript
generateJwtToken: function (global, event) {
    const header = JSON.stringify({
        "typ": "JWT",
        "alg": "HS256"
    });
    
    const payload = JSON.stringify({
        "jti": event.jwtId,
        "sub": event.subject,
        "aud": event.audience,
        "iat": seconds,
        "iss": event.issuer,
        "exp": seconds + global.duration
    });
    
    // HMAC-SHA256 签名
    const signed = hmacAndBase64Url(beforeSign, global.secretKey);
    return `${headerBase64url}.${payloadBase64url}.${signed}`;
}
```

**JWT 特性**:
- 标准 JWT 格式
- HMAC-SHA256 签名算法
- 可配置的过期时间
- Base64URL 编码

### 3. 事件处理系统 (eventUtils)

#### 校验和计算
```javascript
calculateChecksum: function (event) {
    const eventContent = event.client + "," + event.userId
        + "," + event.uploadTime + "," + event.device
        + "," + event.events;
    return CryptoJS.MD5(eventContent).toString(CryptoJS.enc.Hex);
}
```

**安全特性**:
- MD5 哈希校验
- 防止数据篡改
- 确保传输完整性

#### 设备信息构建
```javascript
buildDeviceInfo: function () {
    const ua = this.utils().getDeviceInfo();
    const locale = this.utils().getLanguageAndRegion();
    return {
        language: locale.language,
        country: locale.country,
        deviceManufacturer: ua.browser.name,
        deviceModel: ua.browser.name,
        versionName: ua.browser.version,
        osName: ua.os.name,
        platform: 'Web',
    };
}
```

#### 事件信息构建
```javascript
buildEventInfo: function (type, currentTimestamp, options) {
    const eventInfo = {};
    eventInfo.eventType = type;
    eventInfo.timestamp = currentTimestamp;
    eventInfo.eventProperties = options.event || Object.entries(options).reduce((acc, [key, value]) => {
        if (key !== 'type') acc[key] = value;
        return acc;
    }, {});
    eventInfo.userProperties = options.user || {};
    return [eventInfo];
}
```

**事件结构**:
- 事件类型标识
- 精确的时间戳
- 事件属性（可自定义）
- 用户属性（用户相关数据）

### 4. 配置系统

#### 全局配置
```javascript
AnalysisEvent.prototype.globalConfig = {
    domain: "https://event.suunto.com",
    uri: "event/submit",
    retry: 3,
    duration: 3600,  // Token 过期时间（秒）
    tokenCache: false,
};
```

#### 事件配置
```javascript
AnalysisEvent.prototype.eventConfig = {
    client: 'apikeyst',
    jwtId: "suunto-event",
    subject: "suunto-line",
    audience: "suuntoapp",
    issuer: "suunto"
};
```

### 5. 事件提交机制

#### 主提交方法
```javascript
AnalysisEvent.prototype.submit = function (options) {
    // 1. 参数验证
    if (!this.loaded) {
        throw new Error("AnalysisEvent is not init");
    }
    
    if (!options || !options.type) {
        throw new Error("type is missing");
    }
    
    // 2. 生成 JWT Token
    let jwtToken = this.utils.generateJwtToken(globalConfig, eventConfig);
    
    // 3. 构建事件对象
    const eventObj = this.eventUtils.buildEvent(globalConfig, eventConfig, type, options);
    
    // 4. 编码为 URL 格式
    const body = this.utils.objectEncode(eventObj);
    
    // 5. 执行提交
    doSubmit(retry, type, globalConfig, eventConfig, body, options);
}
```

#### 重试机制
```javascript
function doSubmit(retry, type, global, event, body, options) {
    let last = false;
    if (retry <= 1) {
        last = true;
    }
    
    fetch(`${globalConfig.domain}/${globalConfig.uri}`, {
        method: "POST",
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${jwtToken}`
        },
        body: body
    }).then(r => {
        if (thisEvent.utils.requestSuccess(r)) {
            // 成功处理
            r.json().then(data => {
                if (data.code >= 200 && data.code < 400) {
                    thisEvent.utils.safeCall(options.onSuccess, data);
                } else {
                    // 业务错误重试
                    if (!last) doSubmit(--retry, type, global, event, body, options);
                    else thisEvent.utils.safeCall(options.onFailure, data);
                }
            });
        } else {
            // 网络错误重试
            if (!last) doSubmit(--retry, type, global, event, body, options);
            else thisEvent.utils.safeCall(options.onFailure, r);
        }
    }).catch(err => {
        // 异常错误重试
        if (!last) doSubmit(--retry, type, global, event, body, options);
        else thisEvent.utils.safeCall(options.onError, err);
    });
}
```

**重试策略**:
- 最多重试 3 次
- 网络错误重试
- 业务错误重试
- 异常错误重试
- 回调函数安全调用

### 6. 单例模式实现

```javascript
const createAnalysis = (function () {
    let analysis;
    return function () {
        if (analysis) {
            return analysis;
        }
        analysis = new AnalysisEvent();
        return analysis;
    }
})();

const analysisClient = {};
analysisClient.getInstance = function () {
    return createAnalysis();
}
```

**设计优势**:
- 全局唯一实例
- 延迟初始化
- 内存优化

## 使用方式

### 基本使用
```javascript
import analysisClient from './static/lib/analysis_event';

// 获取实例
const analytics = analysisClient.getInstance();

// 初始化
analytics.init({
    client: 'suunto-maps',
    userId: 'user123',
    secretKey: 'your-secret-key'
});

// 记录事件
analytics.logEvent('page_view', {
    page: '/move/user/workout123',
    referrer: document.referrer
});

// 自定义事件
analytics.submit({
    type: 'map_interaction',
    event: {
        action: 'zoom',
        level: 15,
        duration: 1200
    },
    user: {
        subscription: 'premium'
    },
    onSuccess: (data) => console.log('Event sent successfully'),
    onFailure: (error) => console.error('Event failed', error)
});
```

### 在 Suunto 项目中的集成

```javascript
// 在 App.tsx 中初始化
import analysisClient from './lib/analysis_event';

const analytics = analysisClient.getInstance();
analytics.init({
    client: 'suunto-maps-web',
    userId: getCurrentUserId(),
    domain: process.env.REACT_APP_ANALYTICS_DOMAIN
});

// 在地图组件中使用
class WorkoutMap {
    constructor() {
        // 记录地图初始化事件
        analytics.logEvent('map_initialized', {
            style: this.mapStyle,
            workout_id: this.workoutId
        });
    }
    
    onMapInteraction(event) {
        // 记录用户交互
        analytics.logEvent('map_interaction', {
            type: event.type,
            coordinates: event.lngLat,
            zoom_level: this.map.getZoom()
        });
    }
}
```

## 数据流架构

```
用户行为
    ↓
事件触发 (logEvent/submit)
    ↓
参数验证和配置检查
    ↓
设备信息收集
    ↓
事件对象构建
    ↓
JWT Token 生成
    ↓
数据校验和计算
    ↓
HTTP 请求发送
    ↓
重试机制处理
    ↓
回调函数执行
```

## 安全特性

### 1. 数据完整性
- MD5 校验和验证
- 防止数据传输过程中的篡改

### 2. 身份认证
- JWT Token 认证
- HMAC-SHA256 签名
- 可配置的过期时间

### 3. 传输安全
- HTTPS 强制传输
- Bearer Token 授权头
- URL 编码防止注入

### 4. 错误处理
- 异常捕获和处理
- 安全的回调函数调用
- 详细的错误分类

## 性能优化

### 1. 单例模式
- 避免重复实例化
- 减少内存占用

### 2. 延迟初始化
- 按需加载配置
- 减少启动时间

### 3. 批量处理
- 事件数组支持
- 减少网络请求

### 4. 缓存机制
- Token 缓存选项
- 避免重复计算

## 监控和调试

### 1. 错误分类
- 初始化错误
- 参数验证错误
- 网络传输错误
- 业务逻辑错误

### 2. 回调支持
- 成功回调 (onSuccess)
- 失败回调 (onFailure)
- 错误回调 (onError)

### 3. 日志记录
- 控制台错误输出
- 详细的错误信息

## 扩展性设计

### 1. 配置灵活性
- 可配置的服务端点
- 可调整的重试策略
- 自定义事件属性

### 2. 插件化支持
- 工具函数模块化
- 事件处理器可扩展
- 中间件机制

### 3. 多环境支持
- 开发/生产环境配置
- 不同客户端标识
- 动态配置更新

这个事件分析系统为 Suunto 地图应用提供了完整的用户行为追踪能力，确保了数据的安全传输和可靠处理，是现代 Web 应用分析系统的优秀实践。

## 深度技术分析

### 事件数据结构详解

#### 完整的事件对象结构
```javascript
{
  client: 'suunto-maps-web',           // 客户端标识
  userId: 'user_12345',               // 用户唯一标识
  uploadTime: 1640995200000,          // 上传时间戳
  device: JSON.stringify({            // 设备信息（JSON字符串）
    language: 'en',
    country: 'US',
    deviceManufacturer: 'Chrome',
    deviceModel: 'Chrome',
    versionName: '96.0.4664.110',
    osName: 'Windows',
    platform: 'Web'
  }),
  events: JSON.stringify([{           // 事件数组（JSON字符串）
    eventType: 'map_interaction',
    timestamp: 1640995200000,
    eventProperties: {
      action: 'zoom',
      level: 15,
      coordinates: [24.9384, 60.1699]
    },
    userProperties: {
      subscription: 'premium',
      workout_count: 150
    }
  }]),
  checksum: 'a1b2c3d4e5f6...'         // MD5校验和
}
```

### 加密和安全机制深度解析

#### JWT Token 生成流程
```javascript
// 1. Header 构建
const header = {
  "typ": "JWT",
  "alg": "HS256"
};

// 2. Payload 构建
const payload = {
  "jti": "suunto-event",              // JWT ID
  "sub": "suunto-line",               // Subject
  "aud": "suuntoapp",                 // Audience
  "iat": 1640995200,                  // Issued At
  "iss": "suunto",                    // Issuer
  "exp": 1640998800                   // Expiration (iat + 3600)
};

// 3. 签名计算
const headerBase64 = base64UrlEncode(JSON.stringify(header));
const payloadBase64 = base64UrlEncode(JSON.stringify(payload));
const signature = hmacSha256(headerBase64 + '.' + payloadBase64, secretKey);

// 4. 最终 Token
const jwtToken = headerBase64 + '.' + payloadBase64 + '.' + signature;
```

#### 校验和算法详解
```javascript
function calculateChecksum(event) {
  // 1. 构建校验字符串
  const checksumString = [
    event.client,
    event.userId,
    event.uploadTime,
    event.device,      // JSON 字符串
    event.events       // JSON 字符串
  ].join(',');

  // 2. MD5 哈希计算
  const hash = CryptoJS.MD5(checksumString);

  // 3. 十六进制编码
  return hash.toString(CryptoJS.enc.Hex);
}
```

**安全考虑**:
- 包含所有关键数据字段
- 防止数据篡改
- 服务端可验证数据完整性

### 网络请求优化策略

#### 指数退避重试算法
```javascript
function exponentialBackoffRetry(attempt, maxAttempts, baseDelay = 1000) {
  if (attempt >= maxAttempts) {
    return false; // 停止重试
  }

  const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;

  setTimeout(() => {
    doSubmit(attempt + 1, ...args);
  }, delay);

  return true;
}
```

#### 请求去重机制
```javascript
const pendingRequests = new Map();

function deduplicateRequest(eventKey, requestFn) {
  if (pendingRequests.has(eventKey)) {
    return pendingRequests.get(eventKey);
  }

  const promise = requestFn().finally(() => {
    pendingRequests.delete(eventKey);
  });

  pendingRequests.set(eventKey, promise);
  return promise;
}
```

### 事件缓存和批处理

#### 本地存储缓存
```javascript
class EventCache {
  constructor() {
    this.storageKey = 'suunto_analytics_cache';
    this.maxCacheSize = 100;
  }

  addEvent(event) {
    const cached = this.getCachedEvents();
    cached.push({
      ...event,
      cached_at: Date.now()
    });

    // 限制缓存大小
    if (cached.length > this.maxCacheSize) {
      cached.shift();
    }

    localStorage.setItem(this.storageKey, JSON.stringify(cached));
  }

  getCachedEvents() {
    try {
      return JSON.parse(localStorage.getItem(this.storageKey) || '[]');
    } catch {
      return [];
    }
  }

  clearCache() {
    localStorage.removeItem(this.storageKey);
  }
}
```

#### 批量提交优化
```javascript
class BatchProcessor {
  constructor(batchSize = 10, flushInterval = 30000) {
    this.batch = [];
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.timer = null;
  }

  addEvent(event) {
    this.batch.push(event);

    if (this.batch.length >= this.batchSize) {
      this.flush();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.flush(), this.flushInterval);
    }
  }

  flush() {
    if (this.batch.length === 0) return;

    const events = [...this.batch];
    this.batch = [];

    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    this.submitBatch(events);
  }

  submitBatch(events) {
    // 批量提交逻辑
    analytics.submit({
      type: 'batch_events',
      events: events
    });
  }
}
```

### 用户隐私保护

#### 数据脱敏处理
```javascript
function sanitizeUserData(userData) {
  const sensitiveFields = ['email', 'phone', 'address'];
  const sanitized = { ...userData };

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = hashSensitiveData(sanitized[field]);
    }
  });

  return sanitized;
}

function hashSensitiveData(data) {
  return CryptoJS.SHA256(data + SALT).toString().substring(0, 8);
}
```

#### GDPR 合规性支持
```javascript
class GDPRCompliance {
  constructor() {
    this.consentKey = 'suunto_analytics_consent';
  }

  hasConsent() {
    return localStorage.getItem(this.consentKey) === 'granted';
  }

  grantConsent() {
    localStorage.setItem(this.consentKey, 'granted');
    this.flushPendingEvents();
  }

  revokeConsent() {
    localStorage.setItem(this.consentKey, 'revoked');
    this.clearAllData();
  }

  clearAllData() {
    // 清除所有分析数据
    localStorage.removeItem('suunto_analytics_cache');
    // 通知服务器删除用户数据
    this.requestDataDeletion();
  }
}
```

### 性能监控集成

#### 页面性能指标收集
```javascript
function collectPerformanceMetrics() {
  if (!window.performance) return {};

  const navigation = performance.getEntriesByType('navigation')[0];
  const paint = performance.getEntriesByType('paint');

  return {
    // 页面加载时间
    page_load_time: navigation.loadEventEnd - navigation.fetchStart,
    // DNS 查询时间
    dns_lookup_time: navigation.domainLookupEnd - navigation.domainLookupStart,
    // 首次内容绘制
    first_contentful_paint: paint.find(p => p.name === 'first-contentful-paint')?.startTime,
    // 最大内容绘制
    largest_contentful_paint: getLCP(),
    // 累积布局偏移
    cumulative_layout_shift: getCLS(),
    // 首次输入延迟
    first_input_delay: getFID()
  };
}

// 自动收集性能数据
window.addEventListener('load', () => {
  setTimeout(() => {
    analytics.logEvent('page_performance', collectPerformanceMetrics());
  }, 1000);
});
```

#### 错误监控集成
```javascript
window.addEventListener('error', (event) => {
  analytics.logEvent('javascript_error', {
    message: event.message,
    filename: event.filename,
    line: event.lineno,
    column: event.colno,
    stack: event.error?.stack,
    user_agent: navigator.userAgent,
    url: window.location.href
  });
});

window.addEventListener('unhandledrejection', (event) => {
  analytics.logEvent('promise_rejection', {
    reason: event.reason?.toString(),
    stack: event.reason?.stack,
    url: window.location.href
  });
});
```

### A/B 测试支持

#### 实验配置管理
```javascript
class ExperimentManager {
  constructor() {
    this.experiments = new Map();
  }

  addExperiment(name, variants, trafficAllocation = 1.0) {
    const userId = getCurrentUserId();
    const hash = this.hashUserId(userId + name);
    const bucket = hash % 100;

    if (bucket < trafficAllocation * 100) {
      const variantIndex = hash % variants.length;
      const variant = variants[variantIndex];

      this.experiments.set(name, variant);

      // 记录实验参与事件
      analytics.logEvent('experiment_exposure', {
        experiment_name: name,
        variant: variant,
        user_bucket: bucket
      });

      return variant;
    }

    return null; // 用户不参与实验
  }

  getVariant(experimentName) {
    return this.experiments.get(experimentName);
  }

  hashUserId(input) {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
}

// 使用示例
const experiments = new ExperimentManager();
const mapStyleVariant = experiments.addExperiment('map_style_test', ['satellite', 'terrain'], 0.5);

if (mapStyleVariant) {
  // 应用实验变体
  setMapStyle(mapStyleVariant);
}
```

### 实时数据流处理

#### WebSocket 集成
```javascript
class RealTimeAnalytics {
  constructor(wsUrl) {
    this.wsUrl = wsUrl;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    this.ws = new WebSocket(this.wsUrl);

    this.ws.onopen = () => {
      console.log('Real-time analytics connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleRealTimeEvent(data);
    };

    this.ws.onclose = () => {
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  sendRealTimeEvent(event) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(event));
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000;
      setTimeout(() => this.connect(), delay);
    }
  }
}
```

### 数据可视化支持

#### 分析仪表板数据格式
```javascript
function formatAnalyticsData(events) {
  return {
    summary: {
      total_events: events.length,
      unique_users: new Set(events.map(e => e.userId)).size,
      time_range: {
        start: Math.min(...events.map(e => e.timestamp)),
        end: Math.max(...events.map(e => e.timestamp))
      }
    },

    event_breakdown: events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {}),

    user_journey: events
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(event => ({
        timestamp: event.timestamp,
        event_type: event.eventType,
        properties: event.eventProperties
      })),

    performance_metrics: events
      .filter(e => e.eventType === 'page_performance')
      .map(e => e.eventProperties)
  };
}
```

这个事件分析系统展示了现代 Web 应用中用户行为追踪的完整解决方案，涵盖了数据收集、安全传输、性能优化、隐私保护等各个方面，是企业级分析系统的优秀实践。
