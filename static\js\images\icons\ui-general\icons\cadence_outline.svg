var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgCadenceOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M7.66601 14.5351C12.8781 5.50753 24.4215 2.41454 33.4613 7.6337C36.3788 9.36265 38.6482 11.6065 40.2543 14.3516L40.6658 9.27588C40.7217 8.58779 41.3248 8.0753 42.0129 8.13121C42.658 8.18362 43.1487 8.71699 43.1615 9.35043L43.1576 9.47833L42.3143 19.8575L32.1693 16.6266C31.5115 16.4171 31.1481 15.714 31.3576 15.0562C31.554 14.4395 32.1842 14.0816 32.8042 14.2118L32.928 14.2445L38.2889 15.9523C36.8891 13.4199 34.8628 11.3702 32.1991 9.79159C24.3673 5.26989 14.3528 7.95327 9.83107 15.7851C5.30938 23.6169 7.99275 33.6314 15.8246 38.1531C23.5572 42.6175 33.4177 40.0582 38.0182 32.4545L38.1926 32.1596L40.3576 33.4096C35.1456 42.4371 23.6021 45.5302 14.5746 40.3181C5.54702 35.1061 2.45396 23.5626 7.66601 14.5351Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgCadenceOutline);
export default __webpack_public_path__ + "static/media/cadence_outline.406770cd.svg";
export { ForwardRef as ReactComponent };