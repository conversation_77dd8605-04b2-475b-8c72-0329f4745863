import { Link, Typography } from '@mui/material';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import SuuntoIcons from '../../fonts/Suuntocom-icons.woff2';
import { SomeLinkGroup } from '../parseSiteFooter';

type SomeProps = {
  classes?: Record<string, string>;
  someLinks?: SomeLinkGroup;
};

const useStyles = makeStyles(
  (theme) => ({
    '@global': {
      '@font-face': {
        fontFamily: 'Suuntocom Icons',
        fontWeight: 'normal',
        src: `url("${SuuntoIcons}") format("woff2")`,
      },
    },
    root: {
      marginBottom: theme.spacing(2),
      textAlign: 'center',
    },
    title: {},
    link: {
      '&:hover': {
        color: theme.palette.primary.dark,
      },
      margin: theme.spacing(0.4),
      fontSize: '3rem',
      fontFamily: 'Suuntocom Icons',
    },
  }),
  { name: 'Some' },
);

const IconMap = [
  {
    Name: 'Twitter',
    iconRound: '\uE926',
  },
  {
    Name: 'Facebook',
    iconRound: '\uE917',
  },
  {
    Name: 'Youtube',
    iconRound: '\uE928',
  },
  {
    Name: 'Instagram',
    iconRound: '\uE919',
  },
  {
    Name: 'Pinterest',
    iconRound: '\uE91D',
  },
  {
    Name: 'RSS Feed',
    iconRound: '\uE9AF',
  },
  {
    Name: 'Youko',
    iconRound: '\uE9B1',
  },
  {
    Name: 'Weibo',
    iconRound: '\uE9B0',
  },
];

function Some(props: SomeProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { someLinks } = props;
  if (!someLinks) return null;
  return (
    <section className={classes.root}>
      <Typography component="h2" variant="h5" className={classes.title}>
        <strong>{someLinks.title}</strong>
      </Typography>
      {someLinks.links.map(({ title, href, some }) => (
        <Link
          key={some}
          className={classes.link}
          href={href}
          color="inherit"
          target="_blank"
          rel="external nofollow noopener"
          aria-label={title}
          underline="none"
          title={title}
        >
          {(Object.values(IconMap).find(({ Name }) => Name === some) || {}).iconRound || ''}
        </Link>
      ))}
    </section>
  );
}

export default Some;
