var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgStepsOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M11.5838 25.7084C13.3908 22.5786 17.141 23.2024 21.9745 25.9931C23.5199 26.8853 24.4409 27.839 25.5221 29.4297L26.1533 30.3826L26.4387 30.7978C26.9675 31.5389 27.4094 31.9783 28.0024 32.3206C28.9734 32.8813 29.4999 33.0892 30.306 33.2725L31.0741 33.4389C31.7781 33.6012 32.3331 33.7837 33.0829 34.1221C36.3826 35.6113 37.9582 38.2902 36.1962 41.3421C34.7945 43.77 32.7212 44.4463 30.4564 43.7693C29.7807 43.5674 29.2639 43.3266 28.5307 42.9137L28.2449 42.7506L28.0078 42.6103L27.7454 42.4442C27.7012 42.4151 27.6563 42.385 27.6101 42.3534L27.3122 42.1432L26.9527 41.8771L25.5717 40.8225C24.8394 40.2694 24.3428 39.9283 23.7933 39.611C23.4177 39.3942 23.035 39.2559 22.3038 39.0874L22.0736 39.0358L21.6307 38.9426L20.7907 38.7754C19.2334 38.4607 18.4052 38.2086 17.5011 37.6866C12.532 34.8177 9.48915 29.3365 11.5838 25.7084ZM20.7245 28.1581C17.0159 26.0169 14.5311 25.6036 13.7489 26.9584C12.5145 29.0964 14.8221 33.2532 18.7511 35.5216C19.2644 35.818 19.8082 36.0035 20.7626 36.2145L21.0333 36.2728L22.4414 36.5573L22.9833 36.6773C23.9004 36.8922 24.4266 37.0899 25.0433 37.4459C25.4343 37.6717 25.793 37.9007 26.1901 38.1781L26.4962 38.3959L27.0562 38.8107L28.4411 39.8681C28.8396 40.1675 29.0747 40.3314 29.313 40.4775L29.4949 40.5855L29.728 40.7187C30.3223 41.054 30.711 41.2361 31.1723 41.374C32.3843 41.7363 33.2438 41.456 34.0312 40.0921C34.8994 38.5882 34.1761 37.3582 32.0546 36.4008C31.4766 36.14 31.0731 36.0052 30.5298 35.879L30.245 35.816L29.7517 35.7102C28.7095 35.4729 27.9549 35.18 26.7524 34.4857C25.7978 33.9346 25.1198 33.2559 24.3949 32.2376L24.2125 31.9759L23.4484 30.8261C22.5667 29.5302 21.8827 28.8268 20.7245 28.1581ZM30.7984 10.5717C31.2831 11.4112 31.535 12.1853 31.8205 13.5385L32.1021 14.9315L32.1985 15.3715L32.2987 15.7784C32.4132 16.2091 32.5237 16.4978 32.6694 16.7681L32.7228 16.8638C33.04 17.4133 33.3812 17.91 33.9342 18.6423L34.9889 20.0232L35.3367 20.4966C35.5002 20.7263 35.6221 20.9124 35.744 21.1146L35.8624 21.3154C36.3775 22.2077 36.6539 22.7668 36.8811 23.527C37.558 25.7918 36.8817 27.8651 34.4539 29.2668C31.402 31.0288 28.723 29.4532 27.2339 26.1535C26.8955 25.4037 26.713 24.8486 26.5506 24.1446L26.4338 23.6039C26.2358 22.6625 26.044 22.1322 25.4324 21.0729C25.09 20.4799 24.6507 20.0381 23.9096 19.5093L23.4943 19.2238L22.8474 18.7975C21.0702 17.6241 20.0529 16.6871 19.1048 15.045L18.7925 14.4922C16.2718 9.92593 15.8108 6.39186 18.8202 4.65436C22.4482 2.55971 27.9295 5.60255 30.7984 10.5717ZM20.0702 6.81942C18.7154 7.60163 19.1287 10.0864 21.2699 13.795C21.9386 14.9533 22.642 15.6373 23.9379 16.519L25.0876 17.2831C26.2537 18.0792 27.0005 18.7889 27.5975 19.8229C28.2918 21.0255 28.5847 21.78 28.822 22.8223L28.9278 23.3156C29.0755 24.014 29.2083 24.4508 29.5126 25.1251C30.47 27.2466 31.7 27.97 33.2039 27.1017C34.5677 26.3143 34.848 25.4549 34.4858 24.2429C34.3479 23.7815 34.1658 23.3929 33.8305 22.7985L33.6973 22.5654C33.4806 22.19 33.2897 21.9155 32.7281 21.1796L31.9224 20.1268C31.3193 19.3275 30.934 18.7656 30.5577 18.1138C30.1106 17.3395 29.9132 16.7079 29.6179 15.2657L29.3846 14.1039C29.148 12.9779 28.9567 12.3817 28.6333 11.8217C26.365 7.89269 22.2081 5.58509 20.0702 6.81942Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgStepsOutline);
export default __webpack_public_path__ + "static/media/steps_outline.8012ed6f.svg";
export { ForwardRef as ReactComponent };