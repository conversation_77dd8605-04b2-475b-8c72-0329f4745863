var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSeaLevelPressureOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24 4C35.0457 4 44 12.9543 44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24C4 12.9543 12.9543 4 24 4ZM24 6.5C14.335 6.5 6.5 14.335 6.5 24C6.5 33.665 14.335 41.5 24 41.5C33.665 41.5 41.5 33.665 41.5 24C41.5 14.335 33.665 6.5 24 6.5ZM32.6031 18.3853C33.0591 18.8406 33.0901 19.5604 32.6957 20.0515L32.6047 20.1531L27.1961 25.5713C27.3914 26.0076 27.5 26.4911 27.5 27C27.5 28.933 25.933 30.5 24 30.5C23.4911 30.5 23.0076 30.3914 22.5713 30.1961L20.6339 32.1339L18.8661 30.3661L20.8039 28.4287C20.6086 27.9924 20.5 27.5089 20.5 27C20.5 25.067 22.067 23.5 24 23.5C24.5085 23.5 24.9917 23.6084 25.4277 23.8035L30.8353 18.3869C31.3231 17.8983 32.1145 17.8976 32.6031 18.3853ZM9.52157 21.2688L11.9784 21.7312C11.8265 22.5385 11.75 23.2721 11.75 24C11.75 26.2907 12.2173 28.3539 13.1437 30.0456L13.3228 30.3584L11.1772 31.6416C9.88451 29.4803 9.25 26.862 9.25 24C9.25 23.107 9.34237 22.2208 9.52157 21.2688ZM38.4795 21.2745C38.6537 22.2244 38.75 23.1397 38.75 24C38.75 26.594 38.1885 28.9182 37.0362 31.0169L36.8268 31.3849L34.6732 30.1151C35.7336 28.3168 36.25 26.3116 36.25 24C36.25 23.4203 36.1939 22.7901 36.0891 22.1271L36.0205 21.7255L38.4795 21.2745ZM24 26C23.4477 26 23 26.4477 23 27C23 27.5523 23.4477 28 24 28C24.5523 28 25 27.5523 25 27C25 26.4477 24.5523 26 24 26ZM16.6436 11.1569L17.8564 13.3431C15.4116 14.6993 13.6123 16.8241 12.5612 19.5571L12.4284 19.917L10.0716 19.083C11.2996 15.6132 13.5469 12.8748 16.6436 11.1569ZM31.3731 11.1689C34.322 12.8645 36.5797 15.5195 37.7943 18.7157L37.9297 19.0868L35.5703 19.9132C34.6317 17.2334 32.8272 14.9918 30.4414 13.5233L30.1269 13.3361L31.3731 11.1689ZM24 9.25C25.8988 9.25 27.4121 9.4516 28.865 9.96061L29.1994 10.0836L28.3006 12.4164C27.0664 11.9409 25.7616 11.75 24 11.75C22.5302 11.75 21.1778 11.9337 20.0219 12.3059L19.6812 12.4233L18.8188 10.0767C20.3461 9.51546 22.1074 9.25 24 9.25Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSeaLevelPressureOutline);
export default __webpack_public_path__ + "static/media/sea_level_pressure_outline.8a61e2cb.svg";
export { ForwardRef as ReactComponent };