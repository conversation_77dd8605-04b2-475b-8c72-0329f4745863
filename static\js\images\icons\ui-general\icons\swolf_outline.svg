var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSwolfOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M36.5 29.75C37.3685 29.75 38.0927 30.0477 38.8896 30.621L39.218 30.869L39.4516 31.0569L39.9839 31.4992L40.2378 31.7052C41.1892 32.4589 41.8453 32.7534 42.75 32.7534C43.4404 32.7534 44 33.313 44 34.0034C44 34.6938 43.4404 35.2534 42.75 35.2534C41.1731 35.2534 40.0522 34.7452 38.6973 33.6742L38.4404 33.4668L37.6748 32.8359L37.43 32.6506C37.0285 32.3618 36.7554 32.25 36.5 32.25C36.2446 32.25 35.9715 32.3618 35.57 32.6506L35.4133 32.7674L35.2312 32.9108L34.3027 33.6742C32.9478 34.7452 31.8269 35.2534 30.25 35.2534C28.6731 35.2534 27.5522 34.7452 26.1973 33.6742L25.9404 33.4668L25.1748 32.8359L24.93 32.6506C24.5285 32.3618 24.2554 32.25 24 32.25C23.7446 32.25 23.4715 32.3618 23.07 32.6506L22.9133 32.7674L22.7312 32.9108L21.8027 33.6742C20.4478 34.7452 19.3269 35.2534 17.75 35.2534C16.1731 35.2534 15.0522 34.7452 13.6973 33.6742L13.4404 33.4668L12.6748 32.8359L12.43 32.6506C12.0285 32.3618 11.7554 32.25 11.5 32.25C11.2446 32.25 10.9715 32.3618 10.57 32.6506L10.4133 32.7674L10.2312 32.9108L9.30266 33.6742C7.94777 34.7452 6.82687 35.2534 5.25 35.2534C4.55964 35.2534 4 34.6938 4 34.0034C4 33.313 4.55964 32.7534 5.25 32.7534C6.15467 32.7534 6.81082 32.4589 7.7622 31.7052L7.97079 31.5366L8.54841 31.0569L8.78205 30.869L9.1104 30.621C9.90732 30.0477 10.6315 29.75 11.5 29.75C12.3685 29.75 13.0927 30.0477 13.8896 30.621L14.218 30.869L14.4516 31.0569L14.9839 31.4992L15.2378 31.7052C16.1892 32.4589 16.8453 32.7534 17.75 32.7534C18.6547 32.7534 19.3108 32.4589 20.2622 31.7052L20.4708 31.5366L21.0484 31.0569L21.282 30.869L21.6104 30.621C22.4073 30.0477 23.1315 29.75 24 29.75C24.8685 29.75 25.5927 30.0477 26.3896 30.621L26.718 30.869L26.9516 31.0569L27.4839 31.4992L27.7378 31.7052C28.6892 32.4589 29.3453 32.7534 30.25 32.7534C31.1547 32.7534 31.8108 32.4589 32.7622 31.7052L32.9708 31.5366L33.5484 31.0569L33.782 30.869L34.1104 30.621C34.9073 30.0477 35.6315 29.75 36.5 29.75ZM21.9687 13.5011C22.6154 13.528 23.1267 14.0417 23.1645 14.6741L23.1656 14.8021L22.8458 22.4757L15.4525 21.1899C14.7723 21.0716 14.3169 20.4243 14.4352 19.7442C14.546 19.1065 15.1219 18.6664 15.7538 18.7113L15.8808 18.7268L18.6901 19.2147C17.6169 17.4247 15.6599 16.25 13.5 16.25C10.9036 16.25 8.59825 17.9507 7.77146 20.3763L7.69831 20.6058L5.30169 19.8943C6.36959 16.297 9.70423 13.75 13.5 13.75C16.3786 13.75 18.9909 15.2129 20.5512 17.4881L20.6677 14.698C20.6965 14.0082 21.2789 13.4724 21.9687 13.5011ZM42.8 13.501C43.4467 13.5269 43.9589 14.0396 43.9977 14.672L43.999 14.8L43.6924 22.466L36.2945 21.233C35.6135 21.1195 35.1535 20.4755 35.267 19.7945C35.3734 19.1561 35.9461 18.7119 36.5784 18.7524L36.7055 18.767L39.7217 19.2702C38.6546 17.4476 36.6823 16.25 34.5 16.25C31.902 16.25 29.6011 17.9479 28.7715 20.3769L28.698 20.6067L26.302 19.8933C27.3738 16.2936 30.7027 13.75 34.5 13.75C37.2876 13.75 39.8227 15.1205 41.3978 17.2734L41.501 14.7001C41.5286 14.0103 42.1102 13.4734 42.8 13.501Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSwolfOutline);
export default __webpack_public_path__ + "static/media/swolf_outline.a1fb7089.svg";
export { ForwardRef as ReactComponent };