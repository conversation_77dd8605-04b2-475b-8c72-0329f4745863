var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgPercentageOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M30.5 28.5C33.6756 28.5 36.25 31.0744 36.25 34.25C36.25 37.4256 33.6756 40 30.5 40C27.3244 40 24.75 37.4256 24.75 34.25C24.75 31.0744 27.3244 28.5 30.5 28.5ZM12.9654 38.0486L32.9654 8.54855C33.3528 7.97714 34.13 7.82797 34.7014 8.21537C35.2348 8.57694 35.4003 9.27813 35.105 9.8346L35.0346 9.95145L15.0346 39.4515C14.6472 40.0229 13.87 40.172 13.2986 39.7846C12.7652 39.4231 12.5997 38.7219 12.895 38.1654L12.9654 38.0486L32.9654 8.54855L12.9654 38.0486ZM30.5 31C28.7051 31 27.25 32.4551 27.25 34.25C27.25 36.0449 28.7051 37.5 30.5 37.5C32.2949 37.5 33.75 36.0449 33.75 34.25C33.75 32.4551 32.2949 31 30.5 31ZM17.5 8C20.6756 8 23.25 10.5744 23.25 13.75C23.25 16.9256 20.6756 19.5 17.5 19.5C14.3244 19.5 11.75 16.9256 11.75 13.75C11.75 10.5744 14.3244 8 17.5 8ZM17.5 10.5C15.7051 10.5 14.25 11.9551 14.25 13.75C14.25 15.5449 15.7051 17 17.5 17C19.2949 17 20.75 15.5449 20.75 13.75C20.75 11.9551 19.2949 10.5 17.5 10.5Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgPercentageOutline);
export default __webpack_public_path__ + "static/media/percentage_outline.313c0a36.svg";
export { ForwardRef as ReactComponent };