var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSunriseOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24.0001 28.9999C30.1162 28.9999 35.0924 33.8806 35.2464 39.9596L35.2501 40.2499H42.7501C43.4405 40.2499 44.0001 40.8096 44.0001 41.4999C44.0001 42.1472 43.5082 42.6795 42.8779 42.7435L42.7501 42.7499H5.2501C4.55974 42.7499 4.00009 42.1903 4.00009 41.4999C4.00009 40.8527 4.49197 40.3204 5.12229 40.2564L5.2501 40.2499H12.7501C12.7501 34.0367 17.7869 28.9999 24.0001 28.9999ZM24.0001 31.4999C19.2539 31.4999 15.3903 35.2788 15.2538 39.992L15.2501 40.2499H32.7501C32.7501 35.4175 28.8326 31.4999 24.0001 31.4999ZM43.9359 32.3547C44.1406 32.9687 43.8423 33.6292 43.2646 33.8893L43.1454 33.9358L39.3954 35.1858C38.7405 35.4041 38.0326 35.0502 37.8142 34.3952C37.6096 33.7812 37.9079 33.1207 38.4856 32.8606L38.6048 32.8141L42.3548 31.5641C43.0097 31.3458 43.7176 31.6997 43.9359 32.3547ZM4.06424 32.3547C4.26891 31.7407 4.90388 31.3912 5.52209 31.5298L5.64538 31.5641L9.39538 32.8141C10.0503 33.0324 10.4043 33.7403 10.1859 34.3952C9.98128 35.0092 9.34631 35.3587 8.7281 35.2201L8.60481 35.1858L4.85481 33.9358C4.19988 33.7175 3.84593 33.0096 4.06424 32.3547ZM37.384 23.1161C37.8396 23.5717 37.87 24.2915 37.4751 24.7823L37.384 24.8838L34.884 27.3838C34.3958 27.872 33.6044 27.872 33.1162 27.3838C32.6606 26.9282 32.6302 26.2084 33.0251 25.7176L33.1162 25.6161L35.6162 23.1161C36.1044 22.6279 36.8958 22.6279 37.384 23.1161ZM12.2825 23.0249L12.384 23.1161L14.884 25.6161C15.3721 26.1042 15.3721 26.8957 14.884 27.3838C14.4284 27.8394 13.7085 27.8698 13.2177 27.475L13.1162 27.3838L10.6162 24.8838C10.1281 24.3957 10.1281 23.6042 10.6162 23.1161C11.0393 22.693 11.6902 22.6366 12.174 22.9468L12.2825 23.0249ZM24.0001 4.73218L32.384 13.1161C32.8721 13.6042 32.8721 14.3957 32.384 14.8838C31.9284 15.3394 31.2085 15.3698 30.7177 14.975L30.6162 14.8838L25.2498 9.51718L25.2501 21.4999C25.2501 22.1472 24.7582 22.6795 24.1279 22.7435L24.0001 22.7499C23.3529 22.7499 22.8206 22.2581 22.7565 21.6278L22.7501 21.4999L22.7498 9.51718L17.384 14.8838C16.8958 15.372 16.1044 15.372 15.6162 14.8838C15.1606 14.4282 15.1302 13.7084 15.5251 13.2176L15.6162 13.1161L24.0001 4.73218Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSunriseOutline);
export default __webpack_public_path__ + "static/media/sunrise_outline.48a12607.svg";
export { ForwardRef as ReactComponent };