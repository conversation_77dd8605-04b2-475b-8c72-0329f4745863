import { useMediaQuery, useTheme, IconButton } from '@mui/material';
import React, { Suspense } from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { AmplitudeContext, AmplitudeEvent, useAmplitude } from '../Amplitude/Amplitude';
import useToggle from '../helpers/useToggle';
import SuuntoLogo from '../images/suunto_logo.svg';
import SuuntoLogoWhite from '../images/suunto_logo_white.svg';
import LoadingIndicator from '../LoadingIndicator/LoadingIndicator';
import Menu from '../Menu/Menu';
import SiteFooter from '../SiteFooter/SiteFooter';
import { WorkoutExtensionType } from '../types/WorkoutPayload';
import { Workout as WorkoutModel } from './../models/Workout';
import Footer from './Footer/Footer';
import { getGraphs } from './GraphsHelper';
import PathColorDataSelector from './PathColorDataSelector/PathColorDataSelector';
import MapStyleSelector, { MapStyles } from './MapStyleSelector/MapStyleSelector';
import PathConfiguration from './PathConfiguration';
import ScrollUpButton from './ScrollUpButton/ScrollUpButton';
import WorkoutSummary from './Summary/WorkoutSummary';
import UserChip from './UserChip/UserChip';
import WorkoutDetails from './UserChip/WorkoutDetails';
import { TWO_PANEL_BREAKPOINT } from './workoutConfig';
import { ReactComponent as SettingsIcon } from './../images/icons/software-actions/icons/settings_outline.svg';
import { getActivityConfigBySTId } from '../activities/helper';

const Mapbox = React.lazy(() => import(/* webpackChunkName: 'Mapbox' */ './Mapbox/Mapbox'));
type WorkoutProps = {
  classes?: Record<string, string>;
  workout?: WorkoutModel | null;
  workoutLoading: boolean;
};

const SUMMARY_WIDTH_TWO_PANEL = 375;
const SUMMARY_PADDING = 1.2;

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    height: 'fill-available',
    fallbacks: [{ height: '100vh' }],
  },
  footerWrapper: {
    bottom: 0,
    display: 'flex',
    flexWrap: 'wrap',
    position: 'absolute',
    alignItems: 'end',
    width: '100%',
  },
  footer: {
    padding: theme.spacing(1),
    marginTop: theme.spacing(1),
    backgroundColor: theme.palette.background.default,
    borderTopRightRadius: theme.spacing(1.5),
    borderTopLeftRadius: theme.spacing(1.5),
    position: 'relative',
    width: '100%',
  },
  footerSide: {
    flexBasis: 100,
    flexShrink: 0,
    flexGrow: 0,
    position: 'relative',
  },
  footerRight: {
    flexBasis: 100,
    flexShrink: 0,
    flexGrow: 100,
    position: 'relative',
  },
  siteFooter: {
    padding: theme.spacing(SUMMARY_PADDING),
  },
  pathDial: {
    position: 'absolute',
    left: theme.spacing(2),
    bottom: 0,
  },
  styleDial: {
    position: 'absolute',
    right: theme.spacing(2),
    bottom: 0,
  },
  summary: {
    paddingTop: 0,
    position: 'relative',
    flexBasis: 800,
    flexGrow: 0,
  },
  mapWrapper: {
    backgroundColor: theme.palette.action.disabledBackground,
    position: 'relative',
    height: 'fill-available',
    fallbacks: [
      {
        height: '100vh',
      },
    ],
  },
  main: {
    height: 'fill-available',
    display: 'block',
    fallbacks: [
      {
        height: '100vh',
      },
    ],
  },
  footerCenter: {
    width: '100%',
  },
  summaryLogoWrapper: {
    display: 'none',
    textAlign: 'center',
    paddingTop: 25,
  },
  summaryWrapper: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.palette.background.default,
    alignItems: 'center',
    position: 'relative',
  },
  settingsIcon: {
    height: '2.5rem',
    width: '2.5rem',
  },
  userChip: {
    display: 'flex',
    position: 'absolute',
    alignItems: 'center',
    margin: theme.spacing(2),
    top: 0,
  },
  fixedContainer: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
  },
  summarySection: {
    paddingLeft: theme.spacing(SUMMARY_PADDING),
    paddingRight: theme.spacing(SUMMARY_PADDING),
  },
  buttonWrapper: {
    flexGrow: 0,
    borderRadius: 1000,
    backgroundColor: theme.palette.background.default,
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
  },
  settingsButton: {},
  useChipRoot: {
    marginRight: theme.spacing(1),
  },
  [theme.breakpoints.up(TWO_PANEL_BREAKPOINT)]: {
    footerCenter: {
      marginRight: 'auto',
      marginLeft: 'auto',
      width: 680,
      maxWidth: '100%',
      position: 'relative',
      paddingLeft: theme.spacing(2),
      paddingRight: theme.spacing(2),
    },
    footerRight: {
      flexGrow: 0,
      order: 3,
    },
    fixedContainer: {
      position: 'relative',
    },
    main: {
      display: 'flex',
      flexDirection: 'row-reverse',
      height: '100vh',
    },
    mapWrapper: {
      flexGrow: 10000,
      position: 'relative',
    },
    footerWrapper: {
      bottom: theme.spacing(2),
    },
    footer: {
      maxWidth: '100%',
      width: 680,
      borderRadius: 999,
    },
    summaryWrapper: {
      flexBasis: SUMMARY_WIDTH_TWO_PANEL,
      flexGrow: 1,
      overflowY: 'auto',
      overflowX: 'hidden',
    },
    summaryLogo: {
      display: 'block',
    },
    summaryLogoWrapper: {
      top: 0,
      display: 'block',
      width: '100%',
    },
  },
}));

const makeSupportedComparator = (workout: WorkoutModel) => (graph: string) =>
  PathConfiguration[graph]?.getExtension(workout);

const withAmplitude = (Component: React.FunctionComponent<WorkoutProps>) => {
  return (props: WorkoutProps) => {
    const { workout } = props;

    const amplitudeValue = {
      ActivityType: workout?.workout.activityId,
      Duration: workout ? Math.round((workout.workout.totalTime || 0) / 60) : undefined,
      Distance: workout ? Math.round(workout.workout.totalDistance) : undefined,
      TotalAscent: workout ? Math.round(workout.workout.totalAscent) : undefined,
      AverageHR: workout?.workout.hrdata.avg,
    };

    return (
      <AmplitudeContext.Provider
        value={{
          ...React.useContext(AmplitudeContext),
          ...amplitudeValue,
        }}
      >
        <Component {...props} />
      </AmplitudeContext.Provider>
    );
  };
};

const hasNativeScrollUp = () => Boolean(navigator.userAgent.match(/SamsungBrowser/i));

const winterActivities: Record<string, true | undefined> = {
  DownhillSkiing: true,
  NordicSkiing: true,
  IceHockey: true,
  IceSkating: true,
  SkiTouring: true,
  SnowShoeing: true,
  Snowboarding: true,
  TelemarkSkiing: true,
};

function Workout(props: WorkoutProps): React.ReactElement {
  const { workout, workoutLoading } = props;
  const classes = useStyles();
  const [selectedGraph, setSelectedGraph] = React.useState<string | null>(null);
  const [selectedStyle, setSelectedStyle] = React.useState<string>(MapStyles.satellite);
  const theme = useTheme();
  const summaryScrollRef = React.useRef<HTMLDivElement | null>(null);
  const mainRef = React.useRef<HTMLElement | null>(null);
  const [graphs, setGraphs] = React.useState<string[] | null>(null);
  const [showMap, setShowMap] = React.useState<boolean>(false);
  const hideScrollUp =
    useMediaQuery(theme.breakpoints.up(TWO_PANEL_BREAKPOINT)) || hasNativeScrollUp();
  const [isMenuVisible, openMenu, closeMenu] = useToggle();
  const { logEvent } = useAmplitude();

  React.useEffect(() => {
    if (workout) {
      logEvent(AmplitudeEvent.SharedWorkoutScreen);

      const activity = getActivityConfigBySTId(workout.workout.activityId);
      if (activity && winterActivities[activity.Key]) {
        setSelectedStyle(MapStyles.ski);
      }

      const graphs = getGraphs(workout, true).filter(makeSupportedComparator(workout));
      if (graphs?.length) {
        setGraphs(graphs);
        setSelectedGraph(graphs[0]);
      }
      setShowMap(workout.showMap());
    }
  }, [workout]);

  const styles = [MapStyles.satellite, MapStyles.offroad, MapStyles.ski];

  const workoutSummaryClasses = React.useMemo(
    () => ({ root: classes.summary, section: classes.summarySection }),
    [classes.summary],
  );
  return (
    <main className={classes.main} ref={mainRef}>
      <Menu open={isMenuVisible} onClose={closeMenu} />
      {showMap && (
        <div className={classes.mapWrapper}>
          <div className={classes.fixedContainer}>
            {!workoutLoading && workout && (
              <Suspense fallback={<LoadingIndicator active linear />}>
                <Mapbox graph={selectedGraph} style={selectedStyle} workout={workout} />
              </Suspense>
            )}
            <div className={classes.userChip}>
              {workout?.workout.fullname && (
                <UserChip
                  details={<WorkoutDetails workout={workout} />}
                  name={workout.workout.fullname}
                  avatar={workout.workout.userPhoto}
                  classes={{ root: classes.useChipRoot }}
                />
              )}
              <div className={classes.buttonWrapper}>
                <IconButton size="large" onClick={openMenu} className={classes.settingsButton}>
                  <SettingsIcon className={classes.settingsIcon} />
                </IconButton>
              </div>
            </div>
          </div>
          {!workoutLoading && workout && (
            <div className={classes.footerWrapper}>
              <div className={classes.footerSide}>
                <PathColorDataSelector
                  graphs={graphs}
                  onChange={setSelectedGraph}
                  value={selectedGraph}
                  workout={workout}
                  classes={{ root: classes.pathDial }}
                />
              </div>
              <div className={classes.footerRight}>
                <MapStyleSelector
                  styles={styles}
                  onChange={setSelectedStyle}
                  value={selectedStyle}
                  classes={{ root: classes.styleDial }}
                />
              </div>
              <div className={classes.footerCenter}>
                <Footer
                  classes={{ root: classes.footer }}
                  summaryScrollRef={summaryScrollRef}
                  workout={workout}
                  showMapControl={workout.workout.availableExtensions.includes(
                    WorkoutExtensionType.LocationStreamExtension,
                  )}
                />
              </div>
            </div>
          )}
        </div>
      )}
      <div className={classes.summaryWrapper}>
        <a href="https://suunto.com" target="_blank" className={classes.summaryLogoWrapper}>
          <img
            src={theme.palette.mode === 'dark' ? SuuntoLogoWhite : SuuntoLogo}
            alt="Suunto logo"
          />
        </a>
        <LoadingIndicator active={workoutLoading} />
        {!workoutLoading && workout && (
          <WorkoutSummary
            showUserChip={!showMap}
            classes={workoutSummaryClasses}
            workout={workout}
            scrollRef={summaryScrollRef}
          />
        )}
        <SiteFooter classes={{ root: classes.siteFooter }} />
      </div>
      {!hideScrollUp && <ScrollUpButton scrollToRef={mainRef} scrollTop={130} />}
    </main>
  );
}

export default React.memo(withAmplitude(Workout));
