import { Accordion, AccordionDetails, AccordionSummary, Typography } from '@mui/material';
import classNames from 'classnames';
import React from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import { ActivityIds, getActivityConfigBySTId } from '../../../activities/helper';
import { DeveloperContext } from '../../../Developer/Developer';
import { MultisportMarker } from '../../../types/WorkoutPayload';
import { getSummaryItemsByActivityName } from '../helpers';
import { hasValue } from '../SummaryItems/helpers';
import SummaryItem from '../SummaryItems/SummaryItem';
import getValue from './getValue';
import { ReactComponent as DownIcon } from '../../../images/icons/ui-navigation/chevron_down_outline.svg';

type MultisportSummarySheetProps = {
  classes?: Record<string, string>;
  multisportMarker: MultisportMarker;
};
const GRID_ITEM_PADDING = 1;
const useStyles = makeStyles(
  (theme) => ({
    root: {
      marginBottom: theme.spacing(0.5),
    },
    icon: {
      width: '2.5rem',
      height: '2.5rem',
    },
    grid: {
      display: 'flex',
      flexWrap: 'wrap',
      overflow: 'hidden',
    },
    detailsRoot: {
      paddingTop: 0,
    },
    gridItemCommon: {
      flexGrow: 1,
      width: 115,
      borderLeftWidth: 1,
      borderLeftStyle: 'solid',
      borderBottomWidth: 1,
      borderBottomStyle: 'solid',
      marginLeft: -1,
      marginBottom: -1,
    },
    gridItem: {
      padding: theme.spacing(GRID_ITEM_PADDING),
      paddingTop: 20,
      paddingBottom: 20,
      borderColor: theme.palette.divider,
    },
    placeHolder: {
      borderColor: 'transparent',
    },
  }),
  { name: 'MultisportSummarySheet' },
);

type summaryItemValue = string | number | null | undefined;
type nameValue = [string, summaryItemValue];

function MultisportSummarySheet(props: MultisportSummarySheetProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { multisportMarker } = props;
  const developer = React.useContext(DeveloperContext);
  if (!multisportMarker.Totals) return null;
  const { t } = useTranslation();

  const activityConfig =
    getActivityConfigBySTId(multisportMarker.ActivityID) ||
    getActivityConfigBySTId(ActivityIds.UNSPECIFIED);

  if (!activityConfig) {
    throw new Error('Unspecified activity not found');
  }

  const summaryItemKeys =
    getSummaryItemsByActivityName(activityConfig.Key) || getSummaryItemsByActivityName('Fallback');

  if (!summaryItemKeys) throw new Error('Fallback activity not found');

  let summaryItems: nameValue[] = summaryItemKeys.map((summaryItemKey) => [
    summaryItemKey,
    getValue(summaryItemKey, multisportMarker.Totals),
  ]);

  if (!developer) {
    summaryItems = summaryItems.filter(([name, value]) => hasValue(name, value));
  }

  return (
    <div className={classes.root}>
      <Accordion elevation={0}>
        <AccordionSummary expandIcon={<DownIcon className={classes.icon} />}>
          <Typography variant={'h2'}>{t(activityConfig?.PhraseID)}</Typography>
        </AccordionSummary>
        <AccordionDetails classes={{ root: classes.detailsRoot }}>
          <div className={classes.grid}>
            {summaryItems.map(([name, value]) => (
              <SummaryItem
                key={name}
                value={value}
                name={name}
                small
                classes={{ root: classNames(classes.gridItemCommon, classes.gridItem) }}
              />
            ))}
            {Array.from({ length: 10 }).map((v, index) => (
              <div
                key={index}
                className={classNames(classes.gridItemCommon, classes.placeHolder)}
              />
            ))}
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}

export default MultisportSummarySheet;
