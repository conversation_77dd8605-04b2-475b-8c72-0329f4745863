import { IconButton, useMediaQuery, useTheme } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Workout } from '../../models/Workout';
import SuuntoLogo from '../../images/suunto_logo.svg';
import { ReactComponent as DownIcon } from '../../images/icons/ui-navigation/chevron_down_outline.svg';
import SuuntoLogoWhite from '../../images/suunto_logo_white.svg';
import { TWO_PANEL_BREAKPOINT } from '../workoutConfig';
import MapControl from './MapControl/MapControl';

type WorkoutFooterProps = {
  workout: Workout;
  classes?: Record<string, string>;
  summaryScrollRef: React.MutableRefObject<HTMLDivElement | null>;
  showMapControl: boolean;
};

const useStyles = makeStyles((theme) => ({
  root: {
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  img: {
    height: '2rem',
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  mapControl: {
    flexGrow: 1,
    width: '900px',
    maxWidth: '100%',
  },
  downIcon: {
    height: '3rem',
    width: '3rem',
  },
  buttonDown: {
    marginBottom: -12,
  },
}));

function Footer(props: WorkoutFooterProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { summaryScrollRef, showMapControl } = props;
  const theme = useTheme();
  const { t } = useTranslation();
  const isTwoPanelViewMode = useMediaQuery(theme.breakpoints.up(TWO_PANEL_BREAKPOINT));

  if (isTwoPanelViewMode && !showMapControl) {
    return null;
  }
  return (
    <div className={classes.root}>
      {!isTwoPanelViewMode && (
        <a href="https://suunto.com" target="_blank">
          <img
            src={theme.palette.mode === 'dark' ? SuuntoLogoWhite : SuuntoLogo}
            alt="Suunto logo"
            className={classes.img}
          />
        </a>
      )}
      {showMapControl && (
        <MapControl
          useScrollableSlider={!isTwoPanelViewMode}
          classes={{ root: classes.mapControl }}
        />
      )}
      {!isTwoPanelViewMode && (
        <IconButton
          size="small"
          className={classes.buttonDown}
          onClick={() => summaryScrollRef.current?.scrollIntoView({ behavior: 'smooth' })}
          aria-label={t('JUMP_TO_SUMMARY')}
        >
          <DownIcon className={classes.downIcon} />
        </IconButton>
      )}
    </div>
  );
}

export default Footer;
