import { Link } from '@mui/material';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { Terms as TermsType } from '../parseSiteFooter';

type TermsProps = {
  classes?: Record<string, string>;
  terms?: TermsType;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      textAlign: 'center',
    },
    term: {
      marginLeft: theme.spacing(0.4),
      marginRight: theme.spacing(0.4),
      display: 'inline-block',
    },
  }),
  { name: 'Terms' },
);

function Terms(props: TermsProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { terms } = props;
  if (!terms) return null;
  return (
    <section className={classes.root}>
      {terms.map(({ title, href }) => (
        <Link
          className={classes.term}
          key={href}
          href={href}
          color="inherit"
          target="_blank"
          rel="external nofollow noopener"
        >
          {title}
        </Link>
      ))}
    </section>
  );
}

export default Terms;
