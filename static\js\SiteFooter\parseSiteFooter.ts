export interface Link {
  title: string;
  href: string;
}

export interface SomeLink extends Link {
  some: string | null;
}

export interface CopyrightItem {
  title: string;
}

export interface FooterLinkGroup<T = Link> {
  title: string;
  links: T[];
}
export type SomeLinkGroup = FooterLinkGroup<SomeLink>;
export type Terms = Link[];

export interface ParseResponse {
  someLinks: FooterLinkGroup<SomeLink>;
  linkGroups: FooterLinkGroup<Link>[];
  terms: Terms;
  copyrights: CopyrightItem[];
}
export const SUUNTO_BASE_URL = 'https://www.suunto.com';

const getFooter = (htmlString: string): HTMLElement | null => {
  const doc = document.implementation.createHTMLDocument();
  const html = doc.createElement('html');
  html.innerHTML = htmlString;
  const base = doc.createElement('base');
  base.href = SUUNTO_BASE_URL;
  doc.getElementsByTagName('head')[0].appendChild(base);
  return html.querySelector('footer.footer');
};

const getSomeLinks = (footer: Element | null): SomeLinkGroup => {
  const someContainer = footer?.querySelector<HTMLElement>('.footer__social-networks');

  const title = someContainer?.querySelector<HTMLParagraphElement>('p')?.textContent || '';

  const linkElements = Array.from(
    someContainer?.querySelectorAll<HTMLAnchorElement>('a[data-footer-some-link]') || [],
  );
  const links = linkElements.map(
    (el): SomeLink => ({
      href: el.href,
      title: el.title,
      some: el.getAttribute('data-footer-some-link'),
    }),
  );
  return {
    title,
    links,
  };
};

const getGroup = (groupEl: HTMLDivElement): FooterLinkGroup => ({
  links: Array.from(groupEl.querySelectorAll<HTMLAnchorElement>('.list__item a')).map(
    (el): Link => ({ href: el.href, title: (el.textContent || '').trim() }),
  ),
  title: (groupEl.querySelector('.list__title')?.textContent || '').trim(),
});

const getLinkGroups = (footer: Element | null): FooterLinkGroup[] => {
  const groups = Array.from(
    footer?.querySelectorAll<HTMLDivElement>('.footer__links-container') || [],
  );
  return groups.map(getGroup);
};

const getTerms = (footer: Element | null): Link[] => {
  const termElements = Array.from(
    footer?.querySelectorAll<HTMLAnchorElement>('.footer__bottom-page a') || [],
  );
  return termElements.map((el): Link => ({ href: el.href, title: (el.textContent || '').trim() }));
};
const getCopyrights = (footer: Element | null): CopyrightItem[] => {
  const termElements = Array.from(
    footer?.querySelectorAll<HTMLElement>('.footer__bottom-page span') || [],
  );
  return termElements.map((el): CopyrightItem => ({ title: (el.textContent || '').trim() }));
};

export default (htmlString: string): ParseResponse => {
  const footer = getFooter(htmlString);
  const someLinks = getSomeLinks(footer);
  const linkGroups = getLinkGroups(footer);
  const terms = getTerms(footer);
  const copyrights = getCopyrights(footer);

  return {
    copyrights,
    someLinks,
    linkGroups,
    terms,
  };
};
