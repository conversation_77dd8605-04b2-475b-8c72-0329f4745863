import { Drawer, useTheme } from '@mui/material';
import React from 'react';
import { makeStyles } from '@mui/styles';
import SuuntoLogo from '../images/suunto_logo.svg';
import SuuntoLogoWhite from '../images/suunto_logo_white.svg';
import Preferences from '../Preferences/Preferences';

type MenuProps = {
  classes?: Record<string, string>;
  open: boolean;
  onClose: () => void;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {},
    logo: {
      display: 'flex',
      justifyContent: 'center',
    },
    paper: {
      padding: theme.spacing(2),
      width: 300,
      maxWidth: '100%',
      background: theme.palette.background.default,
    },
    preferences: {
      marginTop: theme.spacing(1),
      display: 'flex',
      flexDirection: 'column',
    },
  }),
  { name: 'Menu' },
);

function Menu(props: MenuProps): React.ReactElement {
  const { open, onClose } = props;
  const classes = useStyles(props);
  const theme = useTheme();

  return (
    <Drawer
      classes={{ root: classes.root, paper: classes.paper }}
      open={open}
      anchor="left"
      onClose={onClose}
    >
      <a href="https://suunto.com" target="_blank" className={classes.logo}>
        <img src={theme.palette.mode === 'dark' ? SuuntoLogoWhite : SuuntoLogo} alt="Suunto logo" />
      </a>
      <section className={classes.preferences}>
        <Preferences />
      </section>
    </Drawer>
  );
}

export default Menu;
