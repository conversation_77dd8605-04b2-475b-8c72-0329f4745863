import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import AliceCarousel from '../../AliceCarousel';
import { renderDotsItem } from '../../AliceCarousel/DotsItem';
import NavButton from '../../AliceCarousel/NavButton';
import { AmplitudeContext } from '../../Amplitude/Amplitude';
import noOp from '../../helpers/noOp';
import { useLoading } from '../../helpers/useLoading';
import {
  Culture,
  fetchPopularProducts,
  loadCulture,
  PopularProduct as PopularProductType,
} from '../../api';
import { languages } from './languages';
import PopularProduct from './PopularProduct';

type PopularProductsProps = {
  classes?: Record<string, string>;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      marginTop: theme.spacing(2),
      '& .alice-carousel__dots': {
        height: theme.spacing(4.5),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: theme.spacing(4.5),
        marginLeft: theme.spacing(4.5),
        marginBottom: 0,
      },
      [['& .alice-carousel__prev-btn', '& .alice-carousel__next-btn'].join(',')]: {
        width: 0,
        top: 0,
        position: 'absolute',
        padding: 0,
        display: 'flex',
        alignItems: 'center',
        height: '100%',
      },
      '& .alice-carousel__prev-btn': {
        left: 0,
      },
      '& .alice-carousel__next-btn': {
        left: '100%',
        width: 'auto',
      },
      '& svg': {
        width: '2.8rem',
        height: '2.8rem',
      },
      overflowX: 'hidden',
    },
    product: {
      margin: 'auto',
      maxWidth: 350,
      width: 'calc(100% - 10rem)',
    },
  }),
  { name: 'PopularProducts' },
);

function PopularProducts(props: PopularProductsProps): React.ReactElement | null {
  const classes = useStyles(props);
  const [popularProducts, setPopularProducts] = React.useState<PopularProductType[] | null>(null);
  const [isLoading, setLoading] = useLoading(true);
  const amplitudeValue = React.useContext(AmplitudeContext);
  const [showPrices, setShowPrices] = React.useState<boolean>(true);

  React.useEffect(() => {
    loadCulture()
      .then(
        ({ AutoDetectedCulture: culture }: Culture) =>
          !languages[culture.toLowerCase()] && setShowPrices(false),
      )
      .catch(noOp);
  }, []);

  React.useEffect(() => {
    fetchPopularProducts().then(setPopularProducts).catch(noOp).finally(setLoading());
  }, []);

  const renderProduct = (popularProduct: PopularProductType, i = 0) => (
    <AmplitudeContext.Provider
      key={popularProduct.Url}
      value={{ ...amplitudeValue, PlaceInProductCarousel: i + 1 }}
    >
      <PopularProduct
        showPrice={showPrices}
        value={popularProduct}
        classes={{ root: classes.product }}
      />
    </AmplitudeContext.Provider>
  );

  if (isLoading) return null;
  if (!popularProducts?.length) return null;
  return (
    <section className={classes.root}>
      <React.Suspense fallback={renderProduct(popularProducts[0])}>
        <AliceCarousel
          keyboardNavigation
          infinite
          renderDotsItem={renderDotsItem}
          renderNextButton={({ isDisabled }) => <NavButton disabled={isDisabled} next />}
          renderPrevButton={({ isDisabled }) => <NavButton disabled={isDisabled} prev />}
          items={popularProducts.slice(0, 5).map(renderProduct)}
        />
      </React.Suspense>
    </section>
  );
}

export default PopularProducts;
