var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgAltitudeDownhillOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M10.1898 25.297L10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.2906L14.4249 34.8619C14.8668 34.3316 15.6551 34.2599 16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.2906L9.0676 26.5405C9.0676 25.8933 9.55947 25.361 10.1898 25.297L10.3176 25.2905L10.1898 25.297ZM33.086 8.14544C33.3157 8.29441 33.5183 8.48054 33.6859 8.69578L33.8049 8.86254L44.7299 25.6142L42.6359 26.9798L31.7529 10.2926L25.7087 19.6107L23.4672 15.8748L16.3811 26.9762L14.2738 25.6311L21.4033 14.4616C22.1313 13.321 23.6461 12.9866 24.7867 13.7146C25.053 13.8846 25.2835 14.1043 25.4658 14.3609L25.5693 14.5193L25.7914 14.8893L29.6973 8.86763C30.4336 7.73243 31.9508 7.4091 33.086 8.14544Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgAltitudeDownhillOutline);
export default __webpack_public_path__ + "static/media/altitude_downhill_outline.843e0345.svg";
export { ForwardRef as ReactComponent };