var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgHeartPulseOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 44",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24.1898 10.6164L24 10.8536L23.8102 10.6164C21.8708 8.26392 18.6894 6.5 15.25 6.5C13.2739 6.5 10.7944 7.08996 8.74021 8.3834C5.75217 10.2648 4 13.3728 4 17.75C4 26.7204 13.6749 36.985 23.441 41.868C23.7929 42.044 24.2071 42.044 24.559 41.868C34.3251 36.985 44 26.7204 44 17.75C44 13.3728 42.2478 10.2648 39.2598 8.3834C37.2056 7.08996 34.7261 6.5 32.75 6.5C29.3106 6.5 26.1292 8.26392 24.1898 10.6164ZM37.1973 28.25C33.9284 32.5685 29.1865 36.5565 24.3674 39.151L24 39.346L23.6326 39.151C18.8135 36.5565 14.0716 32.5685 10.8027 28.25H18C18.4179 28.25 18.8082 28.0412 19.0401 27.6934L19.5167 26.9785L21.794 35.3289C21.9429 35.8748 22.4398 36.2525 23.0056 36.25C23.5713 36.2475 24.0649 35.8653 24.2088 35.3181L28.0216 20.8298L29.794 27.3289C29.9424 27.8728 30.4363 28.25 31 28.25H37.1973ZM38.8975 25.75H31.9547L29.206 15.6711C29.0571 15.1253 28.5602 14.7475 27.9944 14.75C27.4287 14.7526 26.9351 15.1348 26.7912 15.6819L22.9784 30.1702L21.206 23.6711C21.077 23.1984 20.6839 22.8447 20.2002 22.7662C19.7166 22.6877 19.2317 22.899 18.9599 23.3067L17.331 25.75H9.10253C7.47462 23.0498 6.5 20.3015 6.5 17.75C6.5 11.6211 10.6628 9 15.25 9C18.624 9 21.7593 11.2861 22.8577 13.7577C23.2976 14.7474 24.7024 14.7474 25.1423 13.7577C26.2407 11.2861 29.376 9 32.75 9C37.3372 9 41.5 11.6211 41.5 17.75C41.5 20.3015 40.5254 23.0498 38.8975 25.75Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgHeartPulseOutline);
export default __webpack_public_path__ + "static/media/heart_pulse_outline.4be5d7e3.svg";
export { ForwardRef as ReactComponent };