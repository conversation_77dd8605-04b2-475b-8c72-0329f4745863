import { ActivityIds } from '../../../activities/helper';
import { Workout } from '../../../models/Workout';

const avgSpeed = (workout: Workout) => {
  let result;
  if (workout.workout.activityId === ActivityIds.SWIMMING) {
    result = workout.getSummary()?.avgSpeed;
  }
  if (!result) {
    result = workout.workout.avgSpeed;
  }
  return result;
};

const maxSpeed = (workout: Workout) => workout.workout.maxSpeed;

const unknownValue = () => undefined;

const resolveValueMap: Record<string, (workout: Workout) => number | string | undefined | null> = {
  Duration: ({ workout }) => workout.totalTime,
  Distance: ({ workout }) => workout.totalDistance,
  AvgPace: avgSpeed,
  AvgHeartrate: ({ workout }: Workout) => workout.hrdata.workoutAvgHR,
  MaxHeartRate: ({ workout }: Workout) => workout.hrdata.workoutMaxHR,
  Energy: ({ workout }) => workout.energyConsumption,
  RecoveryTime: (workout) => workout.getSummary()?.recoveryTime,
  Pte: (workout) => workout.getSummary()?.pte,
  PerformanceLevel: (workout) => workout.getSummary()?.performanceLevel,
  AvgSpeed: avgSpeed,
  AvgCadence: (workout) => workout.getSummary()?.avgCadence,
  Steps: ({ workout }) => workout.stepCount,
  AscentAltitude: (workout) => workout.getSummary()?.ascent,
  DescentAltitude: (workout) => workout.getSummary()?.descent,
  HighAltitude: ({ workout }) => workout.maxAltitude,
  LowAltitude: ({ workout }) => workout.minAltitude,
  AvgTemperature: (workout: Workout) => workout.getSummary()?.avgTemperature,
  PeakEpoc: (workout: Workout) => workout.getSummary()?.peakEpoc,
  Feeling: (workout: Workout) => workout.getSummary()?.feeling,
  MoveType: unknownValue,
  'Catch:Fish': unknownValue,
  'Catch:BigGame': unknownValue,
  'Catch:SmallGame': unknownValue,
  'Catch:Bird': unknownValue,
  'Catch:ShotCount': unknownValue,
  AvgPower: (workout: Workout) => workout.getSummary()?.avgPower,
  AvgSWOLF: (workout) => workout.getSwimmingHeaderExtension()?.avgSwolf,
  AvgNauticalSpeed: avgSpeed,
  MaxNauticalSpeed: maxSpeed,
  NauticalDistance: ({ workout }) => workout.totalDistance,
  MaxSpeed: maxSpeed,
  MaxDepth: (workout) => workout.getDiveHeaderExtension()?.maxDepth,
  DiveTime: (workout) => {
    let diveTime = workout.workout.totalTime;
    if (diveTime) {
      const pauseDuration = workout.getDiveHeaderExtension()?.pauseDuration;
      if (pauseDuration) {
        diveTime = diveTime - pauseDuration;
      }
    }
    return diveTime;
  },
  DiveMode: (workout) => workout.getDiveHeaderExtension()?.diveMode,
  DiveNumberInSeries: (workout) => workout.getDiveHeaderExtension()?.diveNumberInSeries,
  DiveSurfaceTime: (workout) => workout.getDiveHeaderExtension()?.surfaceTime,
  DiveVisibility: unknownValue,
  DiveMaxDepthTemperature: (workout) => workout.getDiveHeaderExtension()?.maxDepthTemperature,
  SkiRuns: (workout) => workout.getSkiExtension()?.statistics.numberOfRuns,
  SkiDistance: (workout) => workout.getSkiExtension()?.statistics.descentDistanceMeters,
  SkiTime: (workout) => workout.getSkiExtension()?.statistics.descentDurationSeconds,
  AvgSkiSpeed: (workout) => workout.getSkiExtension()?.statistics.avgSpeed,
  MaxSkiSpeed: (workout) => workout.getSkiExtension()?.statistics.maxSpeed,
  AscentTime: (workout: Workout) => workout.getSummary()?.ascentTime,
  DescentTime: (workout: Workout) => workout.getSummary()?.descentTime,
  EstVO2peak: (workout) => workout.getFitness()?.estimatedVo2Max,
};

export default (item: string, workout: Workout): number | string => {
  let value: any;
  const summary = workout.getSummary();
  if (summary) {
    const summaryItemName = item.charAt(0).toLowerCase() + item.slice(1);
    value = summary[summaryItemName];
  }
  if (typeof value === 'undefined' && resolveValueMap[item]) {
    value = resolveValueMap[item](workout);
  }
  return value;
};
