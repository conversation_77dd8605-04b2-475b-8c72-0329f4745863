var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgFeeling3Fill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24C6 33.9415 14.0592 42 24.0004 42ZM32.5978 26.7468C32.9309 27.2215 32.8161 27.8764 32.3413 28.2094C27.3504 31.711 20.6713 31.711 15.6804 28.2094C15.2057 27.8764 15.0908 27.2215 15.4239 26.7468C15.7569 26.2721 16.4118 26.1573 16.8865 26.4903C21.1535 29.484 26.8682 29.484 31.1352 26.4903C31.61 26.1573 32.2648 26.2721 32.5978 26.7468ZM28.8 19.65V18.1483C28.8 17.4036 29.4044 16.8 30.15 16.8C30.8956 16.8 31.5 17.4036 31.5 18.1483V19.65C31.5 20.3807 30.8956 20.9843 30.15 21C29.4044 20.9843 28.8 20.3807 28.8 19.65ZM16.5 19.65C16.5 20.3807 17.1044 20.9843 17.85 20.9843C18.5956 20.9843 19.2 20.3807 19.2 19.65V18.1483C19.2 17.4036 18.5956 16.8 17.85 16.8C17.1044 16.8 16.5 17.4036 16.5 18.1483V19.65Z",
    fill: "#FFDD33"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgFeeling3Fill);
export default __webpack_public_path__ + "static/media/feeling_3_fill.1f63aae0.svg";
export { ForwardRef as ReactComponent };