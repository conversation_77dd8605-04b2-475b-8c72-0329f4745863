import { Fab, Zoom } from '@mui/material';
import React from 'react';
import { emphasize } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import { ReactComponent as CloseIcon } from '../../images/icons/ui-navigation/close_fill.svg';

type ScrollUpButtonProps = {
  classes?: Record<string, string>;
  scrollToRef: React.MutableRefObject<HTMLElement | null>;
  scrollTop: number;
};

const throttle = (fn: () => void, time = 200) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return () => {
    if (!timeout) {
      timeout = setTimeout(() => {
        fn();
        timeout = null;
      }, time);
    }
  };
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      bottom: 35,
      zIndex: 10,
      marginLeft: '50%',
      marginRight: '50%',
      transform: 'translateX(-50%)',
      position: 'fixed',
    },
    scrollUp: {
      color: theme.palette.text.secondary,
      backgroundColor: theme.palette.background.default,
      '&:hover': {
        backgroundColor: emphasize(theme.palette.background.default, 0.15),
      },
      transition: `${theme.transitions.create('transform', {
        duration: theme.transitions.duration.shorter,
      })}, opacity 0.8s`,
      opacity: 1,
    },
    icon: {
      height: '2.5rem',
      width: '2.5rem',
    },
  }),
  { name: 'ScrollUpButton' },
);

function ScrollUpButton(props: ScrollUpButtonProps): React.ReactElement {
  const classes = useStyles(props);
  const { scrollToRef, scrollTop } = props;
  const [showBackToTopFab, setShowBackToTopFab] = React.useState<boolean>(false);

  React.useEffect(() => {
    const onScroll = throttle(() => {
      setShowBackToTopFab(window.scrollY > scrollTop);
    }, 150);
    onScroll();
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [scrollTop]);

  const onClick = () => scrollToRef.current?.scrollIntoView({ behavior: 'smooth' });

  return (
    <div className={classes.root}>
      <Zoom in={showBackToTopFab} timeout={200} unmountOnExit>
        <Fab className={classes.scrollUp} size="medium" onClick={onClick}>
          <CloseIcon className={classes.icon} />
        </Fab>
      </Zoom>
    </div>
  );
}

export default ScrollUpButton;
