import { FormControl, FormLabel, Typography } from '@mui/material';
import React from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import namespaces from '../i18n/namespaces';
import { ReactComponent as I18nIcon } from '../images/icons/ui-general/icons/explore_outline-1.svg';
import LanguageSelect from './LanguageSelect';
import MeasurementSystemSelect from './MeasurementSystemSelect';

type PreferencesProps = {
  classes?: Record<string, string>;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {},
    item: {
      marginTop: theme.spacing(2),
      marginBottom: theme.spacing(2),
    },
    iconAndInput: {
      marginTop: theme.spacing(2),
      display: 'flex',
      alignItems: 'center',
    },
    icon: {
      height: '3rem',
      width: '3rem',
      marginRight: theme.spacing(2),
    },
    input: {
      flexGrow: 1,
    },
  }),
  { name: 'Preferences' },
);

const useIDs = makeStyles({ languageSelect: {}, toggleButton: {} }, { name: 'PreferencesIds' });

function Preferences(props: PreferencesProps): React.ReactElement {
  const classes = useStyles(props);
  const { t } = useTranslation([namespaces.PHRASES]);
  const ids = useIDs();

  return (
    <>
      <FormControl className={classes.item}>
        <FormLabel component="legend" htmlFor={ids.languageSelect}>
          <Typography variant="h5">{t('TXT_LANGUAGE')}</Typography>
        </FormLabel>
        <div className={classes.iconAndInput}>
          <I18nIcon className={classes.icon} />
          <LanguageSelect
            id={ids.languageSelect}
            classes={{
              root: classes.input,
            }}
          />
        </div>
      </FormControl>
      <FormControl className={classes.item} component="fieldset">
        <FormLabel component="legend" htmlFor={ids.toggleButton}>
          <Typography variant="h5">{t('TXT_UNITS')}</Typography>
        </FormLabel>
        <div className={classes.iconAndInput}>
          <I18nIcon className={classes.icon} />
          <MeasurementSystemSelect
            id={ids.toggleButton}
            classes={{
              root: classes.input,
            }}
          />
        </div>
      </FormControl>
    </>
  );
}

export default Preferences;
