import { Link, Typography } from '@mui/material';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { FooterLinkGroup } from '../parseSiteFooter';

type GroupsProps = {
  classes?: Record<string, string>;
  linkGroups?: FooterLinkGroup[];
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      display: 'flex',
      flexWrap: 'wrap',
    },
    group: {
      padding: theme.spacing(0.8),
      flexBasis: 120,
      flexGrow: 1,
    },
    title: {},
    list: {
      listStyle: 'none',
      padding: 0,
    },
    item: {
      marginBottom: theme.spacing(0.6),
    },
  }),
  { name: 'Groups' },
);

function Groups(props: GroupsProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { linkGroups } = props;
  if (!linkGroups) return null;

  return (
    <section className={classes.root}>
      {linkGroups.map(({ title, links }) => (
        <div key={title} className={classes.group}>
          <Typography component="h2" variant="h5" className={classes.title}>
            <strong>{title}</strong>
          </Typography>
          <ul className={classes.list}>
            {links.map(({ href, title }) => (
              <li key={href} className={classes.item}>
                <Link
                  href={href}
                  color="inherit"
                  target="_blank"
                  rel="external nofollow noopener"
                  underline="none"
                >
                  {title}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </section>
  );
}

export default Groups;
