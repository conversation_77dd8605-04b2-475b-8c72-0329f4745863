import makeStyles from '@mui/styles/makeStyles';
import React from 'react';
import TextPlaceholder from './TextPlaceholder';

const usePricePlaceholderStyles = makeStyles({
  span: {
    width: '4rem',
    height: '1.6rem',
  },
  span2: {
    width: '2.5rem',
  },
});

function PricePlaceholder(): React.ReactElement {
  const classes = usePricePlaceholderStyles();
  return (
    <>
      <TextPlaceholder classes={{ root: classes.span }} />{' '}
      <TextPlaceholder classes={{ root: [classes.span2, classes.span].join(' ') }} />
    </>
  );
}

export default PricePlaceholder;
