import { Map, CustomLayerInterface, MercatorCoordinate } from '!mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

import { scale } from '../helpers/math';

function buildMatrix(a: number[], b: number[], c: number[], d: number[]) {
  return a.concat(b, c, d);
}

function multiplyMat4Vec4(m: number[], [x, y, z, w]: number[]) {
  return [
    x * m[0] + y * m[4] + z * m[8] + w * m[12],
    x * m[1] + y * m[5] + z * m[9] + w * m[13],
    x * m[2] + y * m[6] + z * m[10] + w * m[14],
    x * m[3] + y * m[7] + z * m[11] + w * m[15],
  ];
}

function multiplyMat4Mat4(a: number[], b: number[]) {
  return buildMatrix(
    multiplyMat4Vec4(a, b.slice(0, 4)),
    multiplyMat4Vec4(a, b.slice(4, 8)),
    multiplyMat4Vec4(a, b.slice(8, 12)),
    multiplyMat4Vec4(a, b.slice(12, 16)),
  );
}

class Renderer {
  constructor(gl: WebGLRenderingContext, vert: string, frag: string) {
    const vertexShader = gl.createShader(gl.VERTEX_SHADER);
    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);

    if (!vertexShader || !fragmentShader) throw new Error();

    gl.shaderSource(vertexShader, vert);
    gl.compileShader(vertexShader);

    if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
      throw new Error('' + gl.getShaderInfoLog(vertexShader));
    }

    gl.shaderSource(fragmentShader, frag);
    gl.compileShader(fragmentShader);

    if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
      throw new Error('' + gl.getShaderInfoLog(fragmentShader));
    }

    this.program = gl.createProgram();
    if (!this.program) throw new Error();

    gl.attachShader(this.program, vertexShader);
    gl.attachShader(this.program, fragmentShader);
    gl.linkProgram(this.program);

    if (!gl.getProgramParameter(this.program, gl.LINK_STATUS)) {
      throw new Error('' + gl.getProgramInfoLog(this.program));
    }

    gl.validateProgram(this.program);

    this.uMatrix = gl.getUniformLocation(this.program, 'uMatrix');
    this.uCamera = gl.getUniformLocation(this.program, 'uCamera');
    this.uOffset = gl.getUniformLocation(this.program, 'uOffset');
    this.uThickness = gl.getUniformLocation(this.program, 'uThickness');
    this.uAlpha = gl.getUniformLocation(this.program, 'uAlpha');
    this.uDist = gl.getUniformLocation(this.program, 'uDist');

    this.aPos = gl.getAttribLocation(this.program, 'aPos');
    this.aDist = gl.getAttribLocation(this.program, 'aDist');

    this.buffer = gl.createBuffer();
    this.distBuffer = gl.createBuffer();
  }

  update(gl: WebGLRenderingContext, data: Float32Array, count: number) {
    gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer);
    gl.bufferData(gl.ARRAY_BUFFER, data, gl.STATIC_DRAW);

    this.count = count;
  }

  updateDist(gl: WebGLRenderingContext, data: Float32Array) {
    gl.bindBuffer(gl.ARRAY_BUFFER, this.distBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, data, gl.STATIC_DRAW);
  }

  program: WebGLProgram | null;
  buffer: WebGLBuffer | null;
  distBuffer: WebGLBuffer | null;

  uMatrix: WebGLUniformLocation | null;
  uCamera: WebGLUniformLocation | null;
  uOffset: WebGLUniformLocation | null;
  uThickness: WebGLUniformLocation | null;
  uAlpha: WebGLUniformLocation | null;
  uDist: WebGLUniformLocation | null;

  aPos: number;
  aDist: number;

  count = 0;
}

class LineRenderer extends Renderer {
  constructor(gl: WebGLRenderingContext) {
    const vert = `
    precision highp float;

    uniform mat4 uMatrix;
    uniform float uThickness;
    uniform vec4 uCamera;
    uniform vec2 uOffset;
    attribute vec4 aPos;
    attribute float aDist;
    varying float vItem;
    varying float vAlpha;
    varying float vDist;

    const vec3 alt = vec3(0.0, 0.0, 0.0000002);

    void main() {
      vec3 pos = aPos.xyz;

      float item = aPos.w;
      float side = -1.0;

      if(item < 3.0) {
        item -= 2.0;
      } else {
        side = 1.0;
        item -= 3.0;
      }

      vItem = item;
      vDist = aDist;

      pos += alt;
      pos += normalize(uCamera.xyz - pos) * 0.000001;

      vec4 xyPos = uMatrix * vec4(pos, 1.0);

      xyPos += vec4(uOffset, 0.0, 0.0) * side * xyPos.w * uThickness;
      vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), length(uCamera.xyz - pos) / uCamera.z);

      gl_Position = xyPos;
    }
    `;

    const frag = `
    precision highp float;

    uniform float uAlpha;
    uniform float uDist;
    varying float vItem;
    varying float vAlpha;
    varying float vDist;

    void main() {
      if(vDist > uDist) discard;
      if(vItem == 1.0) {
        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;
      } else {
        gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;
      }
    }
    `;

    super(gl, vert, frag);
  }

  render(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[],
  ) {
    const count = this.count;

    gl.useProgram(this.program);

    gl.uniformMatrix4fv(this.uMatrix, false, matrix);

    gl.uniform1f(this.uThickness, thickness);

    if (camera) gl.uniform4fv(this.uCamera, camera);

    gl.enableVertexAttribArray(this.aPos);
    gl.enableVertexAttribArray(this.aDist);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer);
    gl.vertexAttribPointer(this.aPos, 4, gl.FLOAT, false, 0, 0);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.distBuffer);
    gl.vertexAttribPointer(this.aDist, 1, gl.FLOAT, false, 0, 0);

    gl.uniform1f(this.uAlpha, 1);
    gl.uniform1f(this.uDist, position);

    gl.uniform2f(this.uOffset, Math.SQRT2 / 2, Math.SQRT2 / 2);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);

    gl.uniform2f(this.uOffset, Math.SQRT2 / 2, -Math.SQRT2 / 2);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);

    gl.uniform2f(this.uOffset, 1, 0);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);

    gl.uniform2f(this.uOffset, 0, 1);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);

    gl.disable(gl.DEPTH_TEST);
    gl.uniform1f(this.uAlpha, 0.125);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
    gl.enable(gl.DEPTH_TEST);
  }
}

class PointRenderer extends Renderer {
  constructor(gl: WebGLRenderingContext) {
    const vert = `
    precision highp float;

    uniform mat4 uMatrix;
    uniform float uThickness;
    uniform vec4 uCamera;
    uniform vec2 uOffset;
    attribute vec4 aPos;
    attribute float aDist;

    varying float vItem;
    varying float vAlpha;
    varying vec2 vDelta;
    varying float vDist;

    const vec3 alt = vec3(0.0, 0.0, 0.0000002);

    void main() {
      vec3 pos = aPos.xyz;

      float item = aPos.w;
      vec2 delta;

      if(item < 3.0) {
        item -= 2.0;
        delta = vec2(-1.0, -1.0);
      } else if(item < 4.0) {
        item -= 3.0;
        delta = vec2(-1.0, 1.0);
      } else if(item < 5.0) {
        item -= 4.0;
        delta = vec2(1.0, -1.0);
      } else {
        item -= 5.0;
        delta = vec2(1.0, 1.0);
      }

      vItem = item;
      vDelta = delta;
      vDist = aDist;

      pos += alt;
      pos += normalize(uCamera.xyz - pos) * 0.000001;

      vec4 xyPos = uMatrix * vec4(pos, 1.0);
      vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), length(uCamera.xyz - pos) / uCamera.z);

      xyPos += vec4(uOffset * delta, 0.0, 0.0) * xyPos.w * uThickness;

      gl_Position = xyPos;
    }
    `;

    const frag = `
    precision highp float;

    uniform float uAlpha;
    uniform float uDist;

    varying float vItem;
    varying float vAlpha;
    varying float vDist;
    varying vec2 vDelta;

    void main() {
      if(vDist > uDist) discard;
      if(length(vDelta) > 1.0) discard;
      if(vItem == 1.0) {
        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;
      } else {
        gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;
      }
    }
    `;

    super(gl, vert, frag);
  }

  render(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[],
  ) {
    const count = this.count;

    gl.useProgram(this.program);

    gl.uniformMatrix4fv(this.uMatrix, false, matrix);

    gl.uniform1f(this.uThickness, thickness);

    if (camera) gl.uniform4fv(this.uCamera, camera);

    gl.enableVertexAttribArray(this.aPos);
    gl.enableVertexAttribArray(this.aDist);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer);
    gl.vertexAttribPointer(this.aPos, 4, gl.FLOAT, false, 0, 0);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.distBuffer);
    gl.vertexAttribPointer(this.aDist, 1, gl.FLOAT, false, 0, 0);

    gl.uniform1f(this.uAlpha, 1);
    gl.uniform1f(this.uDist, position);

    gl.uniform2f(this.uOffset, 1, 1);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 6);
  }
}

export class LineLayer implements CustomLayerInterface {
  constructor(id: string, private pts: number[][]) {
    this.id = id;
    this.pts = pts;
  }

  onAdd(map: Map, gl: WebGLRenderingContext): void {
    this.map = map;

    this.lineData = new Float32Array(this.pts.length * 2 * 4);
    this.pointData = new Float32Array(this.pts.length * 6 * 4);
    this.lineDistData = new Float32Array(this.pts.length * 2);
    this.pointDistData = new Float32Array(this.pts.length * 6);

    let latMin = Infinity;
    let latMax = -Infinity;
    let lonMin = Infinity;
    let lonMax = -Infinity;

    for (const pt of this.pts) {
      const [lat, lon] = pt;

      if (lon < lonMin) lonMin = lon;
      if (lon > lonMax) lonMax = lon;
      if (lat < latMin) latMin = lat;
      if (lat > latMax) latMax = lat;
    }

    this.xyzMin = MercatorCoordinate.fromLngLat({ lng: lonMin, lat: latMin });
    this.xyzMax = MercatorCoordinate.fromLngLat({ lng: lonMax, lat: latMax });

    this.lineRenderer = new LineRenderer(gl);
    this.pointRenderer = new PointRenderer(gl);

    this.updateData(true);
  }

  updateData(updateAll?: boolean): void {
    const lineData = this.lineData;
    const lineDistData = this.lineDistData;
    const pointData = this.pointData;
    const pointDistData = this.pointDistData;
    const map = this.map;

    if (!lineData || !lineDistData || !pointData || !pointDistData || !map) return;

    const count = this.pts.length;
    const tick = ++this.tick;

    if (count < 100) updateAll = true;

    const batches: { dist: number; first: number; last: number }[] = [];
    const batchCount = updateAll ? 1 : 30;
    const ll = map.getCenter();

    for (let batchNum = 0; batchNum < batchCount; ++batchNum) {
      let latMin = Infinity;
      let latMax = -Infinity;
      let lonMin = Infinity;
      let lonMax = -Infinity;

      const first = ~~((count * batchNum) / batchCount);
      const last = ~~((count * (batchNum + 1)) / batchCount);

      for (let num = first; num < last; ++num) {
        const pt = this.pts[num];

        if (pt[0] < latMin) latMin = pt[0];
        if (pt[0] > latMax) latMax = pt[0];
        if (pt[1] < lonMin) lonMin = pt[1];
        if (pt[1] > lonMax) lonMax = pt[1];
      }

      const dlat = ll.lat - (latMin + latMax) / 2;
      const dlon = ll.lng - (lonMin + lonMax) / 2;
      const dist = dlat * dlat + dlon * dlon;

      batches.push({ dist, first, last });
    }

    batches.sort((a, b) => a.dist - b.dist);
    let changed = false;

    // Update nearest batch, one among the next nearest 4, and one among the rest.
    for (let i = 0; i < 3; ++i) {
      let batchNum = 0;

      if (i == 1) {
        if (updateAll) break;
        batchNum = 1 + (tick % 4);
      } else if (i == 2) {
        batchNum = 5 + (tick % (batchCount - 5));
      }

      const batch = batches[batchNum];
      const first = batch.first;
      const last = batch.last;

      let p = first * 8;
      let q = first * 24;

      for (let num = first; num < last; ++num) {
        const pt = this.pts[num];
        const ll = {
          lng: pt[1],
          lat: pt[0],
        };

        let elevation = map.queryTerrainElevation(ll, { exaggerated: true });
        if (updateAll && (!elevation || elevation <= 0)) elevation = 1;
        const xyz = MercatorCoordinate.fromLngLat(ll, elevation || 0);

        for (let j = 0; j < 2; ++j) {
          lineDistData[num * 2 + j] = pt[4];
          pointDistData[num * 6 + j] = pt[4];

          lineData[p++] = xyz.x - this.xyzMin.x;
          lineData[p++] = xyz.y - this.xyzMin.y;
          if (typeof elevation == 'number') {
            lineData[p++] = xyz.z || 0;
          } else {
            ++p;
          }
          lineData[p++] = pt[2] / 255 + 2 + j;
        }

        pointData[q++] = xyz.x - this.xyzMin.x;
        pointData[q++] = xyz.y - this.xyzMin.y;
        if (typeof elevation == 'number') {
          if (pointData[q] != (xyz.z || 0)) changed = true;
          pointData[q++] = xyz.z || 0;
        } else {
          ++q;
        }
        pointData[q++] = pt[2] / 255 + 2;

        for (let j = 0; j < 4; ++j) {
          pointDistData[num * 6 + 2 + j] = pt[4];

          pointData[q++] = xyz.x - this.xyzMin.x;
          pointData[q++] = xyz.y - this.xyzMin.y;
          if (typeof elevation == 'number') {
            pointData[q++] = xyz.z || 0;
          } else {
            ++q;
          }
          pointData[q++] = pt[2] / 255 + j;
        }

        pointData[q++] = xyz.x - this.xyzMin.x;
        pointData[q++] = xyz.y - this.xyzMin.y;
        if (typeof elevation == 'number') {
          pointData[q++] = xyz.z || 0;
        } else {
          ++q;
        }
        pointData[q++] = pt[2] / 255 + 5;
      }
    }

    if (map && changed) map.triggerRepaint();
  }

  render(gl: WebGLRenderingContext, matrix: number[]): void {
    matrix = multiplyMat4Mat4(
      matrix,
      buildMatrix(
        [1, 0, 0, 0], //
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [this.xyzMin.x, this.xyzMin.y, 0, 1],
      ),
    );

    const scaleFactor = 1 / Math.max.apply(null, matrix.map(Math.abs));

    for (let i = 0; i < 16; ++i) {
      matrix[i] *= scaleFactor;
    }

    const zoom = this.map && this.map.getZoom();
    const xyz = this.map && this.map.getFreeCameraOptions().position;
    const pitch = this.map && Math.tan((this.map.getPitch() / 180) * Math.PI);
    let camera: number[] | undefined;

    if (xyz) {
      camera = [xyz.x - this.xyzMin.x, xyz.y - this.xyzMin.y, xyz.z || 0, pitch || 0];
    }

    let thickness = zoom ? scale(zoom, [14, 16], [1 / 256, 1 / 128]) : 0.01;

    if (thickness < 0.005) thickness = 0.005;
    if (thickness > 0.01) thickness = 0.01;

    if (
      this.lineData &&
      this.lineDistData &&
      this.pointData &&
      this.pointDistData &&
      this.lineRenderer &&
      this.pointRenderer
    ) {
      if (this.tick > this.renderedTick) {
        this.lineRenderer.update(gl, this.lineData, this.pts.length);
        this.lineRenderer.updateDist(gl, this.lineDistData);
        this.pointRenderer.update(gl, this.pointData, this.pts.length);
        this.pointRenderer.updateDist(gl, this.pointDistData);
        this.renderedTick = this.tick;
      }
      const position = this.progressed ? this.position : this.pts[this.pts.length - 1][4];
      this.lineRenderer.render(gl, matrix, thickness, position, camera);
      this.pointRenderer.render(gl, matrix, thickness, position, camera);
    }
  }

  setPosition(position: number): void {
    this.position = position;
    if (position) this.progressed = true;
  }

  id: string;
  type = 'custom' as const;
  renderingMode = '3d' as const;
  tick = 0;
  renderedTick = 0;
  position = 0;
  progressed = false;

  map?: Map;

  lineRenderer?: LineRenderer;
  pointRenderer?: PointRenderer;

  lineData?: Float32Array;
  pointData?: Float32Array;
  lineDistData?: Float32Array;
  pointDistData?: Float32Array;

  xyzMin = new MercatorCoordinate(0, 0);
  xyzMax = this.xyzMin;
}
