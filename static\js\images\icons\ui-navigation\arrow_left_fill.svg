var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgArrowLeftFill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M7.59253 24L23.9573 8.17181C24.5527 7.59587 25.5023 7.6117 26.0783 8.20716C26.6182 8.76541 26.6381 9.63493 26.1478 10.216L26.0429 10.3282L13.4585 22.5L38.7501 22.5C39.5298 22.5 40.1706 23.0949 40.2432 23.8555L40.2501 24C40.2501 24.7797 39.6552 25.4205 38.8946 25.4931L38.7501 25.5L13.4585 25.5L26.0429 37.6718C26.6012 38.2118 26.65 39.0801 26.1793 39.6772L26.0783 39.7928C25.5383 40.3511 24.67 40.3999 24.0729 39.9292L23.9573 39.8282L7.59253 24L23.9573 8.17181L7.59253 24Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgArrowLeftFill);
export default __webpack_public_path__ + "static/media/arrow_left_fill.56b80a9c.svg";
export { ForwardRef as ReactComponent };