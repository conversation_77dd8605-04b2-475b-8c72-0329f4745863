import classNames from 'classnames';
import React from 'react';
import { Button } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import namespaces from '../../../i18n/namespaces';
import { ReactComponent as DownIcon } from '../../../images/icons/ui-navigation/chevron_down_outline.svg';

type PaginationProps = {
  classes?: Record<string, string>;
  setAllVisible: (showAll: boolean) => void;
  isAllVisible: boolean;
  count: number;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      justifyContent: 'space-between',
      display: 'flex',
    },
    button: {
      padding: theme.spacing(0.5, 1),
    },
    chevron: {
      height: '3rem',
      width: '3rem',
      transition: 'transform 0.3s ease-out',
    },
    rotate: {
      transform: 'rotate(180deg)',
    },
  }),
  { name: 'Pagination' },
);

function Pagination(props: PaginationProps): React.ReactElement {
  const classes = useStyles(props);
  const { t } = useTranslation(namespaces.TRANSLATIONS);
  const { setAllVisible, isAllVisible, count } = props;

  return (
    <div className={classes.root}>
      <Button
        className={classes.button}
        color="body"
        variant="text"
        onClick={() => setAllVisible(!isAllVisible)}
      >
        {t(isAllVisible ? 'SHOW_LESS' : 'SHOW_MORE')}
      </Button>
      <Button
        className={classes.button}
        variant="text"
        color="body"
        onClick={() => setAllVisible(!isAllVisible)}
        endIcon={
          <DownIcon className={classNames(classes.chevron, { [classes.rotate]: isAllVisible })} />
        }
      >
        {t('N_ITEMS', { count })}
      </Button>
    </div>
  );
}

export default Pagination;
