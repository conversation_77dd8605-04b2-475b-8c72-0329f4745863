import React from 'react';
import { Tooltip } from '@mui/material';
import { emphasize } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import { SpeedDial } from '@mui/material';
import { SpeedDialAction } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { snakeCase } from 'snake-case';
import { Workout } from '../../models/Workout';
import Icons from '../Summary/SummaryItems/Icons';
type PathColorDataSelectorProps = {
  classes?: Record<string, string>;
  workout: Workout;
  value: string | null;
  onChange: (value: string) => void;
  graphs?: string[] | null;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {},
    fab: {
      color: theme.palette.text.secondary,
      backgroundColor: theme.palette.background.default,
      '&:hover': {
        backgroundColor: emphasize(theme.palette.background.default, 0.15),
      },
      transition: `${theme.transitions.create('transform', {
        duration: theme.transitions.duration.shorter,
      })}, opacity 0.8s`,
      opacity: 1,
    },
    icon: {
      width: '3rem',
      height: '3rem',
      color: theme.palette.text.primary,
    },
    iconSmall: {
      width: '2rem',
      height: '2rem',
      color: theme.palette.text.primary,
    },
    tooltip: {
      fontSize: '1.4rem',
      fontWeight: 'normal',
    },
    mainIconWrapper: {
      width: '100%',
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
  }),
  { name: 'PathColorDataSelector' },
);

const getIconByGraphName = (name: string | null) => {
  if (!name) return null;
  return Icons[`ICON_${snakeCase(name).toUpperCase()}`];
};

function PathColorDataSelector(props: PathColorDataSelectorProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { value, onChange, graphs } = props;
  const [open, setOpen] = React.useState(false);
  const [hidden] = React.useState(false);
  const { t } = useTranslation();
  const handleClose = () => setOpen(false);
  const handleOpen = () => setOpen(true);

  const Icon = getIconByGraphName(value);
  if (!graphs || !value || !Icon) return null;
  return (
    <SpeedDial
      className={classes.root}
      ariaLabel={t(`TXT_${snakeCase(value).toUpperCase()}`)}
      hidden={hidden}
      icon={
        <Tooltip
          classes={{ tooltip: classes.tooltip }}
          title={<span>{t(`TXT_${snakeCase(value).toUpperCase()}`)}</span>}
          placement="left"
        >
          <div className={classes.mainIconWrapper}>
            {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
            {/* @ts-ignore */}
            <Icon className={classes.icon} />
          </div>
        </Tooltip>
      }
      onClose={handleClose}
      onOpen={handleOpen}
      open={open}
      FabProps={{ classes: { root: classes.fab } }}
    >
      {graphs
        .filter((graphName) => graphName !== value)
        .map((graphName) => {
          const GraphIcon = getIconByGraphName(graphName);
          if (!GraphIcon) return null;
          return (
            <SpeedDialAction
              key={graphName}
              TooltipClasses={{ tooltip: classes.tooltip }}
              icon={<GraphIcon className={classes.iconSmall} />}
              tooltipTitle={t(`TXT_${snakeCase(graphName).toUpperCase()}`)}
              FabProps={{ classes: { root: classes.fab } }}
              onClick={() => {
                handleClose();
                onChange(graphName);
              }}
            />
          );
        })}
    </SpeedDial>
  );
}

export default PathColorDataSelector;
