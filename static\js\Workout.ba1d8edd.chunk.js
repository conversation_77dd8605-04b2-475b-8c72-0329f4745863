(this["webpackJsonpbig-screen"]=this["webpackJsonpbig-screen"]||[]).push([[3],{676:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="lg"},677:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e,t){return"Feeling"===e?Boolean(t):<PERSON><PERSON><PERSON>(t)||0===t}},685:function(e,t,n){"use strict";var r,i=n(0),a=["title","titleId"];function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function l(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function c(e,t){var n=e.title,c=e.titleId,s=l(e,a);return i.createElement("svg",o({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":c},s),n?i.createElement("title",{id:c},n):null,r||(r=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M26.75 4C27.4404 4 28 4.55964 28 5.25C28 5.89721 27.5081 6.42953 26.8778 6.49355L26.75 6.5H25.249L25.2494 8.54184C34.5059 9.16422 41.75 16.6555 41.75 26C41.75 35.8447 33.7619 44 24 44C14.2381 44 6.25 35.8447 6.25 26C6.25 16.6558 13.4936 9.16475 22.7496 8.54191L22.749 6.5H21.25C20.5596 6.5 20 5.94036 20 5.25C20 4.60279 20.4919 4.07047 21.1222 4.00645L21.25 4H26.75ZM24 11C15.5234 11 8.75 17.6316 8.75 26C8.75 34.4755 15.6305 41.5 24 41.5C32.3695 41.5 39.25 34.4755 39.25 26C39.25 17.6316 32.4766 11 24 11ZM24 12.75C31.373 12.75 37.25 18.627 37.25 26C37.25 29.6694 35.8346 33.1979 33.3812 35.6365C32.8916 36.1232 32.1001 36.1208 31.6135 35.6312C31.1268 35.1416 31.1292 34.3501 31.6188 33.8635C33.5917 31.9024 34.75 29.0148 34.75 26C34.75 20.0077 29.9923 15.25 24 15.25C23.3096 15.25 22.75 14.6904 22.75 14C22.75 13.3096 23.3096 12.75 24 12.75ZM24 17.25C28.9429 17.25 33 21.3071 33 26.25C33 28.6808 32.0463 30.9815 30.3812 32.6365C29.8916 33.1232 29.1001 33.1208 28.6135 32.6312C28.1268 32.1416 28.1292 31.3501 28.6188 30.8635C29.811 29.6784 30.5 28.0162 30.5 26.25C30.5 22.6878 27.5622 19.75 24 19.75C23.3096 19.75 22.75 19.1904 22.75 18.5C22.75 17.8096 23.3096 17.25 24 17.25ZM24 21.75C26.5129 21.75 28.5 23.7371 28.5 26.25C28.5 27.4622 28.0063 28.5167 27.1312 29.3865C26.6416 29.8732 25.8501 29.8708 25.3635 29.3812C24.8768 28.8916 24.8792 28.1001 25.3688 27.6135C25.7963 27.1885 26 26.7534 26 26.25C26 25.1693 25.2093 24.3294 24.1527 24.2553L23.8722 24.2435C23.2419 24.1795 22.75 23.6472 22.75 23C22.75 22.3096 23.3096 21.75 24 21.75ZM37.2824 8.02499L37.3839 8.11612L41.3839 12.1161C41.872 12.6043 41.872 13.3957 41.3839 13.8839C40.9283 14.3395 40.2085 14.3699 39.7176 13.975L39.6161 13.8839L35.6161 9.88388C35.128 9.39573 35.128 8.60427 35.6161 8.11612C36.0717 7.6605 36.7915 7.63013 37.2824 8.02499Z",fill:"#303030"})))}var s,u=i.forwardRef(c),d=(n.p,["title","titleId"]);function f(){return(f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function p(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function v(e,t){var n=e.title,r=e.titleId,a=p(e,d);return i.createElement("svg",f({width:48,height:48,viewBox:"0 0 48 44",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,s||(s=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24.1898 10.6164L24 10.8536L23.8102 10.6164C21.8708 8.26392 18.6894 6.5 15.25 6.5C13.2739 6.5 10.7944 7.08996 8.74021 8.3834C5.75217 10.2648 4 13.3728 4 17.75C4 26.7204 13.6749 36.985 23.441 41.868C23.7929 42.044 24.2071 42.044 24.559 41.868C34.3251 36.985 44 26.7204 44 17.75C44 13.3728 42.2478 10.2648 39.2598 8.3834C37.2056 7.08996 34.7261 6.5 32.75 6.5C29.3106 6.5 26.1292 8.26392 24.1898 10.6164ZM37.1973 28.25C33.9284 32.5685 29.1865 36.5565 24.3674 39.151L24 39.346L23.6326 39.151C18.8135 36.5565 14.0716 32.5685 10.8027 28.25H18C18.4179 28.25 18.8082 28.0412 19.0401 27.6934L19.5167 26.9785L21.794 35.3289C21.9429 35.8748 22.4398 36.2525 23.0056 36.25C23.5713 36.2475 24.0649 35.8653 24.2088 35.3181L28.0216 20.8298L29.794 27.3289C29.9424 27.8728 30.4363 28.25 31 28.25H37.1973ZM38.8975 25.75H31.9547L29.206 15.6711C29.0571 15.1253 28.5602 14.7475 27.9944 14.75C27.4287 14.7526 26.9351 15.1348 26.7912 15.6819L22.9784 30.1702L21.206 23.6711C21.077 23.1984 20.6839 22.8447 20.2002 22.7662C19.7166 22.6877 19.2317 22.899 18.9599 23.3067L17.331 25.75H9.10253C7.47462 23.0498 6.5 20.3015 6.5 17.75C6.5 11.6211 10.6628 9 15.25 9C18.624 9 21.7593 11.2861 22.8577 13.7577C23.2976 14.7474 24.7024 14.7474 25.1423 13.7577C26.2407 11.2861 29.376 9 32.75 9C37.3372 9 41.5 11.6211 41.5 17.75C41.5 20.3015 40.5254 23.0498 38.8975 25.75Z",fill:"#303030"})))}var m,b=i.forwardRef(v),h=(n.p,["title","titleId"]);function g(){return(g=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function O(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function C(e,t){var n=e.title,r=e.titleId,a=O(e,h);return i.createElement("svg",g({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,m||(m=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M42.25 12C43.2165 12 44 12.8002 44 13.7872V34.2128C44 35.1998 43.2165 36 42.25 36H5.75C4.7835 36 4 35.1998 4 34.2128V13.7872C4 12.8002 4.7835 12 5.75 12H42.25ZM41.5 14.5532H6.5V33.4468H41.5V14.5532ZM25.25 16.5957V28.3404H22.75V16.5957H25.25ZM12.75 16.5957V28.3404H10.25V16.5957H12.75ZM37.75 16.5957V28.3404H35.25V16.5957H37.75ZM19 16.5957V24H16.5V16.5957H19ZM31.5 16.5957V24H29V16.5957H31.5Z",fill:"#303030"})))}var j,L=i.forwardRef(C),y=(n.p,["title","titleId"]);function w(){return(w=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function x(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function S(e,t){var n=e.title,r=e.titleId,a=x(e,y);return i.createElement("svg",w({width:48,height:48,viewBox:"0 3 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,j||(j=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.8099 8.07066L31.1883 8.20797L30.3118 10.5493C23.9211 8.1568 16.4933 9.65315 11.6339 14.5125C4.90891 21.2375 4.82269 32.3919 11.3753 39.2305L11.6339 39.4947L9.86616 41.2625C2.07749 33.4738 2.07749 20.5334 9.86616 12.7447C15.237 7.37387 23.3128 5.59908 30.4296 7.94125L30.8099 8.07066ZM40.5546 21.994L42.9455 21.2633C45.0671 28.2055 43.4259 35.7529 38.4157 40.9748L38.1339 41.2625L36.3662 39.4947C40.8376 35.0233 42.3904 28.4633 40.6623 22.3596L40.5546 21.994ZM40.3668 11.7185L37.5151 16.6731L35.6645 19.8519C32.3235 25.5453 30.0312 29.1779 28.7267 30.8294L28.6307 30.9496L28.4533 31.1634L28.2932 31.3456L28.1339 31.5125C25.7653 33.8811 21.7348 33.8811 19.3662 31.5125C16.9858 29.1321 16.9082 25.3527 19.2662 22.9947C20.1561 22.1048 22.724 20.5676 27.0861 18.2996L29.0658 17.2826L30.7229 16.4468L33.3322 15.1515L40.3668 11.7185ZM32.6484 20.0444L34.112 17.5543L31.5335 18.8378C26.2388 21.4991 22.8491 23.3774 21.5029 24.3729L21.3119 24.5191L21.1563 24.6486L21.0339 24.7625C19.6689 26.1276 19.714 28.3248 21.1339 29.7447C22.4765 31.0873 24.8004 31.1352 26.1932 29.9089L26.344 29.7674L26.4627 29.643L26.6129 29.471C27.6177 28.2779 29.6462 25.1134 32.6484 20.0444Z",fill:"#303030"})))}var E,T=i.forwardRef(S),I=(n.p,["title","titleId"]);function N(){return(N=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function R(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _(e,t){var n=e.title,r=e.titleId,a=R(e,I);return i.createElement("svg",N({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,E||(E=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M23.8723 4.00645L24.0001 4C24.6473 4 25.1796 4.49187 25.2437 5.12219L25.2501 5.25L25.25 37.778L33.2231 26.2874C33.5904 25.758 34.2934 25.6001 34.8466 25.9014L34.9627 25.973C35.4921 26.3403 35.65 27.0433 35.3487 27.5965L35.2771 27.7126L24.0173 43.9405L12.7241 27.7141C12.3298 27.1474 12.4694 26.3684 13.0361 25.974C13.5649 25.606 14.2788 25.7031 14.6923 26.1783L14.7761 26.2859L22.75 37.743L22.7501 5.25C22.7501 4.60279 23.242 4.07047 23.8723 4.00645L24.0001 4L23.8723 4.00645Z",fill:"#303030"})))}var k,P=i.forwardRef(_),A=(n.p,["title","titleId"]);function M(){return(M=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function H(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function D(e,t){var n=e.title,r=e.titleId,a=H(e,A);return i.createElement("svg",M({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,k||(k=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M36.5 32.75C37.3685 32.75 38.0927 33.0477 38.8896 33.621L39.218 33.8689L39.4516 34.0569L39.9839 34.4991L40.2378 34.7052C41.1892 35.4589 41.8453 35.7534 42.75 35.7534C43.4404 35.7534 44 36.313 44 37.0034C44 37.6937 43.4404 38.2534 42.75 38.2534C41.1731 38.2534 40.0522 37.7452 38.6973 36.6742L38.4404 36.4668L37.6748 35.8359L37.43 35.6506C37.0285 35.3618 36.7554 35.25 36.5 35.25C36.2446 35.25 35.9715 35.3618 35.57 35.6506L35.4133 35.7673L35.2312 35.9108L34.3027 36.6742C32.9478 37.7452 31.8269 38.2534 30.25 38.2534C28.6731 38.2534 27.5522 37.7452 26.1973 36.6742L25.9404 36.4668L25.1748 35.8359L24.93 35.6506C24.5285 35.3618 24.2554 35.25 24 35.25C23.7446 35.25 23.4715 35.3618 23.07 35.6506L22.9133 35.7673L22.7312 35.9108L21.8027 36.6742C20.4478 37.7452 19.3269 38.2534 17.75 38.2534C16.1731 38.2534 15.0522 37.7452 13.6973 36.6742L13.4404 36.4668L12.6748 35.8359L12.43 35.6506C12.0285 35.3618 11.7554 35.25 11.5 35.25C11.2446 35.25 10.9715 35.3618 10.57 35.6506L10.4133 35.7673L10.2312 35.9108L9.30266 36.6742C7.94777 37.7452 6.82687 38.2534 5.25 38.2534C4.55964 38.2534 4 37.6937 4 37.0034C4 36.313 4.55964 35.7534 5.25 35.7534C6.15467 35.7534 6.81082 35.4589 7.7622 34.7052L7.97079 34.5366L8.54841 34.0569L8.78205 33.8689L9.1104 33.621C9.90732 33.0477 10.6315 32.75 11.5 32.75C12.3685 32.75 13.0927 33.0477 13.8896 33.621L14.218 33.8689L14.4516 34.0569L14.9839 34.4991L15.2378 34.7052C16.1892 35.4589 16.8453 35.7534 17.75 35.7534C18.6547 35.7534 19.3108 35.4589 20.2622 34.7052L20.4708 34.5366L21.0484 34.0569L21.282 33.8689L21.6104 33.621C22.4073 33.0477 23.1315 32.75 24 32.75C24.8685 32.75 25.5927 33.0477 26.3896 33.621L26.718 33.8689L26.9516 34.0569L27.4839 34.4991L27.7378 34.7052C28.6892 35.4589 29.3453 35.7534 30.25 35.7534C31.1547 35.7534 31.8108 35.4589 32.7622 34.7052L32.9708 34.5366L33.5484 34.0569L33.782 33.8689L34.1104 33.621C34.9073 33.0477 35.6315 32.75 36.5 32.75ZM23.5 10C31.1938 10 37.9234 14.8787 40.4427 21.8275L41.5373 17.4501C41.7049 16.7804 42.3836 16.3732 43.0533 16.5407C43.6812 16.6978 44.0782 17.3041 43.9874 17.9311L43.9626 18.0567L41.5796 27.5849L32.4267 22.8608C31.8132 22.5441 31.5726 21.7902 31.8892 21.1767C32.1847 20.6041 32.8613 20.3563 33.4489 20.5833L33.5733 20.6392L38.2162 23.0353C36.154 16.8727 30.2576 12.5 23.5 12.5C16.088 12.5 9.70788 17.7689 8.29288 24.8866L8.23112 25.2165L5.76888 24.7835C7.25962 16.3047 14.7596 10 23.5 10Z",fill:"#303030"})))}var V,Z=i.forwardRef(D),B=(n.p,["title","titleId"]);function W(){return(W=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function X(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function U(e,t){var n=e.title,r=e.titleId,a=X(e,B);return i.createElement("svg",W({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,V||(V=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M36.5 29.75C37.3685 29.75 38.0927 30.0477 38.8896 30.621L39.218 30.869L39.4516 31.0569L39.9839 31.4992L40.2378 31.7052C41.1892 32.4589 41.8453 32.7534 42.75 32.7534C43.4404 32.7534 44 33.313 44 34.0034C44 34.6938 43.4404 35.2534 42.75 35.2534C41.1731 35.2534 40.0522 34.7452 38.6973 33.6742L38.4404 33.4668L37.6748 32.8359L37.43 32.6506C37.0285 32.3618 36.7554 32.25 36.5 32.25C36.2446 32.25 35.9715 32.3618 35.57 32.6506L35.4133 32.7674L35.2312 32.9108L34.3027 33.6742C32.9478 34.7452 31.8269 35.2534 30.25 35.2534C28.6731 35.2534 27.5522 34.7452 26.1973 33.6742L25.9404 33.4668L25.1748 32.8359L24.93 32.6506C24.5285 32.3618 24.2554 32.25 24 32.25C23.7446 32.25 23.4715 32.3618 23.07 32.6506L22.9133 32.7674L22.7312 32.9108L21.8027 33.6742C20.4478 34.7452 19.3269 35.2534 17.75 35.2534C16.1731 35.2534 15.0522 34.7452 13.6973 33.6742L13.4404 33.4668L12.6748 32.8359L12.43 32.6506C12.0285 32.3618 11.7554 32.25 11.5 32.25C11.2446 32.25 10.9715 32.3618 10.57 32.6506L10.4133 32.7674L10.2312 32.9108L9.30266 33.6742C7.94777 34.7452 6.82687 35.2534 5.25 35.2534C4.55964 35.2534 4 34.6938 4 34.0034C4 33.313 4.55964 32.7534 5.25 32.7534C6.15467 32.7534 6.81082 32.4589 7.7622 31.7052L7.97079 31.5366L8.54841 31.0569L8.78205 30.869L9.1104 30.621C9.90732 30.0477 10.6315 29.75 11.5 29.75C12.3685 29.75 13.0927 30.0477 13.8896 30.621L14.218 30.869L14.4516 31.0569L14.9839 31.4992L15.2378 31.7052C16.1892 32.4589 16.8453 32.7534 17.75 32.7534C18.6547 32.7534 19.3108 32.4589 20.2622 31.7052L20.4708 31.5366L21.0484 31.0569L21.282 30.869L21.6104 30.621C22.4073 30.0477 23.1315 29.75 24 29.75C24.8685 29.75 25.5927 30.0477 26.3896 30.621L26.718 30.869L26.9516 31.0569L27.4839 31.4992L27.7378 31.7052C28.6892 32.4589 29.3453 32.7534 30.25 32.7534C31.1547 32.7534 31.8108 32.4589 32.7622 31.7052L32.9708 31.5366L33.5484 31.0569L33.782 30.869L34.1104 30.621C34.9073 30.0477 35.6315 29.75 36.5 29.75ZM21.9687 13.5011C22.6154 13.528 23.1267 14.0417 23.1645 14.6741L23.1656 14.8021L22.8458 22.4757L15.4525 21.1899C14.7723 21.0716 14.3169 20.4243 14.4352 19.7442C14.546 19.1065 15.1219 18.6664 15.7538 18.7113L15.8808 18.7268L18.6901 19.2147C17.6169 17.4247 15.6599 16.25 13.5 16.25C10.9036 16.25 8.59825 17.9507 7.77146 20.3763L7.69831 20.6058L5.30169 19.8943C6.36959 16.297 9.70423 13.75 13.5 13.75C16.3786 13.75 18.9909 15.2129 20.5512 17.4881L20.6677 14.698C20.6965 14.0082 21.2789 13.4724 21.9687 13.5011ZM42.8 13.501C43.4467 13.5269 43.9589 14.0396 43.9977 14.672L43.999 14.8L43.6924 22.466L36.2945 21.233C35.6135 21.1195 35.1535 20.4755 35.267 19.7945C35.3734 19.1561 35.9461 18.7119 36.5784 18.7524L36.7055 18.767L39.7217 19.2702C38.6546 17.4476 36.6823 16.25 34.5 16.25C31.902 16.25 29.6011 17.9479 28.7715 20.3769L28.698 20.6067L26.302 19.8933C27.3738 16.2936 30.7027 13.75 34.5 13.75C37.2876 13.75 39.8227 15.1205 41.3978 17.2734L41.501 14.7001C41.5286 14.0103 42.1102 13.4734 42.8 13.501Z",fill:"#303030"})))}var G,F=i.forwardRef(U),z=(n.p,["title","titleId"]);function Y(){return(Y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function K(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function q(e,t){var n=e.title,r=e.titleId,a=K(e,z);return i.createElement("svg",Y({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,G||(G=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.3732 29.3397L20.75 28.1842V44.1162L35.6269 18.6603L27.25 19.8157V3.88373L12.3732 29.3397ZM17.1269 26.1603L24.75 13.1162V22.6842L30.8732 21.8397L23.25 34.8837V25.3157L17.1269 26.1603Z",fill:"#303030"})))}var J,$=i.forwardRef(q),Q=(n.p,["title","titleId"]);function ee(){return(ee=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function te(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ne(e,t){var n=e.title,r=e.titleId,a=te(e,Q);return i.createElement("svg",ee({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,J||(J=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.8161 29.5909C14.0978 33.4095 13.9796 36.9656 15.4184 40.3227L16.8876 43.7509L13.6493 41.9004C6.74981 37.9579 6.427 28.92 11.2835 21.6351C13.6773 18.0446 19.3514 11.8978 19.9205 11.1297L19.9622 11.0669C20.481 10.0295 20.6208 8.49146 20.3293 6.45135L19.9791 4L22.6073 5.41752C26.7386 7.68585 29.4902 9.82512 30.8771 11.9055C32.5461 14.409 33.2963 17.019 33.2005 19.7478C33.9972 19.2822 34.4249 18.4151 34.4249 17.4638V14.446L36.5588 16.5799C38.6192 18.6403 40.2698 24.8367 39.8398 30.2121C39.3691 36.0961 35.7956 41.8302 32.0862 42.7575L27.3067 43.9524L30.9383 40.6234C33.2391 38.5144 33.4992 36.3031 32.072 32.4973C31.6106 31.2667 30.8786 30.029 29.8377 28.6269L29.4223 28.0795L29.1112 27.6826L27.4628 25.6419L27.1452 25.2304C26.7066 24.6482 26.4328 24.2128 26.2534 23.7886C25.8663 22.8737 25.7341 21.8125 25.8399 20.6061C24.4927 21.1092 23.2031 21.9736 21.9642 23.2125C19.7435 25.4332 19.3298 27.7083 20.2524 29.5534L21.1569 31.3624H19.1343C17.8589 31.3624 16.8686 30.6829 16.1165 29.4235L15.9933 29.2082L15.8161 29.5909ZM11.9638 36.6383C11.698 33.0799 12.7708 29.3734 15.1554 25.558L16.7146 23.0633L17.4701 26.082C17.8253 24.5092 18.7215 22.9196 20.1964 21.4447C22.3251 19.316 24.6756 18.0504 27.2278 17.6858L29.0802 17.4212L28.6154 19.2337C28.2116 20.8087 28.2116 22.0009 28.5558 22.8145C28.6553 23.0497 28.8942 23.4088 29.3171 23.9544L29.5933 24.3046L31.0735 26.1335L31.5577 26.7553C32.8773 28.4829 33.8119 30.0168 34.4129 31.6194C35.0708 33.3739 35.4274 34.9323 35.4496 36.3473C36.4325 34.531 37.1658 32.2879 37.3478 30.0127C37.6166 26.6521 36.9493 22.8962 36.0098 20.4859C35.1465 21.7431 33.6993 22.6057 31.783 22.6057H30.3074L30.55 21.1502C31.0117 18.3797 30.4768 15.812 28.797 13.2923C27.8426 11.8606 25.8999 10.2516 22.9784 8.50889C22.9644 9.93743 22.7093 11.1629 22.1983 12.1849L22.0163 12.5484C21.8099 12.9467 21.6212 13.2078 20.7593 14.1549L18.2566 16.8627C16.3183 18.9798 14.8531 20.7877 13.3635 23.0221C10.2244 27.7309 9.61244 33.1119 11.8458 36.7904L11.9989 37.0331L11.9638 36.6383Z",fill:"#303030"})))}var re,ie=i.forwardRef(ne),ae=(n.p,["title","titleId"]);function oe(){return(oe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function le(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ce(e,t){var n=e.title,r=e.titleId,a=le(e,ae);return i.createElement("svg",oe({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,re||(re=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.66601 14.5351C12.8781 5.50753 24.4215 2.41454 33.4613 7.6337C36.3788 9.36265 38.6482 11.6065 40.2543 14.3516L40.6658 9.27588C40.7217 8.58779 41.3248 8.0753 42.0129 8.13121C42.658 8.18362 43.1487 8.71699 43.1615 9.35043L43.1576 9.47833L42.3143 19.8575L32.1693 16.6266C31.5115 16.4171 31.1481 15.714 31.3576 15.0562C31.554 14.4395 32.1842 14.0816 32.8042 14.2118L32.928 14.2445L38.2889 15.9523C36.8891 13.4199 34.8628 11.3702 32.1991 9.79159C24.3673 5.26989 14.3528 7.95327 9.83107 15.7851C5.30938 23.6169 7.99275 33.6314 15.8246 38.1531C23.5572 42.6175 33.4177 40.0582 38.0182 32.4545L38.1926 32.1596L40.3576 33.4096C35.1456 42.4371 23.6021 45.5302 14.5746 40.3181C5.54702 35.1061 2.45396 23.5626 7.66601 14.5351Z",fill:"#303030"})))}var se,ue=i.forwardRef(ce),de=(n.p,["title","titleId"]);function fe(){return(fe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function pe(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ve(e,t){var n=e.title,r=e.titleId,a=pe(e,de);return i.createElement("svg",fe({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,se||(se=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M40.0873 7.9126L35.9764 28.7468C35.8427 29.4241 35.1853 29.8648 34.508 29.7312C33.8731 29.6059 33.446 29.0203 33.5052 28.3895L33.5236 28.2628L36.477 13.2896L10.1339 39.6339C9.67827 40.0895 8.95845 40.1199 8.46761 39.725L8.36612 39.6339C7.9105 39.1783 7.88013 38.4585 8.27499 37.9676L8.36612 37.8661L34.71 11.5216L19.742 14.4763C19.1071 14.6017 18.4896 14.2222 18.3047 13.6162L18.2737 13.492C18.1483 12.8571 18.5278 12.2396 19.1338 12.0547L19.258 12.0237L40.0873 7.9126Z",fill:"#303030"})))}var me,be=i.forwardRef(ve),he=(n.p,["title","titleId"]);function ge(){return(ge=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Oe(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ce(e,t){var n=e.title,r=e.titleId,a=Oe(e,he);return i.createElement("svg",ge({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,me||(me=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.29L14.4249 34.8619C14.8668 34.3316 15.6551 34.2599 16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.29L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM27 4H32C32.6904 4 33.25 4.55964 33.25 5.25C33.25 5.89721 32.7582 6.42953 32.1278 6.49355L32 6.5H30.75L30.7506 8.10829C37.5807 8.74755 43 14.5643 43 21.55C43 28.9571 36.9071 35.05 29.5 35.05C22.0929 35.05 16 28.9571 16 21.55C16 14.564 21.4199 8.74698 28.2504 8.10819L28.25 6.5H27C26.3528 6.5 25.8205 6.00813 25.7565 5.37781L25.75 5.25C25.75 4.60279 26.2419 4.07047 26.8722 4.00645L27 4H32H27ZM29.5 10.55C23.4736 10.55 18.5 15.5236 18.5 21.55C18.5 27.5764 23.4736 32.55 29.5 32.55C35.5264 32.55 40.5 27.5764 40.5 21.55C40.5 15.5236 35.5264 10.55 29.5 10.55ZM29.5 12.75C34.3325 12.75 38.25 16.6675 38.25 21.5C38.25 23.8575 37.3116 26.0695 35.6742 27.7001C35.1851 28.1873 34.3936 28.1856 33.9065 27.6964C33.4193 27.2073 33.421 26.4158 33.9102 25.9287C35.0811 24.7626 35.75 23.1859 35.75 21.5C35.75 18.0482 32.9518 15.25 29.5 15.25C28.8097 15.25 28.25 14.6904 28.25 14C28.25 13.3096 28.8097 12.75 29.5 12.75ZM29.5 17.75C31.5711 17.75 33.25 19.4289 33.25 21.5C33.25 22.512 32.8456 23.463 32.1416 24.1617C31.6516 24.648 30.8601 24.645 30.3738 24.155C29.9199 23.6977 29.8923 22.9778 30.289 22.4884L30.4752 22.2822C30.6518 22.0629 30.75 21.7904 30.75 21.5C30.75 20.8528 30.2582 20.3205 29.6278 20.2565L29.3722 20.2435C28.7419 20.1795 28.25 19.6472 28.25 19C28.25 18.3096 28.8097 17.75 29.5 17.75ZM40.0324 7.77499L40.1339 7.86612L43.6339 11.3661C44.1221 11.8543 44.1221 12.6457 43.6339 13.1339C43.1783 13.5895 42.4585 13.6199 41.9676 13.225L41.8661 13.1339L38.3661 9.63388C37.878 9.14573 37.878 8.35427 38.3661 7.86612C38.8218 7.4105 39.5416 7.38013 40.0324 7.77499Z",fill:"#303030"})))}var je,Le=i.forwardRef(Ce),ye=(n.p,["title","titleId"]);function we(){return(we=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function xe(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Se(e,t){var n=e.title,r=e.titleId,a=xe(e,ye);return i.createElement("svg",we({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,je||(je=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M23.5001 4C26.684 4 29.3752 6.61885 29.4959 9.77869L29.5001 10V27.111C30.3344 27.7789 31.0248 28.6521 31.6801 29.7872C34.2941 34.3148 32.7429 40.1042 28.2152 42.7183C23.6876 45.3323 17.8982 43.781 15.2842 39.2534C12.9359 35.1861 13.8085 30.1565 17.2798 27.2865L17.5001 27.1099V10C17.5001 6.74208 20.2422 4 23.5001 4ZM23.5001 6.5C21.6854 6.5 20.1086 8.01641 20.0054 9.81318L20.0001 10V28.4206L19.4414 28.7914C16.4478 30.7787 15.5847 34.7739 17.4492 38.0034C19.3729 41.3353 23.6334 42.4769 26.9652 40.5532C30.2971 38.6295 31.4387 34.3691 29.5151 31.0372C28.9495 30.0575 28.3962 29.3847 27.7525 28.9226L27.5746 28.8018L27.0001 28.4328V10C27.0001 8.12279 25.3773 6.5 23.5001 6.5ZM24.7321 11.6689L24.7318 29.6445C26.931 30.1744 28.5001 32.1059 28.5001 34.5C28.5001 37.3211 26.3212 39.5 23.5001 39.5C20.679 39.5 18.5001 37.3211 18.5001 34.5C18.5001 32.1193 20.0518 30.1959 22.2317 29.6534L22.2321 11.6689H24.7321Z",fill:"#303030"})))}var Ee,Te=i.forwardRef(Se),Ie=(n.p,["title","titleId"]);function Ne(){return(Ne=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Re(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _e(e,t){var n=e.title,r=e.titleId,a=Re(e,Ie);return i.createElement("svg",Ne({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Ee||(Ee=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.05298 13.285V34.75H11.964V32.482H6.50998V24.868H11.181V22.6H6.50998V15.553H11.964V13.285H4.05298ZM16.008 34.75V15.553H18.222C19.707 15.553 19.977 15.985 19.977 17.659V23.329C19.977 24.967 19.7266 25.4533 18.382 25.487L16.791 25.489V27.757H18.33C21.4824 27.757 22.3855 26.5556 22.4321 24.153L22.434 17.011C22.434 14.446 21.57 13.285 18.33 13.285H13.551V34.75H16.008ZM28.854 35.047C32.3837 35.047 33.1637 33.3508 33.1996 31.2373L33.201 16.957C33.201 14.77 32.472 12.988 28.854 12.988H28.26C24.669 12.988 23.913 14.77 23.913 16.957V31.078L23.9189 31.4028C23.9957 33.4387 24.8485 35.047 28.26 35.047H28.854ZM28.233 32.779C26.532 32.779 26.37 32.104 26.37 30.43V17.605C26.37 15.931 26.505 15.256 28.233 15.256H28.854C30.4942 15.256 30.7285 15.8836 30.7432 17.4294L30.744 30.43C30.744 32.104 30.528 32.779 28.854 32.779H28.233ZM39.783 35.047H39.405C35.922 35.047 35.193 33.373 35.193 31.105V16.957L35.1987 16.6175C35.2728 14.4988 36.0961 12.988 39.405 12.988H39.783C42.915 12.988 43.779 14.662 43.887 15.796L44.103 18.361H41.646L41.484 16.471C41.43 15.931 41.268 15.256 39.783 15.256H39.351C37.843 15.256 37.6619 15.8819 37.6506 17.4231L37.65 30.457C37.65 32.158 37.785 32.779 39.351 32.779H39.783C41.241 32.779 41.322 32.185 41.376 31.591L41.565 29.08H44.022L43.779 32.266L43.7571 32.4625C43.5977 33.5402 42.7307 35.047 39.783 35.047Z",fill:"#303030"})))}var ke,Pe=i.forwardRef(_e),Ae=(n.p,["title","titleId"]);function Me(){return(Me=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function He(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function De(e,t){var n=e.title,r=e.titleId,a=He(e,Ae);return i.createElement("svg",Me({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,ke||(ke=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.0751 30.9971H13.2138L13.5742 28.2814L15.0435 18.1945C15.2376 16.8644 15.3485 15.9499 15.3485 14.9246V12.9848H17.8713V14.9246C17.8713 16.1993 17.7881 17.0861 17.4832 18.8873L14.6831 35.0152H11.5504L8.7503 18.8873C8.41762 16.9475 8.38989 16.227 8.38989 14.9246V12.9848H10.9127V14.9246C10.9127 15.9499 10.9682 16.9475 11.1622 18.1945L12.7147 28.2537L13.0751 30.9971ZM24.913 35.32H24.3031C20.6159 35.32 19.8396 33.4911 19.8396 31.2465V16.7535C19.8396 14.5089 20.6159 12.68 24.3031 12.68H24.913C28.628 12.68 29.3765 14.5089 29.3765 16.7535V31.2465C29.3765 33.4911 28.628 35.32 24.913 35.32ZM24.2754 32.9923H24.913C26.6319 32.9923 26.8537 32.2995 26.8537 30.5814V17.4186C26.8537 15.7005 26.6596 15.0077 24.913 15.0077H24.2754C22.5011 15.0077 22.3625 15.7005 22.3625 17.4186V30.5814C22.3625 32.2995 22.5288 32.9923 24.2754 32.9923ZM31.8439 27.4223V25.7319C31.8439 24.8729 31.8993 24.3464 32.5647 23.4596L36.1687 18.8042C36.7232 18.0837 36.9172 17.2523 36.9172 16.6427V15.7559C36.9172 14.7583 36.5846 14.6752 35.7529 14.6752H35.3925C34.7271 14.6752 34.339 14.7583 34.2281 15.4788L34.1172 16.3102H31.6221L31.733 15.2017C31.927 13.539 33.1746 12.7631 35.4479 12.7631H35.8083C38.0539 12.7631 39.3292 13.2896 39.3292 15.3957V16.5041C39.3292 17.8066 38.8579 18.8319 37.8321 20.1066L34.5053 24.2355C34.2558 24.5404 34.2004 24.9837 34.2004 25.344H39.6064V27.4223H31.8439Z",fill:"#303030"})))}var Ve,Ze=i.forwardRef(De),Be=(n.p,["title","titleId"]);function We(){return(We=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Xe(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ue(e,t){var n=e.title,r=e.titleId,a=Xe(e,Be);return i.createElement("svg",We({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Ve||(Ve=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.7178 9.25C15.1707 9.25 16.3581 10.8456 16.3581 14.3708V23.9072C16.3581 27.3953 15.1707 29.1393 10.7178 29.1393H8.60273V26.0223H10.5694C12.5465 26.0223 12.9565 25.4303 12.9802 23.268L12.9813 15.2613C12.9813 12.9607 12.6103 12.367 10.5694 12.367H7.52663V38.75H4.1499V9.25H10.7178ZM30.7841 9.25V12.367H25.9179V38.75H22.5375V12.367H17.6713V9.25H30.7841ZM43.8968 9.25V12.367H36.3932V22.0519H42.8196V25.1689H36.3932V35.633H43.8968V38.75H33.0129V9.25H43.8968Z",fill:"#303030"})))}var Ge,Fe=i.forwardRef(Ue),ze=(n.p,["title","titleId"]);function Ye(){return(Ye=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Ke(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function qe(e,t){var n=e.title,r=e.titleId,a=Ke(e,ze);return i.createElement("svg",Ye({width:48,height:48,viewBox:"0 3 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Ge||(Ge=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.853 20.4117C16.7109 18.3974 14.0383 18.3974 12.9126 20.3837L4.04199 35.7055L6.20556 36.9581L15.0819 21.6263C15.2557 21.3197 15.4943 21.3197 15.6628 21.6167L18.7256 27.3603L27.0918 12.3588C27.2559 12.0692 27.7441 12.0692 27.9127 12.3665L41.7751 36.9528L43.9528 35.7249L30.0889 11.1361C28.9611 9.14674 26.0381 9.14674 24.9124 11.1339L18.7746 22.1397L17.853 20.4117Z",fill:"#303030"})))}var Je,$e=i.forwardRef(qe),Qe=(n.p,["title","titleId"]);function et(){return(et=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function tt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function nt(e,t){var n=e.title,r=e.titleId,a=tt(e,Qe);return i.createElement("svg",et({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Je||(Je=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 4C35.0457 4 44 12.9543 44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24C4 12.9543 12.9543 4 24 4ZM24 6.5C14.335 6.5 6.5 14.335 6.5 24C6.5 33.665 14.335 41.5 24 41.5C33.665 41.5 41.5 33.665 41.5 24C41.5 14.335 33.665 6.5 24 6.5ZM32.6031 18.3853C33.0591 18.8406 33.0901 19.5604 32.6957 20.0515L32.6047 20.1531L27.1961 25.5713C27.3914 26.0076 27.5 26.4911 27.5 27C27.5 28.933 25.933 30.5 24 30.5C23.4911 30.5 23.0076 30.3914 22.5713 30.1961L20.6339 32.1339L18.8661 30.3661L20.8039 28.4287C20.6086 27.9924 20.5 27.5089 20.5 27C20.5 25.067 22.067 23.5 24 23.5C24.5085 23.5 24.9917 23.6084 25.4277 23.8035L30.8353 18.3869C31.3231 17.8983 32.1145 17.8976 32.6031 18.3853ZM9.52157 21.2688L11.9784 21.7312C11.8265 22.5385 11.75 23.2721 11.75 24C11.75 26.2907 12.2173 28.3539 13.1437 30.0456L13.3228 30.3584L11.1772 31.6416C9.88451 29.4803 9.25 26.862 9.25 24C9.25 23.107 9.34237 22.2208 9.52157 21.2688ZM38.4795 21.2745C38.6537 22.2244 38.75 23.1397 38.75 24C38.75 26.594 38.1885 28.9182 37.0362 31.0169L36.8268 31.3849L34.6732 30.1151C35.7336 28.3168 36.25 26.3116 36.25 24C36.25 23.4203 36.1939 22.7901 36.0891 22.1271L36.0205 21.7255L38.4795 21.2745ZM24 26C23.4477 26 23 26.4477 23 27C23 27.5523 23.4477 28 24 28C24.5523 28 25 27.5523 25 27C25 26.4477 24.5523 26 24 26ZM16.6436 11.1569L17.8564 13.3431C15.4116 14.6993 13.6123 16.8241 12.5612 19.5571L12.4284 19.917L10.0716 19.083C11.2996 15.6132 13.5469 12.8748 16.6436 11.1569ZM31.3731 11.1689C34.322 12.8645 36.5797 15.5195 37.7943 18.7157L37.9297 19.0868L35.5703 19.9132C34.6317 17.2334 32.8272 14.9918 30.4414 13.5233L30.1269 13.3361L31.3731 11.1689ZM24 9.25C25.8988 9.25 27.4121 9.4516 28.865 9.96061L29.1994 10.0836L28.3006 12.4164C27.0664 11.9409 25.7616 11.75 24 11.75C22.5302 11.75 21.1778 11.9337 20.0219 12.3059L19.6812 12.4233L18.8188 10.0767C20.3461 9.51546 22.1074 9.25 24 9.25Z",fill:"#303030"})))}var rt,it=i.forwardRef(nt),at=(n.p,["title","titleId"]);function ot(){return(ot=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function lt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ct(e,t){var n=e.title,r=e.titleId,a=lt(e,at);return i.createElement("svg",ot({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,rt||(rt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M44.1019 7.63196L43.2437 21.5768C43.2013 22.2658 42.6084 22.79 41.9193 22.7476C41.2733 22.7079 40.7722 22.1842 40.747 21.5512L40.7484 21.4232L41.2775 12.815L28.6422 27.9821L18.638 24.2305L5.95652 39.3047L4.04346 37.6953L17.862 21.2695L27.8578 25.0179L39.1955 11.407L30.7973 13.4641C30.1687 13.6181 29.5346 13.2669 29.3225 12.6699L29.2859 12.5473C29.1319 11.9187 29.483 11.2846 30.0801 11.0725L30.2027 11.0359L44.1019 7.63196Z",fill:"#303030"})))}var st,ut=i.forwardRef(ct),dt=(n.p,["title","titleId"]);function ft(){return(ft=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function pt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function vt(e,t){var n=e.title,r=e.titleId,a=pt(e,dt);return i.createElement("svg",ft({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,st||(st=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M26.75 4.02502C27.4404 4.02502 28 4.58467 28 5.27502C28 5.92223 27.5081 6.45456 26.8778 6.51857L26.75 6.52502H25.249L25.25 8.54336C34.4695 9.18482 41.75 16.8675 41.75 26.25C41.75 36.0528 33.8028 44 24 44C14.1972 44 6.25 36.0528 6.25 26.25C6.25 16.8678 13.5299 9.18538 22.749 8.54343L22.749 6.52502H21.25C20.5596 6.52502 20 5.96538 20 5.27502C20 4.62782 20.4919 4.09549 21.1222 4.03148L21.25 4.02502H26.75ZM24 11C15.578 11 8.75 17.828 8.75 26.25C8.75 34.6721 15.578 41.5 24 41.5C32.422 41.5 39.25 34.6721 39.25 26.25C39.25 17.828 32.422 11 24 11ZM25.275 14.4676L25.3661 14.3661C25.8217 13.9105 26.5415 13.8802 27.0324 14.275L27.1339 14.3661L31.2678 18.5L27.1339 22.6339C26.6457 23.1221 25.8543 23.1221 25.3661 22.6339C24.9105 22.1783 24.8801 21.4585 25.275 20.9676L25.3661 20.8661L26.482 19.75H24C20.4378 19.75 17.5 22.6878 17.5 26.25C17.5 29.8123 20.4378 32.75 24 32.75C27.4848 32.75 30.372 29.9386 30.4959 26.4814L30.5 26.25H33C33 31.193 28.9429 35.25 24 35.25C19.0571 35.25 15 31.193 15 26.25C15 21.3953 18.9135 17.3952 23.7361 17.2539L24 17.25H26.483L25.3661 16.1339C24.9105 15.6783 24.8801 14.9585 25.275 14.4676L25.3661 14.3661L25.275 14.4676ZM37.2505 8.2503L37.3549 8.3381L41.3549 12.0881C41.8586 12.5603 41.8841 13.3513 41.4119 13.855C40.9712 14.325 40.2528 14.3786 39.7495 13.9997L39.6451 13.9119L35.6451 10.1619C35.1414 9.68978 35.1159 8.89874 35.5881 8.3951C36.0288 7.92503 36.7472 7.87147 37.2505 8.2503Z",fill:"#303030"})))}var mt,bt=i.forwardRef(vt),ht=(n.p,["title","titleId"]);function gt(){return(gt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Ot(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ct(e,t){var n=e.title,r=e.titleId,a=Ot(e,ht);return i.createElement("svg",gt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,mt||(mt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.87454 11.9842C17.6625 4.19626 30.3351 4.19626 38.1231 11.9842C45.8205 19.6816 45.91 31.6435 38.3916 39.4401L38.1231 39.7135L36.3553 37.9458C43.167 31.1341 43.167 20.5636 36.3553 13.752C29.5437 6.94034 18.4539 6.94034 11.6423 13.752C4.91912 20.4752 4.83181 30.8602 11.3804 37.6785L11.6423 37.9458L9.87454 39.7135C2.08658 31.9256 2.08658 19.7722 9.87454 11.9842ZM23.9988 17.7944L32.7296 29.4354H15.268L23.9988 17.7944ZM23.9988 21.961L20.268 26.9354H27.7296L23.9988 21.961Z",fill:"#303030"})))}var jt,Lt=i.forwardRef(Ct),yt=(n.p,["title","titleId"]);function wt(){return(wt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function xt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function St(e,t){var n=e.title,r=e.titleId,a=xt(e,yt);return i.createElement("svg",wt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,jt||(jt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5838 25.7084C13.3908 22.5786 17.141 23.2024 21.9745 25.9931C23.5199 26.8853 24.4409 27.839 25.5221 29.4297L26.1533 30.3826L26.4387 30.7978C26.9675 31.5389 27.4094 31.9783 28.0024 32.3206C28.9734 32.8813 29.4999 33.0892 30.306 33.2725L31.0741 33.4389C31.7781 33.6012 32.3331 33.7837 33.0829 34.1221C36.3826 35.6113 37.9582 38.2902 36.1962 41.3421C34.7945 43.77 32.7212 44.4463 30.4564 43.7693C29.7807 43.5674 29.2639 43.3266 28.5307 42.9137L28.2449 42.7506L28.0078 42.6103L27.7454 42.4442C27.7012 42.4151 27.6563 42.385 27.6101 42.3534L27.3122 42.1432L26.9527 41.8771L25.5717 40.8225C24.8394 40.2694 24.3428 39.9283 23.7933 39.611C23.4177 39.3942 23.035 39.2559 22.3038 39.0874L22.0736 39.0358L21.6307 38.9426L20.7907 38.7754C19.2334 38.4607 18.4052 38.2086 17.5011 37.6866C12.532 34.8177 9.48915 29.3365 11.5838 25.7084ZM20.7245 28.1581C17.0159 26.0169 14.5311 25.6036 13.7489 26.9584C12.5145 29.0964 14.8221 33.2532 18.7511 35.5216C19.2644 35.818 19.8082 36.0035 20.7626 36.2145L21.0333 36.2728L22.4414 36.5573L22.9833 36.6773C23.9004 36.8922 24.4266 37.0899 25.0433 37.4459C25.4343 37.6717 25.793 37.9007 26.1901 38.1781L26.4962 38.3959L27.0562 38.8107L28.4411 39.8681C28.8396 40.1675 29.0747 40.3314 29.313 40.4775L29.4949 40.5855L29.728 40.7187C30.3223 41.054 30.711 41.2361 31.1723 41.374C32.3843 41.7363 33.2438 41.456 34.0312 40.0921C34.8994 38.5882 34.1761 37.3582 32.0546 36.4008C31.4766 36.14 31.0731 36.0052 30.5298 35.879L30.245 35.816L29.7517 35.7102C28.7095 35.4729 27.9549 35.18 26.7524 34.4857C25.7978 33.9346 25.1198 33.2559 24.3949 32.2376L24.2125 31.9759L23.4484 30.8261C22.5667 29.5302 21.8827 28.8268 20.7245 28.1581ZM30.7984 10.5717C31.2831 11.4112 31.535 12.1853 31.8205 13.5385L32.1021 14.9315L32.1985 15.3715L32.2987 15.7784C32.4132 16.2091 32.5237 16.4978 32.6694 16.7681L32.7228 16.8638C33.04 17.4133 33.3812 17.91 33.9342 18.6423L34.9889 20.0232L35.3367 20.4966C35.5002 20.7263 35.6221 20.9124 35.744 21.1146L35.8624 21.3154C36.3775 22.2077 36.6539 22.7668 36.8811 23.527C37.558 25.7918 36.8817 27.8651 34.4539 29.2668C31.402 31.0288 28.723 29.4532 27.2339 26.1535C26.8955 25.4037 26.713 24.8486 26.5506 24.1446L26.4338 23.6039C26.2358 22.6625 26.044 22.1322 25.4324 21.0729C25.09 20.4799 24.6507 20.0381 23.9096 19.5093L23.4943 19.2238L22.8474 18.7975C21.0702 17.6241 20.0529 16.6871 19.1048 15.045L18.7925 14.4922C16.2718 9.92593 15.8108 6.39186 18.8202 4.65436C22.4482 2.55971 27.9295 5.60255 30.7984 10.5717ZM20.0702 6.81942C18.7154 7.60163 19.1287 10.0864 21.2699 13.795C21.9386 14.9533 22.642 15.6373 23.9379 16.519L25.0876 17.2831C26.2537 18.0792 27.0005 18.7889 27.5975 19.8229C28.2918 21.0255 28.5847 21.78 28.822 22.8223L28.9278 23.3156C29.0755 24.014 29.2083 24.4508 29.5126 25.1251C30.47 27.2466 31.7 27.97 33.2039 27.1017C34.5677 26.3143 34.848 25.4549 34.4858 24.2429C34.3479 23.7815 34.1658 23.3929 33.8305 22.7985L33.6973 22.5654C33.4806 22.19 33.2897 21.9155 32.7281 21.1796L31.9224 20.1268C31.3193 19.3275 30.934 18.7656 30.5577 18.1138C30.1106 17.3395 29.9132 16.7079 29.6179 15.2657L29.3846 14.1039C29.148 12.9779 28.9567 12.3817 28.6333 11.8217C26.365 7.89269 22.2081 5.58509 20.0702 6.81942Z",fill:"#303030"})))}var Et,Tt=i.forwardRef(St),It=(n.p,["title","titleId"]);function Nt(){return(Nt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Rt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _t(e,t){var n=e.title,r=e.titleId,a=Rt(e,It);return i.createElement("svg",Nt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Et||(Et=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 4C35.0457 4 44 12.9543 44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24C4 12.9543 12.9543 4 24 4ZM24 6.5C14.335 6.5 6.5 14.335 6.5 24C6.5 33.665 14.335 41.5 24 41.5C33.665 41.5 41.5 33.665 41.5 24C41.5 14.335 33.665 6.5 24 6.5ZM25 10.5V24.2923L34.8137 21.1807L35.5693 23.5637L24.1278 27.1915C23.3622 27.4343 22.5821 26.9035 22.5061 26.1249L22.5 26V10.5H25Z",fill:"#303030"})))}var kt,Pt=i.forwardRef(_t),At=(n.p,["title","titleId"]);function Mt(){return(Mt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Ht(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Dt(e,t){var n=e.title,r=e.titleId,a=Ht(e,At);return i.createElement("svg",Mt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,kt||(kt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.5 28.5C33.6756 28.5 36.25 31.0744 36.25 34.25C36.25 37.4256 33.6756 40 30.5 40C27.3244 40 24.75 37.4256 24.75 34.25C24.75 31.0744 27.3244 28.5 30.5 28.5ZM12.9654 38.0486L32.9654 8.54855C33.3528 7.97714 34.13 7.82797 34.7014 8.21537C35.2348 8.57694 35.4003 9.27813 35.105 9.8346L35.0346 9.95145L15.0346 39.4515C14.6472 40.0229 13.87 40.172 13.2986 39.7846C12.7652 39.4231 12.5997 38.7219 12.895 38.1654L12.9654 38.0486L32.9654 8.54855L12.9654 38.0486ZM30.5 31C28.7051 31 27.25 32.4551 27.25 34.25C27.25 36.0449 28.7051 37.5 30.5 37.5C32.2949 37.5 33.75 36.0449 33.75 34.25C33.75 32.4551 32.2949 31 30.5 31ZM17.5 8C20.6756 8 23.25 10.5744 23.25 13.75C23.25 16.9256 20.6756 19.5 17.5 19.5C14.3244 19.5 11.75 16.9256 11.75 13.75C11.75 10.5744 14.3244 8 17.5 8ZM17.5 10.5C15.7051 10.5 14.25 11.9551 14.25 13.75C14.25 15.5449 15.7051 17 17.5 17C19.2949 17 20.75 15.5449 20.75 13.75C20.75 11.9551 19.2949 10.5 17.5 10.5Z",fill:"#303030"})))}var Vt,Zt=i.forwardRef(Dt),Bt=(n.p,["title","titleId"]);function Wt(){return(Wt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Xt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ut(e,t){var n=e.title,r=e.titleId,a=Xt(e,Bt);return i.createElement("svg",Wt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Vt||(Vt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24.0001 28.9999C30.1162 28.9999 35.0924 33.8806 35.2464 39.9596L35.2501 40.2499H42.7501C43.4405 40.2499 44.0001 40.8096 44.0001 41.4999C44.0001 42.1472 43.5082 42.6795 42.8779 42.7435L42.7501 42.7499H5.2501C4.55974 42.7499 4.00009 42.1903 4.00009 41.4999C4.00009 40.8527 4.49197 40.3204 5.12229 40.2564L5.2501 40.2499H12.7501C12.7501 34.0367 17.7869 28.9999 24.0001 28.9999ZM24.0001 31.4999C19.2539 31.4999 15.3903 35.2788 15.2538 39.992L15.2501 40.2499H32.7501C32.7501 35.4175 28.8326 31.4999 24.0001 31.4999ZM43.9359 32.3547C44.1406 32.9687 43.8423 33.6292 43.2646 33.8893L43.1454 33.9358L39.3954 35.1858C38.7405 35.4041 38.0326 35.0502 37.8142 34.3952C37.6096 33.7812 37.9079 33.1207 38.4856 32.8606L38.6048 32.8141L42.3548 31.5641C43.0097 31.3458 43.7176 31.6997 43.9359 32.3547ZM4.06424 32.3547C4.26891 31.7407 4.90388 31.3912 5.52209 31.5298L5.64538 31.5641L9.39538 32.8141C10.0503 33.0324 10.4043 33.7403 10.1859 34.3952C9.98128 35.0092 9.34631 35.3587 8.7281 35.2201L8.60481 35.1858L4.85481 33.9358C4.19988 33.7175 3.84593 33.0096 4.06424 32.3547ZM37.384 23.1161C37.8396 23.5717 37.87 24.2915 37.4751 24.7823L37.384 24.8838L34.884 27.3838C34.3958 27.872 33.6044 27.872 33.1162 27.3838C32.6606 26.9282 32.6302 26.2084 33.0251 25.7176L33.1162 25.6161L35.6162 23.1161C36.1044 22.6279 36.8958 22.6279 37.384 23.1161ZM12.2825 23.0249L12.384 23.1161L14.884 25.6161C15.3721 26.1042 15.3721 26.8957 14.884 27.3838C14.4284 27.8394 13.7085 27.8698 13.2177 27.475L13.1162 27.3838L10.6162 24.8838C10.1281 24.3957 10.1281 23.6042 10.6162 23.1161C11.0393 22.693 11.6902 22.6366 12.174 22.9468L12.2825 23.0249ZM24.0001 4.73218L32.384 13.1161C32.8721 13.6042 32.8721 14.3957 32.384 14.8838C31.9284 15.3394 31.2085 15.3698 30.7177 14.975L30.6162 14.8838L25.2498 9.51718L25.2501 21.4999C25.2501 22.1472 24.7582 22.6795 24.1279 22.7435L24.0001 22.7499C23.3529 22.7499 22.8206 22.2581 22.7565 21.6278L22.7501 21.4999L22.7498 9.51718L17.384 14.8838C16.8958 15.372 16.1044 15.372 15.6162 14.8838C15.1606 14.4282 15.1302 13.7084 15.5251 13.2176L15.6162 13.1161L24.0001 4.73218Z",fill:"#303030"})))}var Gt,Ft=i.forwardRef(Ut),zt=(n.p,["title","titleId"]);function Yt(){return(Yt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Kt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function qt(e,t){var n=e.title,r=e.titleId,a=Kt(e,zt);return i.createElement("svg",Yt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Gt||(Gt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24.0001 29C30.1162 29 35.0924 33.8806 35.2464 39.9596L35.2501 40.25H42.7501C43.4405 40.25 44.0001 40.8096 44.0001 41.5C44.0001 42.1472 43.5082 42.6795 42.8779 42.7435L42.7501 42.75H5.2501C4.55974 42.75 4.00009 42.1904 4.00009 41.5C4.00009 40.8528 4.49197 40.3205 5.12229 40.2565L5.2501 40.25H12.7501C12.7501 34.0368 17.7869 29 24.0001 29ZM24.0001 31.5C19.2539 31.5 15.3903 35.2788 15.2538 39.992L15.2501 40.25H32.7501C32.7501 35.4175 28.8326 31.5 24.0001 31.5ZM43.9359 32.3547C44.1406 32.9687 43.8423 33.6293 43.2646 33.8893L43.1454 33.9359L39.3954 35.1859C38.7405 35.4042 38.0326 35.0502 37.8142 34.3953C37.6096 33.7813 37.9079 33.1207 38.4856 32.8607L38.6048 32.8141L42.3548 31.5641C43.0097 31.3458 43.7176 31.6998 43.9359 32.3547ZM5.52209 31.5299L5.64538 31.5641L9.39538 32.8141C10.0503 33.0325 10.4043 33.7404 10.1859 34.3953C9.98128 35.0093 9.34631 35.3587 8.7281 35.2201L8.60481 35.1859L4.85481 33.9359C4.19988 33.7175 3.84593 33.0096 4.06424 32.3547C4.26891 31.7407 4.90388 31.3913 5.52209 31.5299ZM37.384 23.1161C37.8396 23.5717 37.87 24.2915 37.4751 24.7824L37.384 24.8839L34.884 27.3839C34.3958 27.872 33.6044 27.872 33.1162 27.3839C32.6606 26.9283 32.6302 26.2085 33.0251 25.7176L33.1162 25.6161L35.6162 23.1161C36.1044 22.628 36.8958 22.628 37.384 23.1161ZM12.2825 23.025L12.384 23.1161L14.884 25.6161C15.3721 26.1043 15.3721 26.8957 14.884 27.3839C14.4284 27.8395 13.7085 27.8699 13.2177 27.475L13.1162 27.3839L10.6162 24.8839C10.1281 24.3957 10.1281 23.6043 10.6162 23.1161C11.0718 22.6605 11.7916 22.6301 12.2825 23.025ZM23.8723 4.00645L24.0001 4C24.6473 4 25.1796 4.49187 25.2436 5.12219L25.2501 5.25L25.2498 18.482L30.6162 13.1161C31.1044 12.628 31.8958 12.628 32.384 13.1161C32.8396 13.5717 32.87 14.2915 32.4751 14.7824L32.384 14.8839L24.0001 23.2678L15.6162 14.8839C15.1281 14.3957 15.1281 13.6043 15.6162 13.1161C16.0718 12.6605 16.7916 12.6301 17.2825 13.025L17.384 13.1161L22.7498 18.482L22.7501 5.25C22.7501 4.60279 23.242 4.07047 23.8723 4.00645L24.0001 4L23.8723 4.00645Z",fill:"#303030"})))}var Jt,$t=i.forwardRef(qt),Qt=(n.p,["title","titleId"]);function en(){return(en=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function tn(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function nn(e,t){var n=e.title,r=e.titleId,a=tn(e,Qt);return i.createElement("svg",en({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Jt||(Jt=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2906C10.9648 25.2906 11.4971 25.7824 11.5611 26.4128L11.5676 26.5406L11.567 38.2905L14.4249 34.862C14.8374 34.367 15.5515 34.2716 16.0761 34.6204L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.9321 3.91945 35.1439 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.862L9.067 38.2905L9.0676 26.5406C9.0676 25.9365 9.49608 25.4325 10.0657 25.316L10.1898 25.297L10.3176 25.2906ZM40.9818 17.6027L43.3727 16.872C44.8896 21.8355 43.6943 27.0563 40.0625 30.8638L39.8111 31.1212L38.0433 29.3534C41.128 26.2687 42.2085 22.0325 41.0783 17.9346L40.9818 17.6027ZM35.0312 6.73675L35.3638 6.8566L34.4873 9.19791C30.0881 7.55101 24.8855 8.58509 21.5594 11.9111C16.9622 16.5083 16.8856 23.723 21.3295 28.4074L21.5594 28.6434L19.7916 30.4111C14.1402 24.7597 14.1402 15.7948 19.7916 10.1434C23.7389 6.1961 29.8034 4.92483 35.0312 6.73675ZM36.3531 10.8046L41.3199 8.39509L38.5206 13.2002C35.4823 18.3879 33.456 21.634 32.3351 23.0794L32.0866 23.3882L31.9426 23.5534L31.8128 23.6902C29.9947 25.5083 27.1149 25.5083 25.2968 23.6902C23.4787 21.8722 23.4787 18.7406 25.2968 16.9225C26.0529 16.1664 28.3763 14.8138 32.4137 12.763L33.9577 11.9862L36.3531 10.8046ZM33.7479 16.3321L34.9873 14.2681L33.1718 15.185C29.6514 16.9836 27.5738 18.181 27.0646 18.6902C26.2228 19.532 26.2228 21.0807 27.0646 21.9225C27.9064 22.7642 29.2032 22.7642 30.045 21.9225C30.4163 21.5512 31.1706 20.4675 32.2842 18.7028L32.8095 17.8628L33.7479 16.3321Z",fill:"#303030"})))}var rn,an=i.forwardRef(nn),on=(n.p,["title","titleId"]);function ln(){return(ln=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function cn(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function sn(e,t){var n=e.title,r=e.titleId,a=cn(e,on);return i.createElement("svg",ln({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,rn||(rn=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.29L14.4249 34.8619C14.8374 34.3669 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.29L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM17 7V23.5H14.5V7H17ZM30.5 7V23.5H28V7H30.5ZM44 7V23.5H41.5V7H44ZM23.75 7V16.25H21.25V7H23.75ZM37.25 7V16.25H34.75V7H37.25Z",fill:"#303030"})))}var un,dn=i.forwardRef(sn),fn=(n.p,["title","titleId"]);function pn(){return(pn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function vn(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function mn(e,t){var n=e.title,r=e.titleId,a=vn(e,fn);return i.createElement("svg",pn({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,un||(un=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.2897L14.4249 34.8619C14.8374 34.3669 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.2897L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM28.5485 16.1712L30.2704 14.3587L44.3807 27.7635H16.6014V25.2635L28.7644 25.2634C28.9992 22.8955 29.8971 20.6726 31.3368 18.8207L28.5485 16.1712ZM38.1194 25.2635L33.1607 20.5527C32.1445 21.9285 31.4934 23.5432 31.2799 25.2628L38.1194 25.2635L33.1607 20.5527L38.1194 25.2635ZM25.9204 10.2262L28.8204 12.9812L27.0985 14.7937L24.1986 12.0387L25.9204 10.2262ZM21.5704 6.09375L24.4704 8.84875L22.7486 10.6612L19.8486 7.90625L21.5704 6.09375Z",fill:"#303030"})))}var bn,hn=i.forwardRef(mn),gn=(n.p,["title","titleId"]);function On(){return(On=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Cn(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function jn(e,t){var n=e.title,r=e.titleId,a=Cn(e,gn);return i.createElement("svg",On({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,bn||(bn=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.1898 25.297L10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.2906L14.4249 34.8619C14.8668 34.3316 15.6551 34.2599 16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.2906L9.0676 26.5405C9.0676 25.8933 9.55947 25.361 10.1898 25.297L10.3176 25.2905L10.1898 25.297ZM33.086 8.14544C33.3157 8.29441 33.5183 8.48054 33.6859 8.69578L33.8049 8.86254L44.7299 25.6142L42.6359 26.9798L31.7529 10.2926L25.7087 19.6107L23.4672 15.8748L16.3811 26.9762L14.2738 25.6311L21.4033 14.4616C22.1313 13.321 23.6461 12.9866 24.7867 13.7146C25.053 13.8846 25.2835 14.1043 25.4658 14.3609L25.5693 14.5193L25.7914 14.8893L29.6973 8.86763C30.4336 7.73243 31.9508 7.4091 33.086 8.14544Z",fill:"#303030"})))}var Ln,yn=i.forwardRef(jn),wn=(n.p,["title","titleId"]);function xn(){return(xn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Sn(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function En(e,t){var n=e.title,r=e.titleId,a=Sn(e,wn);return i.createElement("svg",xn({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Ln||(Ln=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.29L14.4249 34.8619C14.8374 34.3669 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.29L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM13.5322 6.21761L13.6233 6.11612C14.0789 5.6605 14.7987 5.63013 15.2896 6.02499L15.3911 6.11612L38.789 29.514L36.4202 16.4808C36.2967 15.8015 36.7472 15.1508 37.4264 15.0273C38.0632 14.9115 38.6749 15.3002 38.8507 15.9089L38.8799 16.0335L42.2886 34.7814L23.5407 31.3727C22.8615 31.2492 22.411 30.5985 22.5345 29.9193C22.6503 29.2825 23.2294 28.8467 23.861 28.8965L23.9879 28.913L37.021 31.282L13.6233 7.88388C13.1677 7.42827 13.1373 6.70845 13.5322 6.21761L13.6233 6.11612L13.5322 6.21761Z",fill:"#303030"})))}var Tn,In=i.forwardRef(En),Nn=(n.p,["title","titleId"]);function Rn(){return(Rn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function _n(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function kn(e,t){var n=e.title,r=e.titleId,a=_n(e,Nn);return i.createElement("svg",Rn({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,Tn||(Tn=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3176 25.2906C10.9648 25.2906 11.4971 25.7824 11.5611 26.4127L11.5676 26.5406L11.567 38.2907L14.4249 34.8619C14.8374 34.367 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.9321 3.91945 35.1439 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.2907L9.0676 26.5406C9.0676 25.9365 9.49608 25.4325 10.0657 25.316L10.1898 25.297L10.3176 25.2906ZM36.5214 4.76965L38.9786 5.23037L37.662 12.2497L42.25 12.25V14.75L37.193 14.7497L35.599 23.2497L40 23.25V25.75L35.131 25.7497L33.7286 33.2304L31.2714 32.7697L32.587 25.7497H27.631L26.2286 33.2304L23.7714 32.7697L25.087 25.7497L20.5 25.75V23.25L25.556 23.2497L27.15 14.7497L22.75 14.75V12.25L27.619 12.2497L29.0214 4.76965L31.4786 5.23037L30.162 12.2497H35.118L36.5214 4.76965ZM34.65 14.7497H29.693L28.099 23.2497H33.056L34.65 14.7497Z",fill:"#303030"})))}var Pn=i.forwardRef(kn),An=(n.p,{ICON_DURATION:u,ICON_HEART_RATE:b,ICON_HEART_RATE_PERCENTAGE:b,ICON_DISTANCE:L,ICON_NAUTICAL_DISTANCE:L,ICON_SWIM_DISTANCE:L,ICON_SPEED:T,ICON_NAUTICAL_SPEED:T,ICON_PACE:T,ICON_SWIM_PACE:T,ICON_ROWING_PACE:T,ICON_MAX_DEPTH:P,ICON_STROKES:Z,ICON_SWOLF:F,ICON_EMG:null,ICON_POWER:$,ICON_CALORIES:ie,ICON_CADENCE:ue,ICON_ASCENT:be,ICON_DESCENT:P,ICON_ASCENT_TIME:null,ICON_DESCENT_TIME:Le,ICON_FLAT_TIME:null,ICON_HIGHEST_POINT:null,ICON_LOWEST_POINT:null,ICON_TEMPERATURE:Te,ICON_WATER_TEMPERATURE:Te,ICON_EPOC:Pe,ICON_VO2:Ze,ICON_TRAINING_EFFECT:Fe,ICON_ALTITUDE:$e,ICON_AIR_PRESSURE:it,ICON_PERFORMANCE:ut,ICON_RECOVERY_TIME:bt,ICON_VERTICAL_SPEED:Lt,ICON_STEPS:Tt,ICON_TIME_OF_DAY:Pt,ICON_COUNT:null,ICON_PERCENTAGE:Zt,ICON_SUNRISE:Ft,ICON_SUNSET:$t,ICON_DOWNHILL_SPEED:an,ICON_DOWNHILL_DISTANCE:dn,ICON_DOWNHILL_DURATION:Le,ICON_DOWNHILL_GRADE:hn,ICON_DOWNHILL_ALTITUDE:yn,ICON_DOWNHILL_DESCENT:In,ICON_DOWNHILL_LAP_COUNT:Pn});t.a=An},686:function(e,t,n){"use strict";var r=n(20),i=n(663),a=(n(0),n(667)),o=n.n(a),l=n(661),c=n(665),s=n(60),u=n(29),d=n(710),f={MaxHeartRate:"".concat(s.a.PHRASES,":TXT_MAX_HR"),AvgHeartrate:"".concat(s.a.PHRASES,":TXT_AVG_HR"),AscentAltitude:"".concat(s.a.PHRASES,":TXT_TOTAL_ASCENT"),DescentAltitude:"".concat(s.a.TRANSLATIONS,":TOTAL_DESCENT"),HighAltitude:"".concat(s.a.PHRASES,":TXT_MAX_ALTITUDE"),LowAltitude:"".concat(s.a.PHRASES,":TXT_MIN_ALTITUDE"),AscentTime:"".concat(s.a.TRANSLATIONS,":ASCENT_TIME"),DescentTime:"".concat(s.a.TRANSLATIONS,":DESCENT_TIME"),EstVO2peak:"".concat(s.a.PHRASES,":TXT_VO2MAX"),Feeling:"".concat(s.a.PHRASES,":TXT_VIBES"),Energy:"".concat(s.a.PHRASES,":TXT_ENERGY_CONSUMPTION"),PeakEpoc:"".concat(s.a.PHRASES,":TXT_EPOC"),DiveSurfaceTime:"".concat(s.a.PHRASES,":TXT_SURFACE_TIME"),DiveMaxDepthTemperature:"".concat(s.a.PHRASES,":TXT_TEMP_MAX_DEPTH"),DiveVisibility:"".concat(s.a.TRANSLATIONS,":DIVE_VISIBILITY"),DiveNumberInSeries:"".concat(s.a.TRANSLATIONS,":DIVE_NUMBER_IN_SERIES"),AvgSWOLF:"".concat(s.a.PHRASES,":TXT_AVERAGE_SWOLF"),SkiTime:"".concat(s.a.TRANSLATIONS,":SKI_TIME"),SkiDistance:"".concat(s.a.TRANSLATIONS,":SKI_DISTANCE"),SkiRuns:"".concat(s.a.TRANSLATIONS,":SKI_RUNS"),AvgSkiSpeed:"".concat(s.a.TRANSLATIONS,":AVG_SKI_SPEED"),MaxSkiSpeed:"".concat(s.a.TRANSLATIONS,":MAX_SKI_SPEED")},p=Object(u.a)(Object(u.a)({},d),f),v=n(3),m=Object(l.a)((function(e){return{root:{display:"flex"},small:{},icon:{marginTop:"1.1rem",fontSize:"1.7rem",fontFamily:"Suunto Icons",width:"".concat(2,"rem"),height:"".concat(2,"rem")},left:{display:"flex",alignItems:"flex-start",justifyContent:"center",width:"".concat(6,"rem"),flexGrow:0},right:{flexGrow:1},value:{fontWeight:"bold","&$small":{fontWeight:"normal"},"& svg":{width:"1.7rem",height:"1.7rem",marginTop:"-0.2rem",marginBottom:"-0.4rem"}},unit:{},label:{lineHeight:3/4,fontWeight:"bold","&$small":{lineHeight:1.05,paddingTop:e.spacing(1),fontWeight:"normal"}}}}),{name:"SummaryItemView"});t.a=function(e){var t=m(e),n=Object(c.a)([s.a.PHRASES,s.a.TRANSLATIONS]).t,a=e.unit,l=e.value,u=e.name,d=e.small,f=void 0!==d&&d,b=e.Icon;return Object(v.jsxs)("div",{className:t.root,children:[!f&&Object(v.jsx)("div",{className:t.left,children:b&&Object(v.jsx)(b,{className:t.icon})}),Object(v.jsxs)("div",{className:t.right,children:[Object(v.jsxs)("div",{children:[Object(v.jsx)(i.a,{component:"span",variant:f?"h5":"h1",className:o()(t.value,Object(r.a)({},t.small,f)),children:Object(v.jsx)("strong",{children:l})})," ",!!a&&Object(v.jsx)(i.a,{component:"span",variant:"h5",className:o()(t.unit,Object(r.a)({},t.small,f)),children:Object(v.jsx)("strong",{children:a})})]}),Object(v.jsx)(i.a,{component:"div",variant:f?"body2":"h6",className:o()(t.label,Object(r.a)({},t.small,f)),children:n(p[u],{defaultValue:u})})]})]})}},696:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r,i=n(11),a=n(0),o=n.n(a),l=n(657),c=n(661),s=n(804),u=n(806),d=n(665),f=n.p+"static/media/satellite.f137ef94.png",p=n.p+"static/media/offroad.d02c2b57.png",v=n.p+"static/media/ski.348a69be.png",m=n(3);!function(e){e.satellite="satellite",e.offroad="offroad",e.ski="ski"}(r||(r={}));var b=Object(c.a)((function(e){return{root:{},fab:{color:e.palette.text.secondary,backgroundColor:e.palette.background.default,"&:hover":{backgroundColor:Object(l.c)(e.palette.background.default,.15)},transition:"".concat(e.transitions.create("transform",{duration:e.transitions.duration.shorter}),", opacity 0.8s"),opacity:1},icon:{width:"3rem",height:"3rem",color:e.palette.text.primary},iconSmall:{width:"2rem",height:"2rem",color:e.palette.text.primary},tooltip:{display:"none"},mainIconWrapper:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},mainIcon:{width:"100%",height:"100%",borderRadius:"50%"}}}),{name:"PathColorDataSelector"}),h=function(e){switch(e){case r.satellite:return f;case r.offroad:return p;case r.ski:return v}};t.b=function(e){var t=b(e),n=e.value,r=e.onChange,a=e.styles,l=o.a.useState(!1),c=Object(i.a)(l,2),f=c[0],p=c[1],v=o.a.useState(!1),g=Object(i.a)(v,1)[0],O=(Object(d.a)().t,function(){return p(!1)}),C=h(n);return a&&n&&C?Object(m.jsx)(s.a,{className:t.root,ariaLabel:"",hidden:g,icon:Object(m.jsx)("div",{className:t.mainIconWrapper,children:Object(m.jsx)("img",{className:t.mainIcon,src:C})}),onClose:O,onOpen:function(){return p(!0)},open:f,FabProps:{classes:{root:t.fab}},children:a.filter((function(e){return e!==n})).map((function(e){var n=h(e);return n?Object(m.jsx)(u.a,{TooltipClasses:{tooltip:t.tooltip},icon:Object(m.jsx)("img",{className:t.mainIcon,src:n}),FabProps:{classes:{root:t.fab}},onClick:function(){O(),r(e)}},e):null}))}):null}},710:function(e){e.exports=JSON.parse('{"Duration":"TXT_DURATION","Distance":"TXT_DISTANCE","AvgPace":"TXT_AVG_PACE","AvgHeartrate":"TXT_AVG_HEARTRATE","MaxHeartRate":"TXT_MAX_HEART_RATE","Energy":"TXT_ENERGY","RecoveryTime":"TXT_RECOVERY_TIME","Pte":"TXT_PTE","PerformanceLevel":"TXT_PERFORMANCE_LEVEL","AvgSpeed":"TXT_AVG_SPEED","AvgCadence":"TXT_AVG_CADENCE","Steps":"TXT_STEPS","AscentAltitude":"TXT_ASCENT_ALTITUDE","DescentAltitude":"TXT_DESCENT_ALTITUDE","HighAltitude":"TXT_HIGH_ALTITUDE","LowAltitude":"TXT_LOW_ALTITUDE","AvgTemperature":"TXT_AVG_TEMPERATURE","PeakEpoc":"TXT_PEAK_EPOC","Feeling":"TXT_FEELING","MoveType":"TXT_MOVE_TYPE","Catch:Fish":"TXT_CATCH_FISH","Catch:BigGame":"TXT_CATCH_BIG_GAME","Catch:SmallGame":"TXT_CATCH_SMALL_GAME","Catch:Bird":"TXT_CATCH_BIRD","Catch:ShotCount":"TXT_CATCH_SHOT_COUNT","AvgPower":"TXT_AVG_POWER","AvgSWOLF":"TXT_AVG_SWOLF","AvgNauticalSpeed":"TXT_AVG_NAUTICAL_SPEED","MaxNauticalSpeed":"TXT_MAX_NAUTICAL_SPEED","MaxSpeed":"TXT_MAX_SPEED","MaxDepth":"TXT_MAX_DEPTH","DiveTime":"TXT_DIVE_TIME","DiveMode":"TXT_DIVE_MODE","DiveNumberInSeries":"TXT_DIVE_NUMBER_IN_SERIES","DiveSurfaceTime":"TXT_DIVE_SURFACE_TIME","DiveVisibility":"TXT_DIVE_VISIBILITY","DiveMaxDepthTemperature":"TXT_DIVE_MAX_DEPTH_TEMPERATURE","SkiRuns":"TXT_SKI_RUNS","SkiDistance":"TXT_SKI_DISTANCE","SkiTime":"TXT_SKI_TIME","AvgSkiSpeed":"TXT_AVG_SKI_SPEED","MaxSkiSpeed":"TXT_MAX_SKI_SPEED","AscentTime":"TXT_ASCENT_TIME","DescentTime":"TXT_DESCENT_TIME","EstVO2peak":"TXT_EST_VO2PEAK"}')},784:function(e,t,n){"use strict";n.r(t);var r=n(11),i=n(29),a=n(20),o=n(551),l=n(658),c=n(803),s=n(0),u=n.n(s),d=n(661),f=n(93),p=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=u.a.useState(e),a=Object(r.a)(i,2),o=a[0],l=a[1],c=u.a.useCallback((function(){return l(n)}),[n]),s=u.a.useCallback((function(){return l(t)}),[t]);return[o,s,c]},v=n(208),m=n(209),b=n(796),h=n(797),g=n(3),O=Object(d.a)({root:function(e){return{textAlign:"center",alignItems:"center",justifyContent:"center",display:"flex",width:"100%",height:e.linear?void 0:"100%",top:0,left:0}},indicatorWrapper:function(e){var t=e.linear;return{width:t?"100%":40,height:t?void 0:40}},indicator:{}},{name:"LoadingIndicator"}),C={width:"100%",height:"100%"};var j,L=function(e){var t=O(e),n=e.active,r=void 0!==n&&n,i=e.linear,a=void 0!==i&&i;if(!r)return null;var o=a?b.a:h.a;return Object(g.jsx)("div",{className:t.root,children:Object(g.jsx)("div",{className:t.indicatorWrapper,children:Object(g.jsx)(o,{color:"primary",className:t.indicator,style:a?void 0:C})})})},y=n(790),w=n(799),x=n(800),S=n(663),E=n(665),T=n(60),I=["title","titleId"];function N(){return(N=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function R(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _(e,t){var n=e.title,r=e.titleId,i=R(e,I);return s.createElement("svg",N({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,j||(j=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.512 3.48065L19.4221 3.50043C10.0256 5.58956 3 13.9743 3 24C3 34.0257 10.0256 42.4104 19.4221 44.4996C20.5561 44.7517 21.7245 44.9121 22.9193 44.9727L23.0008 44.9766C23.3319 44.9922 23.6651 45 24 45C24.335 45 24.6681 44.9922 24.9992 44.9766C26.1908 44.9208 27.3564 44.7657 28.488 44.5194L28.5779 44.4996C37.9744 42.4104 45 34.0257 45 24C45 13.9743 37.9744 5.58955 28.5779 3.50043C27.8685 3.34272 27.1457 3.22089 26.4114 3.13694C26.2731 3.12114 26.1345 3.10668 25.9955 3.09357C25.9134 3.08584 25.8313 3.07858 25.7489 3.07179C25.1723 3.02425 24.589 3 24 3C23.2212 3 22.4523 3.04239 21.6955 3.125C20.9554 3.20578 20.2269 3.32501 19.512 3.48065ZM22.2119 42.3695L22.1657 42.4102C13.219 41.5296 6.14375 34.2756 5.54156 25.25H15.0284C15.3293 31.8517 18.0009 37.8354 22.2119 42.3695ZM15.0284 22.75C15.3293 16.1483 18.0009 10.1646 22.2119 5.63052L22.1657 5.58979C13.219 6.4704 6.14375 13.7244 5.54156 22.75H15.0284ZM17.5313 22.75C17.8289 16.8284 20.2289 11.461 24 7.37905C27.7711 11.461 30.1711 16.8284 30.4687 22.75H17.5313ZM17.5313 25.25H30.4687C30.1711 31.1716 27.7711 36.539 24 40.6209C20.2289 36.539 17.8289 31.1716 17.5313 25.25ZM32.9716 25.25C32.6707 31.8517 29.9991 37.8354 25.7881 42.3695L25.8343 42.4102C34.781 41.5296 41.8563 34.2756 42.4584 25.25H32.9716ZM42.4584 22.75C41.8563 13.7244 34.781 6.4704 25.8343 5.58979L25.7881 5.63051C29.9991 10.1646 32.6707 16.1483 32.9716 22.75H42.4584Z",fill:"#303030"})))}var k,P=s.forwardRef(_),A=(n.p,n(786)),M=n(193),H=["title","titleId"];function D(){return(D=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function V(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Z(e,t){var n=e.title,r=e.titleId,i=V(e,H);return s.createElement("svg",D({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,k||(k=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.86612 16.1161C8.35427 15.628 9.14573 15.628 9.63388 16.1161L24 30.4822L38.3661 16.1161C38.8543 15.628 39.6457 15.628 40.1339 16.1161C40.622 16.6043 40.622 17.3957 40.1339 17.8839L24 34.0178L7.86612 17.8839C7.37796 17.3957 7.37796 16.6043 7.86612 16.1161Z",fill:"#303030"})))}var B=s.forwardRef(Z),W=(n.p,Object(d.a)({root:{},downIcon:{width:"2rem",height:"2rem",marginTop:"-0.4rem"},select:{textTransform:"uppercase",padding:0}},{name:"LanguageSelect"}));var X=function(e){var t,n=W(e),r=e.id,i=Object(E.a)().i18n,a=(null===(t=localStorage.getItem("i18nextLng"))||void 0===t?void 0:t.split("-")[0])||i.language||"zh";return Object(g.jsx)(A.a,{id:r,defaultValue:i.language,value:a,onChange:function(e){var t=e.target.value;return t&&i.changeLanguage(t)},disableUnderline:!0,classes:{root:n.root,icon:n.downIcon,select:n.select},IconComponent:B,children:Object.values(M.a).map((function(e){var t=e.language,n=e.label;return Object(g.jsx)("option",{value:t,children:n},t)}))})},U=n(794),G=n(798),F=n(787),z=n(124),Y=Object(d.a)({root:{},radioLabel:{marginRight:0,textTransform:"uppercase",display:"flex",flexDirection:"row-reverse",marginLeft:0,justifyContent:"space-between",flexGrow:1},input:{flexGrow:1}},{name:"MeasurementSystemInput"});var K=function(e){var t=Y(e),n=e.id,i=Object(s.useContext)(z.b),a=Object(r.a)(i,2),o=a[0],l=a[1],c=Object(E.a)([T.a.PHRASES]).t;return Object(g.jsxs)(U.a,{color:"primary",value:o,id:n,className:t.root,onChange:function(e){var t=e.target.value;t&&l(t)},children:[Object(g.jsx)(G.a,{className:t.radioLabel,value:z.a.metric,control:Object(g.jsx)(F.a,{size:"large"}),label:Object(g.jsx)("span",{children:c("TXT_METRIC")})}),Object(g.jsx)(G.a,{className:t.radioLabel,value:z.a.imperial,control:Object(g.jsx)(F.a,{size:"large"}),label:Object(g.jsx)("span",{children:c("TXT_IMPERIAL")})})]})},q=Object(d.a)((function(e){return{root:{},item:{marginTop:e.spacing(2),marginBottom:e.spacing(2)},iconAndInput:{marginTop:e.spacing(2),display:"flex",alignItems:"center"},icon:{height:"3rem",width:"3rem",marginRight:e.spacing(2)},input:{flexGrow:1}}}),{name:"Preferences"}),J=Object(d.a)({languageSelect:{},toggleButton:{}},{name:"PreferencesIds"});var $=function(e){var t=q(e),n=Object(E.a)([T.a.PHRASES]).t,r=J();return Object(g.jsxs)(g.Fragment,{children:[Object(g.jsxs)(w.a,{className:t.item,children:[Object(g.jsx)(x.a,{component:"legend",htmlFor:r.languageSelect,children:Object(g.jsx)(S.a,{variant:"h5",children:n("TXT_LANGUAGE")})}),Object(g.jsxs)("div",{className:t.iconAndInput,children:[Object(g.jsx)(P,{className:t.icon}),Object(g.jsx)(X,{id:r.languageSelect,classes:{root:t.input}})]})]}),Object(g.jsxs)(w.a,{className:t.item,component:"fieldset",children:[Object(g.jsx)(x.a,{component:"legend",htmlFor:r.toggleButton,children:Object(g.jsx)(S.a,{variant:"h5",children:n("TXT_UNITS")})}),Object(g.jsxs)("div",{className:t.iconAndInput,children:[Object(g.jsx)(P,{className:t.icon}),Object(g.jsx)(K,{id:r.toggleButton,classes:{root:t.input}})]})]})]})},Q=Object(d.a)((function(e){return{root:{},logo:{display:"flex",justifyContent:"center"},paper:{padding:e.spacing(2),width:300,maxWidth:"100%",background:e.palette.background.default},preferences:{marginTop:e.spacing(1),display:"flex",flexDirection:"column"}}}),{name:"Menu"});var ee=function(e){var t=e.open,n=e.onClose,r=Q(e),i=Object(o.a)();return Object(g.jsxs)(y.a,{classes:{root:r.root,paper:r.paper},open:t,anchor:"left",onClose:n,children:[Object(g.jsx)("a",{href:"https://suunto.com",target:"_blank",className:r.logo,children:Object(g.jsx)("img",{src:"dark"===i.palette.mode?m.a:v.a,alt:"Suunto logo"})}),Object(g.jsx)("section",{className:r.preferences,children:Object(g.jsx)($,{})})]})},te=n(667),ne=n.n(te),re=n(125),ie=n(98),ae=function(e){var t;return{links:Array.from(e.querySelectorAll(".list__item a")).map((function(e){return{href:e.href,title:(e.textContent||"").trim()}})),title:((null===(t=e.querySelector(".list__title"))||void 0===t?void 0:t.textContent)||"").trim()}},oe=function(e){var t=function(e){var t=document.implementation.createHTMLDocument(),n=t.createElement("html");n.innerHTML=e;var r=t.createElement("base");return r.href="https://www.suunto.com",t.getElementsByTagName("head")[0].appendChild(r),n.querySelector("footer.footer")}(e),n=function(e){var t,n=null===e||void 0===e?void 0:e.querySelector(".footer__social-networks");return{title:(null===n||void 0===n||null===(t=n.querySelector("p"))||void 0===t?void 0:t.textContent)||"",links:Array.from((null===n||void 0===n?void 0:n.querySelectorAll("a[data-footer-some-link]"))||[]).map((function(e){return{href:e.href,title:e.title,some:e.getAttribute("data-footer-some-link")}}))}}(t),r=function(e){return Array.from((null===e||void 0===e?void 0:e.querySelectorAll(".footer__links-container"))||[]).map(ae)}(t),i=function(e){return Array.from((null===e||void 0===e?void 0:e.querySelectorAll(".footer__bottom-page a"))||[]).map((function(e){return{href:e.href,title:(e.textContent||"").trim()}}))}(t);return{copyrights:function(e){return Array.from((null===e||void 0===e?void 0:e.querySelectorAll(".footer__bottom-page span"))||[]).map((function(e){return{title:(e.textContent||"").trim()}}))}(t),someLinks:n,linkGroups:r,terms:i}},le=Object(d.a)((function(e){return{root:{textAlign:"center"},item:{marginLeft:e.spacing(.4),marginRight:e.spacing(.4),textTransform:"uppercase"}}}),{name:"Copyrights"});var ce=function(e){var t=le(e),n=e.copyrights;return n?Object(g.jsx)("section",{className:t.root,children:n.map((function(e){var n=e.title;return Object(g.jsx)("span",{className:t.item,children:n},n)}))}):null},se=n(802),ue=Object(d.a)((function(e){return{root:{display:"flex",flexWrap:"wrap"},group:{padding:e.spacing(.8),flexBasis:120,flexGrow:1},title:{},list:{listStyle:"none",padding:0},item:{marginBottom:e.spacing(.6)}}}),{name:"Groups"});var de=function(e){var t=ue(e),n=e.linkGroups;return n?Object(g.jsx)("section",{className:t.root,children:n.map((function(e){var n=e.title,r=e.links;return Object(g.jsxs)("div",{className:t.group,children:[Object(g.jsx)(S.a,{component:"h2",variant:"h5",className:t.title,children:Object(g.jsx)("strong",{children:n})}),Object(g.jsx)("ul",{className:t.list,children:r.map((function(e){var n=e.href,r=e.title;return Object(g.jsx)("li",{className:t.item,children:Object(g.jsx)(se.a,{href:n,color:"inherit",target:"_blank",rel:"external nofollow noopener",underline:"none",children:r})},n)}))})]},n)}))}):null},fe=n.p+"static/media/Suuntocom-icons.1a0a819c.woff2",pe=Object(d.a)((function(e){return{"@global":{"@font-face":{fontFamily:"Suuntocom Icons",fontWeight:"normal",src:'url("'.concat(fe,'") format("woff2")')}},root:{marginBottom:e.spacing(2),textAlign:"center"},title:{},link:{"&:hover":{color:e.palette.primary.dark},margin:e.spacing(.4),fontSize:"3rem",fontFamily:"Suuntocom Icons"}}}),{name:"Some"}),ve=[{Name:"Twitter",iconRound:"\ue926"},{Name:"Facebook",iconRound:"\ue917"},{Name:"Youtube",iconRound:"\ue928"},{Name:"Instagram",iconRound:"\ue919"},{Name:"Pinterest",iconRound:"\ue91d"},{Name:"RSS Feed",iconRound:"\ue9af"},{Name:"Youko",iconRound:"\ue9b1"},{Name:"Weibo",iconRound:"\ue9b0"}];var me=function(e){var t=pe(e),n=e.someLinks;return n?Object(g.jsxs)("section",{className:t.root,children:[Object(g.jsx)(S.a,{component:"h2",variant:"h5",className:t.title,children:Object(g.jsx)("strong",{children:n.title})}),n.links.map((function(e){var n=e.title,r=e.href,i=e.some;return Object(g.jsx)(se.a,{className:t.link,href:r,color:"inherit",target:"_blank",rel:"external nofollow noopener","aria-label":n,underline:"none",title:n,children:(Object.values(ve).find((function(e){return e.Name===i}))||{}).iconRound||""},i)}))]}):null},be=Object(d.a)((function(e){return{root:{textAlign:"center"},term:{marginLeft:e.spacing(.4),marginRight:e.spacing(.4),display:"inline-block"}}}),{name:"Terms"});var he=function(e){var t=be(e),n=e.terms;return n?Object(g.jsx)("section",{className:t.root,children:n.map((function(e){var n=e.title,r=e.href;return Object(g.jsx)(se.a,{className:t.term,href:r,color:"inherit",target:"_blank",rel:"external nofollow noopener",children:n},r)}))}):null},ge=Object(d.a)((function(e){return{root:{position:"relative"},someTitle:{marginBottom:e.spacing(1.5)},groupTitle:{textTransform:"uppercase"},logoWrapper:{display:"block",margin:e.spacing(2),textAlign:"center"}}}),{name:"Footer"});var Oe,Ce=function(e){var t=ge(e),n=u.a.useState(null),i=Object(r.a)(n,2),a=i[0],l=i[1],c=Object(E.a)().i18n,s=Object(o.a)();return u.a.useEffect((function(){Object(re.c)(c.language).then((function(e){l(oe(e))})).catch(ie.a)}),[c.language]),Object(g.jsxs)("footer",{className:t.root,children:[Object(g.jsx)(me,{someLinks:null===a||void 0===a?void 0:a.someLinks,classes:{title:ne()(t.groupTitle,t.someTitle)}}),Object(g.jsx)(de,{linkGroups:null===a||void 0===a?void 0:a.linkGroups,classes:{title:ne()(t.groupTitle)}}),Object(g.jsx)("a",{href:"https://suunto.com",target:"_blank",className:t.logoWrapper,children:Object(g.jsx)("img",{src:"dark"===s.palette.mode?m.a:v.a,alt:"Suunto logo"})}),Object(g.jsx)(ce,{copyrights:null===a||void 0===a?void 0:a.copyrights}),Object(g.jsx)(he,{terms:null===a||void 0===a?void 0:a.terms})]})},je=n(57),Le=n(676),ye=n(791),we=["title","titleId"];function xe(){return(xe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Se(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ee(e,t){var n=e.title,r=e.titleId,i=Se(e,we);return s.createElement("svg",xe({width:48,height:49,viewBox:"0 0 48 49",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,Oe||(Oe=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M40.2573 24.4073L15.566 8.39867C14.8709 7.94799 13.9421 8.14615 13.4914 8.84126C13.3338 9.08426 13.25 9.36767 13.25 9.65728V39.1573C13.25 39.9857 13.9216 40.6573 14.75 40.6573C15.0396 40.6573 15.323 40.5734 15.566 40.4159L40.2573 24.4073Z",fill:"#303030"})))}var Te,Ie=s.forwardRef(Ee),Ne=(n.p,["title","titleId"]);function Re(){return(Re=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function _e(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ke(e,t){var n=e.title,r=e.titleId,i=_e(e,Ne);return s.createElement("svg",Re({width:48,height:49,viewBox:"0 0 48 49",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,Te||(Te=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.5 8.40723V40.4072H11.5V8.40723H20.5ZM36.5 8.40723V40.4072H27.5V8.40723H36.5Z",fill:"#303030"})))}var Pe=s.forwardRef(ke),Ae=(n.p,n(214)),Me=Object(d.a)({noScroll:{overflow:"hidden !important"},root:{touchAction:"auto"}},{name:"ScrollableSlider"});var He=function(e){var t=Me(e),n=u.a.useRef(0),a=p(),o=Object(r.a)(a,3),l=o[0],c=o[1],s=o[2];return u.a.useEffect((function(){return l&&document.documentElement.classList.add(t.noScroll),function(){document.documentElement.classList.remove(t.noScroll)}}),[t.noScroll,c]),Object(g.jsx)(ye.a,Object(i.a)({onTouchStartCapture:function(e){n.current=e.touches[0].clientY,e.nativeEvent.preventDefault=ie.a,c()},onTouchEnd:s,onTouchCancel:s,onTouchMove:function(e){var t=e.touches[0].clientY-n.current;Math.abs(t)>30&&s()},classes:Object(i.a)(Object(i.a)({},e.classes),{},{root:t.root})},e))},De=Object(d.a)((function(e){return{root:{display:"flex",alignItems:"center",paddingRight:e.spacing(2),paddingLeft:e.spacing(.5)},iconButton:{color:e.palette.text.primary,marginRight:e.spacing(2)},loadingIndicatorRoot:{position:"absolute"},loadingIndicator:{width:"100%",height:"100%"},icon:{width:"2.7rem",height:"2.7rem"},thumb:{height:e.spacing(1.2),width:e.spacing(1.2)},rail:{backgroundColor:e.palette.divider,height:6,boxShadow:"1px 1px 2px 0px #0000004A inset",marginTop:0,borderRadius:999}}}),{name:"MapControl"});var Ve=function(e){var t=De(e),n=e.useScrollableSlider,r=Object(E.a)(T.a.CONTROLS).t,i=u.a.useContext(Ae.a),a=i.position,o=i.isPlaying,l=i.setPosition,s=i.setIsPlaying,d=i.isMapInitialized,f=o?Pe:Ie,p=n?He:ye.a;return Object(g.jsxs)("div",{className:t.root,children:[Object(g.jsxs)(c.a,{title:r(o?"PAUSE":"PLAY"),classes:{root:t.iconButton},onClick:function(){o||1!=a||l(0),s(!o)},size:"small",edge:"start",children:[Object(g.jsx)(f,{className:t.icon}),Object(g.jsx)(L,{active:!d&&o,classes:{root:t.loadingIndicatorRoot,indicatorWrapper:t.loadingIndicator}})]}),Object(g.jsx)(p,{classes:{thumb:t.thumb,rail:t.rail},track:!1,max:1,min:0,step:1e-4,color:"body",onChange:function(e,t){s(!1),l("number"===typeof t?t:t[0])},value:a})]})},Ze=Object(d.a)((function(e){return{root:{textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center"},img:{height:"2rem",marginTop:e.spacing(1),marginBottom:e.spacing(1)},mapControl:{flexGrow:1,width:"900px",maxWidth:"100%"},downIcon:{height:"3rem",width:"3rem"},buttonDown:{marginBottom:-12}}}));var Be=function(e){var t=Ze(e),n=e.summaryScrollRef,r=e.showMapControl,i=Object(o.a)(),a=Object(E.a)().t,s=Object(l.a)(i.breakpoints.up(Le.a));return s&&!r?null:Object(g.jsxs)("div",{className:t.root,children:[!s&&Object(g.jsx)("a",{href:"https://suunto.com",target:"_blank",children:Object(g.jsx)("img",{src:"dark"===i.palette.mode?m.a:v.a,alt:"Suunto logo",className:t.img})}),r&&Object(g.jsx)(Ve,{useScrollableSlider:!s,classes:{root:t.mapControl}}),!s&&Object(g.jsx)(c.a,{size:"small",className:t.buttonDown,onClick:function(){var e;return null===(e=n.current)||void 0===e?void 0:e.scrollIntoView({behavior:"smooth"})},"aria-label":a("JUMP_TO_SUMMARY"),children:Object(g.jsx)(B,{className:t.downIcon})})]})},We=n(707),Xe=21,Ue=-1,Ge=function(e){return We.find((function(t){return t.STId===e}))},Fe=n(211),ze=n(52),Ye=n(708),Ke=function(e){return Ye.find((function(t){return t.Activities.includes(e)}))},qe=function(e){var t;return null===(t=Ke(e))||void 0===t?void 0:t.Items},Je={Heartrate:"HeartRate"},$e=function(e){return Je[e]||e},Qe=function(e){var t=Ke(e);if(t){var n=t.Graphs,r=void 0===n?[]:n,i=t.ZoneGraphs,a=void 0===i?[]:i;return[].concat(Object(ze.a)(a),Object(ze.a)(r)).map($e)}},et="Fallback",tt=n(785),nt=n(657),rt=n(804),it=n(806),at=n(749),ot=n(685),lt=Object(d.a)((function(e){return{root:{},fab:{color:e.palette.text.secondary,backgroundColor:e.palette.background.default,"&:hover":{backgroundColor:Object(nt.c)(e.palette.background.default,.15)},transition:"".concat(e.transitions.create("transform",{duration:e.transitions.duration.shorter}),", opacity 0.8s"),opacity:1},icon:{width:"3rem",height:"3rem",color:e.palette.text.primary},iconSmall:{width:"2rem",height:"2rem",color:e.palette.text.primary},tooltip:{fontSize:"1.4rem",fontWeight:"normal"},mainIconWrapper:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}}}),{name:"PathColorDataSelector"}),ct=function(e){return e?ot.a["ICON_".concat(Object(at.a)(e).toUpperCase())]:null};var st,ut=function(e){var t=lt(e),n=e.value,i=e.onChange,a=e.graphs,o=u.a.useState(!1),l=Object(r.a)(o,2),c=l[0],s=l[1],d=u.a.useState(!1),f=Object(r.a)(d,1)[0],p=Object(E.a)().t,v=function(){return s(!1)},m=ct(n);return a&&n&&m?Object(g.jsx)(rt.a,{className:t.root,ariaLabel:p("TXT_".concat(Object(at.a)(n).toUpperCase())),hidden:f,icon:Object(g.jsx)(tt.a,{classes:{tooltip:t.tooltip},title:Object(g.jsx)("span",{children:p("TXT_".concat(Object(at.a)(n).toUpperCase()))}),placement:"left",children:Object(g.jsx)("div",{className:t.mainIconWrapper,children:Object(g.jsx)(m,{className:t.icon})})}),onClose:v,onOpen:function(){return s(!0)},open:c,FabProps:{classes:{root:t.fab}},children:a.filter((function(e){return e!==n})).map((function(e){var n=ct(e);return n?Object(g.jsx)(it.a,{TooltipClasses:{tooltip:t.tooltip},icon:Object(g.jsx)(n,{className:t.iconSmall}),tooltipTitle:p("TXT_".concat(Object(at.a)(e).toUpperCase())),FabProps:{classes:{root:t.fab}},onClick:function(){v(),i(e)}},e):null}))}):null},dt=n(696),ft=n(781),pt=n(805),vt=["title","titleId"];function mt(){return(mt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function bt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ht(e,t){var n=e.title,r=e.titleId,i=bt(e,vt);return s.createElement("svg",mt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,st||(st=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 21.879L37.6893 8.18934C38.2751 7.60355 39.2249 7.60355 39.8107 8.18934C40.3598 8.73851 40.3942 9.60758 39.9136 10.1967L39.8107 10.3107L26.121 24L39.8107 37.6893C40.3598 38.2385 40.3942 39.1076 39.9136 39.6967L39.8107 39.8107C39.2615 40.3598 38.3924 40.3942 37.8033 39.9136L37.6893 39.8107L24 26.121L10.3107 39.8107C9.76149 40.3598 8.89242 40.3942 8.30326 39.9136L8.18934 39.8107C7.64017 39.2615 7.60584 38.3924 8.08637 37.8033L8.18934 37.6893L21.879 24L8.18934 10.3107C7.64017 9.76149 7.60584 8.89242 8.08637 8.30326L8.18934 8.18934C8.73851 7.64017 9.60758 7.60584 10.1967 8.08637L10.3107 8.18934L24 21.879L37.6893 8.18934L24 21.879Z",fill:"#303030"})))}var gt=s.forwardRef(ht),Ot=(n.p,Object(d.a)((function(e){return{root:{bottom:35,zIndex:10,marginLeft:"50%",marginRight:"50%",transform:"translateX(-50%)",position:"fixed"},scrollUp:{color:e.palette.text.secondary,backgroundColor:e.palette.background.default,"&:hover":{backgroundColor:Object(nt.c)(e.palette.background.default,.15)},transition:"".concat(e.transitions.create("transform",{duration:e.transitions.duration.shorter}),", opacity 0.8s"),opacity:1},icon:{height:"2.5rem",width:"2.5rem"}}}),{name:"ScrollUpButton"}));var Ct=function(e){var t=Ot(e),n=e.scrollToRef,i=e.scrollTop,a=u.a.useState(!1),o=Object(r.a)(a,2),l=o[0],c=o[1];return u.a.useEffect((function(){var e=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}((function(){c(window.scrollY>i)}),150);return e(),window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}}),[i]),Object(g.jsx)("div",{className:t.root,children:Object(g.jsx)(ft.a,{in:l,timeout:200,unmountOnExit:!0,children:Object(g.jsx)(pt.a,{className:t.scrollUp,size:"medium",onClick:function(){var e;return null===(e=n.current)||void 0===e?void 0:e.scrollIntoView({behavior:"smooth"})},children:Object(g.jsx)(gt,{className:t.icon})})})})},jt=n(795),Lt=Object(d.a)((function(e){return{root:{background:e.palette.background.default,padding:e.spacing(1),display:"flex",alignItems:"center",borderRadius:999,paddingRight:e.spacing(2),lineHeight:1},avatar:{marginRight:e.spacing(1),height:"3.5rem",width:"3.5rem"},label:{},info:{lineHeight:7/6},details:{}}}));var yt=function(e){var t,n=Lt(e);return Object(g.jsxs)(S.a,{component:"div",variant:"body2",className:n.root,children:[Object(g.jsx)(jt.a,{classes:{root:n.avatar},alt:e.name,src:e.avatar||void 0,children:(t=e.name,t.split(" ").map((function(e,t,n){return!e||0!==t&&t!==n.length-1?"":e[0].toUpperCase()})).join(""))}),Object(g.jsxs)("div",{className:n.info,children:[Object(g.jsx)("strong",{className:n.label,children:e.name}),!!e.details&&Object(g.jsx)("div",{className:n.info,children:e.details})]})]})},wt=Object(d.a)({root:{}}),xt=function(e){var t=e.getFullYear(),n=(new Date).getFullYear();return new Intl.DateTimeFormat(void 0,{year:t===n?void 0:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric"}).format(e)};var St=function(e){var t=wt(e),n=e.time,r="number"===typeof n?new Date(n):n;return Object(g.jsx)("time",{className:t.root,dateTime:r.toISOString(),children:xt(r)})};var Et=function(e){var t=e.workout,n=e.hideWorkoutName,r=Object(E.a)().t,i=(Ge(t.workout.activityId)||{}).PhraseID,a=void 0===i?"":i;return Object(g.jsxs)(g.Fragment,{children:[n?null:r(a)," ",Object(g.jsx)(St,{time:t.workout.startTime})]})},Tt=u.a.lazy((function(){return Promise.all([n.e(8),n.e(1)]).then(n.bind(null,783))})),It=.6,Nt=Object(d.a)((function(e){return{root:{padding:0},icon:{width:e.spacing(It),height:e.spacing(It),minWidth:e.spacing(It),minHeight:e.spacing(It),margin:e.spacing(1.1),borderRadius:999,padding:0,backgroundColor:e.palette.neutral.main,"&$active":{backgroundColor:e.palette.primary.main}},active:{}}}),{name:"DotsItem"});function Rt(e){var t=e.active,n=Nt(e);return Object(g.jsx)(c.a,{size:"small",className:n.root,children:Object(g.jsx)("span",{className:ne()(n.icon,Object(a.a)({},n.active,t))})})}var _t,kt=function(e){var t=e.isActive;return Object(g.jsx)(Rt,{active:t})},Pt=["title","titleId"];function At(){return(At=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Mt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ht(e,t){var n=e.title,r=e.titleId,i=Mt(e,Pt);return s.createElement("svg",At({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,_t||(_t=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.8208 8.32279L21.9218 8.20716C22.4618 7.64891 23.3301 7.60011 23.9272 8.07076L24.0428 8.17181L40.4076 24L24.0428 39.8282C23.4474 40.4041 22.4978 40.3883 21.9218 39.7928C21.3819 39.2346 21.362 38.3651 21.8523 37.784L21.9572 37.6718L34.541 25.5L9.25 25.5C8.4703 25.5 7.82955 24.9051 7.75687 24.1445L7.75 24C7.75 23.2203 8.34489 22.5796 9.10554 22.5069L9.25 22.5L34.541 22.5L21.9572 10.3282C21.3989 9.78824 21.3501 8.91987 21.8208 8.32279L21.9218 8.20716L21.8208 8.32279Z",fill:"#303030"})))}var Dt,Vt=s.forwardRef(Ht),Zt=(n.p,["title","titleId"]);function Bt(){return(Bt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Wt(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Xt(e,t){var n=e.title,r=e.titleId,i=Wt(e,Zt);return s.createElement("svg",Bt({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,Dt||(Dt=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.59253 24L23.9573 8.17181C24.5527 7.59587 25.5023 7.6117 26.0783 8.20716C26.6182 8.76541 26.6381 9.63493 26.1478 10.216L26.0429 10.3282L13.4585 22.5L38.7501 22.5C39.5298 22.5 40.1706 23.0949 40.2432 23.8555L40.2501 24C40.2501 24.7797 39.6552 25.4205 38.8946 25.4931L38.7501 25.5L13.4585 25.5L26.0429 37.6718C26.6012 38.2118 26.65 39.0801 26.1793 39.6772L26.0783 39.7928C25.5383 40.3511 24.67 40.3999 24.0729 39.9292L23.9573 39.8282L7.59253 24L23.9573 8.17181L7.59253 24Z",fill:"#303030"})))}var Ut=s.forwardRef(Xt),Gt=(n.p,Object(d.a)({root:{},prev:{left:0,justifyContent:"start"},next:{right:0,justifyContent:"end",marginLeft:"-100%"}}));var Ft=function(e){var t,n=Gt(e),r=e.disabled,i=e.next,o=e.prev;return Object(g.jsx)("div",{className:ne()(n.root,(t={},Object(a.a)(t,n.next,i),Object(a.a)(t,n.prev,o),t)),children:Object(g.jsxs)(c.a,{disabled:r,size:"large",children:[i&&!o&&Object(g.jsx)(Vt,{}),o&&!i&&Object(g.jsx)(Ut,{})]})})},zt=n(213),Yt={"en-ca":"ca/en","fr-ca":"ca","es-mx":"int/es","en-us":"us","fr-be":"int/fr","en-be":"int/en","hr-hr":"int/en","cs-cz":"cz","da-dk":"dk","de-de":"de","es-es":"es","et-ee":"int/en","fr-fr":"fr","hu-hu":"int/en","en-ie":"int/en","it-it":"it","fr-lu":"int/fr","fy-nl":"int/en","nb-no":"no","pl-pl":"pl","pt-pt":"int/en","de-ch":"ch/de","sk-sk":"int/en","sl-si":"int/en","en-za":"int/en","fr-ch":"ch","fi-fi":"fi","sv-se":"se","en-gb":"gb","de-at":"at","el-gr":"int/en","ru-ru":"int/ru","en-au":"int/en","en-hk":"int/en","en-in":"int/en","id-id":"int/en","en-my":"int/en","en-nz":"int/en","en-ph":"int/en","en-sg":"int/en","zh-tw":"int/en","th-th":"int/en","ja-jp":"int/jp"},Kt=n(664),qt=Object(d.a)((function(e){return{root:{background:e.palette.action.disabledBackground,display:"inline-block"}}}),{name:"TextPlaceholder"});var Jt=function(e){var t=qt(e);return Object(g.jsx)("span",{className:t.root})},$t=Object(d.a)({span:{width:"4rem",height:"1.6rem"},span2:{width:"2.5rem"}});var Qt=function(){var e=$t();return Object(g.jsxs)(g.Fragment,{children:[Object(g.jsx)(Jt,{classes:{root:e.span}})," ",Object(g.jsx)(Jt,{classes:{root:[e.span2,e.span].join(" ")}})]})},en=Object(d.a)((function(e){return{root:{margin:e.spacing(1)},img:{maxWidth:"85%",marginBottom:e.spacing(2)},link:{marginTop:e.spacing(1.5),borderRadius:200},title:{textTransform:"uppercase",fontWeight:"bold"},imgWrapper:{textAlign:"center",lineHeight:0},subtitle:{fontWeight:"bold"},price:{marginTop:e.spacing(4),marginBottom:e.spacing(1),fontWeight:"bold"},description:{marginTop:e.spacing(1),marginBottom:e.spacing(1)}}}),{name:"PopularProduct"}),tn=function(e,t){var n=new URL(e,"https://www.suunto.com");return n.searchParams.set("height","250"),n.searchParams.set("format","jpg"),n.searchParams.set("bgcolor",t.slice(1)),n.toString()};var nn,rn=(nn=function(e){var t=en(e),n=e.value,i=e.showPrice,a=Object(E.a)(T.a.TRANSLATIONS).t,l=Object(o.a)(),c=u.a.useState(!1),s=Object(r.a)(c,2),d=s[0],p=s[1],v=u.a.useState(null),m=Object(r.a)(v,2),b=m[0],h=m[1],O=Object(zt.a)(!0),C=Object(r.a)(O,2),j=C[0],L=C[1],y=u.a.useRef(null),w=Object(f.c)().logEvent;return u.a.useEffect((function(){var e;(null===(e=n.Ssids)||void 0===e?void 0:e.length)&&d&&i&&Object(re.b)(n.Ssids[0]).then((function(e){var t=e.products,n=Object(r.a)(t,1)[0].formatted_price,i=n.promo,a=n.price;h(i||a)})).catch(ie.a).finally(L())}),[n.Ssids,d,i]),u.a.useEffect((function(){if(y.current&&!d){var e=new IntersectionObserver((function(e){var t=Object(r.a)(e,1)[0];p(t.isIntersecting)}));return e.observe(y.current),function(){e&&y.current&&e.unobserve(y.current)}}}),[d]),Object(g.jsxs)("article",{className:t.root,ref:y,children:[Object(g.jsx)("div",{className:t.imgWrapper,children:Object(g.jsx)("img",{className:t.img,src:tn(n.MainImagePath,l.palette.background.default),alt:n.Name+" "+n.ProductVariantName})}),Object(g.jsx)(S.a,{variant:"h5",className:t.title,children:n.Name}),Object(g.jsx)(S.a,{className:t.subtitle,variant:"h6",children:n.ProductVariantName}),Object(g.jsx)(S.a,{component:"p",variant:"h6",className:t.description,children:n.Description}),(b||j)&&i&&Object(g.jsxs)(S.a,{className:t.price,variant:"h2",children:[b,j&&Object(g.jsx)(Qt,{})]}),Object(g.jsx)(Kt.a,{href:n.Url,target:"_blank",className:t.link,variant:"contained",color:"primary",onClick:function(){return w(f.b.SharedWorkoutPopularProductClicked)},children:a("VIEW_PRODUCT")})]})},function(e){return Object(g.jsx)(f.a.Provider,{value:Object(i.a)(Object(i.a)({},u.a.useContext(f.a)),{},{WatchModel:e.value.Name,WatchColour:e.value.ProductVariantName}),children:Object(g.jsx)(nn,Object(i.a)({},e))})}),an=Object(d.a)((function(e){var t;return{root:(t={marginTop:e.spacing(2),"& .alice-carousel__dots":{height:e.spacing(4.5),display:"flex",alignItems:"center",justifyContent:"center",marginRight:e.spacing(4.5),marginLeft:e.spacing(4.5),marginBottom:0}},Object(a.a)(t,["& .alice-carousel__prev-btn","& .alice-carousel__next-btn"].join(","),{width:0,top:0,position:"absolute",padding:0,display:"flex",alignItems:"center",height:"100%"}),Object(a.a)(t,"& .alice-carousel__prev-btn",{left:0}),Object(a.a)(t,"& .alice-carousel__next-btn",{left:"100%",width:"auto"}),Object(a.a)(t,"& svg",{width:"2.8rem",height:"2.8rem"}),Object(a.a)(t,"overflowX","hidden"),t),product:{margin:"auto",maxWidth:350,width:"calc(100% - 10rem)"}}}),{name:"PopularProducts"});var on=function(e){var t=an(e),n=u.a.useState(null),a=Object(r.a)(n,2),o=a[0],l=a[1],c=Object(zt.a)(!0),s=Object(r.a)(c,2),d=s[0],p=s[1],v=u.a.useContext(f.a),m=u.a.useState(!0),b=Object(r.a)(m,2),h=b[0],O=b[1];u.a.useEffect((function(){Object(re.e)().then((function(e){var t=e.AutoDetectedCulture;return!Yt[t.toLowerCase()]&&O(!1)})).catch(ie.a)}),[]),u.a.useEffect((function(){Object(re.a)().then(l).catch(ie.a).finally(p())}),[]);var C=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Object(g.jsx)(f.a.Provider,{value:Object(i.a)(Object(i.a)({},v),{},{PlaceInProductCarousel:n+1}),children:Object(g.jsx)(rn,{showPrice:h,value:e,classes:{root:t.product}})},e.Url)};return d?null:(null===o||void 0===o?void 0:o.length)?Object(g.jsx)("section",{className:t.root,children:Object(g.jsx)(u.a.Suspense,{fallback:C(o[0]),children:Object(g.jsx)(Tt,{keyboardNavigation:!0,infinite:!0,renderDotsItem:kt,renderNextButton:function(e){var t=e.isDisabled;return Object(g.jsx)(Ft,{disabled:t,next:!0})},renderPrevButton:function(e){var t=e.isDisabled;return Object(g.jsx)(Ft,{disabled:t,prev:!0})},items:o.slice(0,5).map(C)})})}):null},ln=n(662),cn=function(e,t){return e.width>t.width?1:-1},sn=function(e,t){var n=Object(ze.a)(e.sizes||[]).sort(cn).find(function(e){var t;if("string"===typeof e)t=function(t){return t.size===e};else{var n=Object(r.a)(e,2),i=n[0],a=n[1];t=function(e){return e.width>=i&&e.height>=a}}return t}(t));return(null===n||void 0===n?void 0:n.url)||e.url},un=Object(d.a)({root:{objectFit:"cover",objectPosition:"center",maxHeight:"100%",maxWidth:"100%",position:"absolute",top:0}});var dn,fn=function(e){var t=un(e),n=e.photo,r=e.size;return Object(g.jsx)("img",{className:t.root,src:sn(n,r),alt:n.description})},pn=n(789),vn=n(793),mn=n(198),bn=n(666),hn=["title","titleId"];function gn(){return(gn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function On(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Cn(e,t){var n=e.title,r=e.titleId,i=On(e,hn);return s.createElement("svg",gn({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,dn||(dn=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 24C4 12.9543 12.9543 4 24 4C35.0457 4 44 12.9543 44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24ZM24 6.5C14.335 6.5 6.5 14.335 6.5 24C6.5 33.665 14.335 41.5 24 41.5C33.665 41.5 41.5 33.665 41.5 24C41.5 14.335 33.665 6.5 24 6.5ZM15.1161 15.1161C15.6043 14.628 16.3957 14.628 16.8839 15.1161L24 22.2322L31.1161 15.1161C31.6043 14.628 32.3957 14.628 32.8839 15.1161C33.372 15.6043 33.372 16.3957 32.8839 16.8839L25.7678 24L32.8839 31.1161C33.372 31.6043 33.372 32.3957 32.8839 32.8839C32.3957 33.372 31.6043 33.372 31.1161 32.8839L24 25.7678L16.8839 32.8839C16.3957 33.372 15.6043 33.372 15.1161 32.8839C14.628 32.3957 14.628 31.6043 15.1161 31.1161L22.2322 24L15.1161 16.8839C14.628 16.3957 14.628 15.6043 15.1161 15.1161Z",fill:"#303030"})))}var jn=s.forwardRef(Cn),Ln=(n.p,n(192)),yn=n(272),wn=Object(d.a)((function(e){return{root:Object(a.a)({maxWidth:"100vw",maxHeight:"100vh",margin:"auto"},e.breakpoints.up("md"),{maxHeight:"80vh",maxWidth:"80vw"}),loadingIndicator:{top:0,left:0,bottom:0,right:0,position:"absolute",margin:"auto"}}}));var xn=function(e){var t=wn(e),n=e.photo,i=u.a.useState(!1),a=Object(r.a)(i,2),o=a[0],l=a[1];return Object(g.jsxs)(g.Fragment,{children:[!o&&Object(g.jsx)(h.a,{className:t.loadingIndicator,color:"primary"}),Object(g.jsx)("img",{src:sn(n,je.a.medium),className:t.root,alt:n.description,role:"presentation",onLoad:function(){return l(!0)}})]})},Sn=["photos","selectedPhoto","onChange"],En=Object(d.a)({root:{width:"100%","& .alice-carousel__stage-item":{display:"inline-flex"}}},{name:"Carousel"});var Tn,In=function(e){var t=En(e),n=e.photos,r=e.selectedPhoto,a=e.onChange,o=Object(yn.a)(e,Sn);return Object(g.jsx)("div",{className:t.root,children:Object(g.jsx)(Tt,Object(i.a)({onSlideChanged:function(e){var t=e.item;return a(n[t]||null)},keyboardNavigation:!0,activeIndex:r?n.indexOf(r):0,mouseTracking:!0,renderNextButton:function(e){var t=e.isDisabled;return Object(g.jsx)(Ft,{disabled:t,next:!0})},renderPrevButton:function(e){var t=e.isDisabled;return Object(g.jsx)(Ft,{disabled:t,prev:!0})},items:n.map((function(e){return Object(g.jsx)(xn,{photo:e})}))},o))})},Nn=Object(d.a)({root:(Tn={},Object(a.a)(Tn,["& .alice-carousel","& .alice-carousel > div","& .alice-carousel .alice-carousel__wrapper"].join(","),{height:"100%"}),Object(a.a)(Tn,["& .alice-carousel__prev-btn","& .alice-carousel__next-btn"].join(","),{width:0,top:0,position:"absolute",padding:0,display:"flex",alignItems:"center"}),Object(a.a)(Tn,"& .alice-carousel__prev-btn",{left:0}),Object(a.a)(Tn,"& .alice-carousel__next-btn",{left:"100%",width:"auto"}),Tn),closeButton:{position:"absolute",top:"0",right:"0"},carouselWrapper:{display:"flex",height:"100%",width:"100%","&:focus-visible":{outline:"none"}},buttonRoot:{position:"absolute"},closeIcon:{width:"4rem",height:"4rem"},loadingIndicator:{top:0,left:0,width:"100vw",height:"100vh",position:"absolute"}},{name:"Theatre"});var Rn=function(e){var t=Nn(e),n=Object(E.a)([T.a.CONTROLS]).t,r=e.onClose,i=e.open,a=e.photos,o=e.selectedPhoto,l=e.onChange,d=u.a.useMemo((function(){return Object(mn.a)(Ln.b)}),[]);return Object(g.jsx)(bn.a,{theme:d,children:Object(g.jsx)(pn.a,{className:t.root,onClose:r,open:i,BackdropComponent:vn.a,children:Object(g.jsxs)("div",{className:t.carouselWrapper,children:[Object(g.jsx)(s.Suspense,{fallback:Object(g.jsx)(L,{active:!0,classes:{root:t.loadingIndicator}}),children:Object(g.jsx)(In,{onChange:l,photos:a,selectedPhoto:o})}),Object(g.jsx)(c.a,{onClick:r,"aria-label":n("CLOSE"),className:t.closeButton,size:"large",children:Object(g.jsx)(jn,{className:t.closeIcon})})]})})})},_n=Object(d.a)((function(e){var t;return{root:(t={"& .alice-carousel__dots":{minHeight:e.spacing(4.5),marginRight:e.spacing(4.5),marginLeft:e.spacing(4.5),marginTop:0,marginBottom:0,paddingTop:e.spacing(1)},display:"flex",flexWrap:"wrap"},Object(a.a)(t,["& .alice-carousel__prev-btn","& .alice-carousel__next-btn"].join(","),{padding:0,bottom:0,position:"absolute",width:"auto"}),Object(a.a)(t,"& .alice-carousel__prev-btn",{left:0}),Object(a.a)(t,"& .alice-carousel__next-btn",{right:0}),t),icon:{width:e.spacing(2),height:e.spacing(2)},itemCommon:{margin:"auto auto",maxWidth:500,width:"100%",flexShrink:0,flexGrow:1},item:{},imgButton:{width:"100%",backgroundColor:e.palette.action.disabledBackground,paddingTop:"".concat(.656*100,"%")}}}),{name:"Photos"}),kn="imageId",Pn=[500,328];var An=function(e){var t=_n(e),n=e.photos,i=u.a.useState(null),a=Object(r.a)(i,2),o=a[0],l=a[1],s=Object(f.c)().logEvent,d=n.find((function(e){var t=e.key;return o===t}));u.a.useEffect((function(){var e=new URLSearchParams(window.location.search).get(kn);e&&n.find((function(t){var n=t.key;return e===n}))&&l(e)}),[]),u.a.useEffect((function(){d&&s(f.b.SharedWorkoutAttachedMediaViewed,{MediaType:"Photo"})}),[d]),u.a.useEffect((function(){!function(e){var t=new URLSearchParams(window.location.search);if(e!==t.get(kn)){e?t.set(kn,e):t.delete(kn);var n=t.toString();window.history.replaceState(null,document.title,(n?"?"+n:window.location.pathname)+window.location.hash)}}(o)}),[o,n]);var p=function(e){return Object(g.jsx)("div",{className:ne()(t.itemCommon,t.item),children:Object(g.jsx)(ln.a,{className:t.imgButton,focusRipple:!0,onClick:function(){return l(e.key)},children:Object(g.jsx)(fn,{photo:e,size:Pn})})},e.key)};return Object(g.jsxs)(g.Fragment,{children:[Object(g.jsx)(Rn,{open:!!o,onClose:function(){return l(null)},photos:n,selectedPhoto:d,onChange:function(e){return l(null===e||void 0===e?void 0:e.key)}}),Object(g.jsxs)("div",{className:t.root,children:[1===n.length&&p(n[0]),n.length>1&&Object(g.jsx)(u.a.Suspense,{fallback:p(n[0]),children:Object(g.jsx)(Tt,{keyboardNavigation:!0,infinite:!0,renderDotsItem:kt,renderNextButton:function(e){var n=e.isDisabled;return Object(g.jsx)(c.a,{disabled:n,size:"large",children:Object(g.jsx)(Vt,{className:t.icon})})},renderPrevButton:function(e){var n=e.isDisabled;return Object(g.jsx)(c.a,{disabled:n,size:"large",children:Object(g.jsx)(Ut,{className:t.icon})})},items:n.map(p)})})]})]})},Mn=n(792),Hn=n(807),Dn=n(808),Vn=n(207),Zn=n(677),Bn=n(686),Wn=u.a.lazy((function(){return Promise.all([n.e(0),n.e(10)]).then(n.bind(null,695))})),Xn=Object(d.a)((function(e){return{commonPlaceholder:{marginTop:e.spacing(1.1)},icon:{width:"1.5rem",height:"1.5rem",borderRadius:999},value:{height:"1.5rem",marginBottom:"1.1rem",width:"9rem",maxWidth:"100%","&$small":{marginBottom:0,marginTop:0,height:"1.1rem"}},small:{}}}),{name:"SummaryItemWrapper"}),Un=function(e){var t=Xn();return Object(g.jsx)(Bn.a,Object(i.a)(Object(i.a)({},e),{},{Icon:function(){return Object(g.jsx)(Jt,{classes:{root:[t.commonPlaceholder,t.icon].join(" ")}})},value:Object(g.jsx)(Jt,{classes:{root:ne()(t.commonPlaceholder,t.value,Object(a.a)({},t.small,e.small))}})}))};var Gn=function(e){return Object(g.jsx)(u.a.Suspense,{fallback:Object(g.jsx)(Un,Object(i.a)({},e)),children:Object(g.jsx)(Wn,Object(i.a)({},e))})},Fn=function(e){var t=e.speed;return null===t||void 0===t?void 0:t.avg},zn=function(e){var t=e.speed;return null===t||void 0===t?void 0:t.max},Yn=function(){},Kn={Duration:function(e){return e.duration},Distance:function(e){return e.distance},AvgPace:Fn,AvgHeartrate:function(e){var t=e.hr;return null===t||void 0===t?void 0:t.avg},MaxHeartRate:function(e){var t=e.hr;return null===t||void 0===t?void 0:t.max},Energy:function(e){return e.energy},RecoveryTime:function(e){return e.recoveryTime},Pte:Yn,PerformanceLevel:Yn,AvgSpeed:Fn,AvgCadence:function(e){var t=e.cadence;return null===t||void 0===t?void 0:t.avg},Steps:Yn,AscentAltitude:function(e){return e.ascent},DescentAltitude:function(e){return e.descent},HighAltitude:function(e){var t=e.altitude;return null===t||void 0===t?void 0:t.max},LowAltitude:function(e){var t=e.altitude;return null===t||void 0===t?void 0:t.min},AvgTemperature:function(e){var t=e.temperature;return null===t||void 0===t?void 0:t.avg},PeakEpoc:Yn,Feeling:Yn,MoveType:Yn,"Catch:Fish":Yn,"Catch:BigGame":Yn,"Catch:SmallGame":Yn,"Catch:Bird":Yn,"Catch:ShotCount":Yn,AvgPower:function(e){var t=e.power;return null===t||void 0===t?void 0:t.avg},AvgSWOLF:function(e){var t=e.swolf;return null===t||void 0===t?void 0:t.avg},AvgNauticalSpeed:Fn,MaxNauticalSpeed:zn,NauticalDistance:function(e){return e.distance},MaxSpeed:zn,MaxDepth:Yn,DiveTime:Yn,DiveMode:Yn,DiveNumberInSeries:Yn,DiveSurfaceTime:Yn,DiveVisibility:Yn,DiveMaxDepthTemperature:Yn,SkiRuns:Yn,SkiDistance:Yn,SkiTime:Yn,AvgSkiSpeed:Yn,MaxSkiSpeed:Yn,AscentTime:function(e){return e.ascentTime},DescentTime:function(e){return e.descentTime},EstVO2peak:Yn},qn=function(e,t){var n;if(t&&Kn[e]&&"object"===typeof(n=Kn[e](t))&&null!==n)throw new Error(e+JSON.stringify(n));return n},Jn=Object(d.a)((function(e){return{root:{marginBottom:e.spacing(.5)},icon:{width:"2.5rem",height:"2.5rem"},grid:{display:"flex",flexWrap:"wrap",overflow:"hidden"},detailsRoot:{paddingTop:0},gridItemCommon:{flexGrow:1,width:115,borderLeftWidth:1,borderLeftStyle:"solid",borderBottomWidth:1,borderBottomStyle:"solid",marginLeft:-1,marginBottom:-1},gridItem:{padding:e.spacing(1),paddingTop:20,paddingBottom:20,borderColor:e.palette.divider},placeHolder:{borderColor:"transparent"}}}),{name:"MultisportSummarySheet"});var $n=function(e){var t=Jn(e),n=e.multisportMarker,i=u.a.useContext(Vn.a);if(!n.Totals)return null;var a=Object(E.a)().t,o=Ge(n.ActivityID)||Ge(Ue);if(!o)throw new Error("Unspecified activity not found");var l=qe(o.Key)||qe("Fallback");if(!l)throw new Error("Fallback activity not found");var c=l.map((function(e){return[e,qn(e,n.Totals)]}));return i||(c=c.filter((function(e){var t=Object(r.a)(e,2),n=t[0],i=t[1];return Object(Zn.a)(n,i)}))),Object(g.jsx)("div",{className:t.root,children:Object(g.jsxs)(Mn.a,{elevation:0,children:[Object(g.jsx)(Hn.a,{expandIcon:Object(g.jsx)(B,{className:t.icon}),children:Object(g.jsx)(S.a,{variant:"h2",children:a(null===o||void 0===o?void 0:o.PhraseID)})}),Object(g.jsx)(Dn.a,{classes:{root:t.detailsRoot},children:Object(g.jsxs)("div",{className:t.grid,children:[c.map((function(e){var n=Object(r.a)(e,2),i=n[0],a=n[1];return Object(g.jsx)(Gn,{value:a,name:i,small:!0,classes:{root:ne()(t.gridItemCommon,t.gridItem)}},i)})),Array.from({length:10}).map((function(e,n){return Object(g.jsx)("div",{className:ne()(t.gridItemCommon,t.placeHolder)},n)}))]})})]})})},Qn=Object(d.a)({root:{}},{name:"MultisportSummary"});function er(e){var t=Qn(e),n=e.workout.getMultisportMarkerExtension();return Object(g.jsx)("div",{className:t.root,children:null===n||void 0===n?void 0:n.Markers.map((function(e){return Object(g.jsx)($n,{multisportMarker:e},e.StartTime.toString())}))})}var tr=u.a.memo(er);var nr=Object(d.a)({root:function(e){var t=e.duration;return{position:"relative",transition:"max-height ".concat(void 0===t?800:t,"ms ease-out"),overflow:"hidden"}},shadowChildren:{visibility:"hidden",position:"absolute"}},{name:"Slide"});var rr=function(e){var t=e.children,n=e.className,i=e.duration,a=void 0===i?800:i,o=e.onRowLengthChanged,l=nr(e),c=function(e){var t=u.a.useRef();return u.a.useEffect((function(){t.current=e}),[e]),t.current}(t),s=u.a.useState(!1),d=Object(r.a)(s,2),f=d[0],p=d[1],v=u.a.Children.count(c),m=u.a.Children.count(t)-u.a.Children.count(c),b=u.a.useRef(null),h=u.a.useRef(null),O=u.a.useState(null),C=Object(r.a)(O,2),j=C[0],L=C[1];u.a.useEffect((function(){if(b.current&&ResizeObserver){var e=new ResizeObserver((function(){var e=function(e){if(!e)return 0;for(var t=e.firstChild.offsetTop,n=Array.from(e.children),r=0;(null===(i=n[r])||void 0===i?void 0:i.offsetTop)===t;){var i;r++}return r}(b.current);o(e)}));return e.observe(b.current),function(){e.disconnect()}}}),[b.current,o]),u.a.useEffect((function(){v&&m&&(p(!0),setTimeout((function(){return p(!1)}),a),m<0&&(L(c),setTimeout((function(){return L(null)}),a)))}),[v,m]);var y,w,x=void 0;c&&m&&(x=null===(y=h.current)||void 0===y?void 0:y.offsetHeight),f&&(x=null===(w=b.current)||void 0===w?void 0:w.offsetHeight);var S=j||(c&&m<0?c:t);return Object(g.jsxs)("div",{className:l.root,style:{maxHeight:x},children:[Object(g.jsx)("div",{className:ne()(n,l.shadowChildren),ref:b,"aria-hidden":"true",children:t}),Object(g.jsx)("div",{className:n,ref:h,children:S})]})},ir=function(e){var t,n;e.workout.activityId===Xe&&(t=null===(n=e.getSummary())||void 0===n?void 0:n.avgSpeed);return t||(t=e.workout.avgSpeed),t},ar=function(e){return e.workout.maxSpeed},or=function(){},lr={Duration:function(e){return e.workout.totalTime},Distance:function(e){return e.workout.totalDistance},AvgPace:ir,AvgHeartrate:function(e){return e.workout.hrdata.workoutAvgHR},MaxHeartRate:function(e){return e.workout.hrdata.workoutMaxHR},Energy:function(e){return e.workout.energyConsumption},RecoveryTime:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.recoveryTime},Pte:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.pte},PerformanceLevel:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.performanceLevel},AvgSpeed:ir,AvgCadence:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.avgCadence},Steps:function(e){return e.workout.stepCount},AscentAltitude:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.ascent},DescentAltitude:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.descent},HighAltitude:function(e){return e.workout.maxAltitude},LowAltitude:function(e){return e.workout.minAltitude},AvgTemperature:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.avgTemperature},PeakEpoc:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.peakEpoc},Feeling:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.feeling},MoveType:or,"Catch:Fish":or,"Catch:BigGame":or,"Catch:SmallGame":or,"Catch:Bird":or,"Catch:ShotCount":or,AvgPower:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.avgPower},AvgSWOLF:function(e){var t;return null===(t=e.getSwimmingHeaderExtension())||void 0===t?void 0:t.avgSwolf},AvgNauticalSpeed:ir,MaxNauticalSpeed:ar,NauticalDistance:function(e){return e.workout.totalDistance},MaxSpeed:ar,MaxDepth:function(e){var t;return null===(t=e.getDiveHeaderExtension())||void 0===t?void 0:t.maxDepth},DiveTime:function(e){var t=e.workout.totalTime;if(t){var n,r=null===(n=e.getDiveHeaderExtension())||void 0===n?void 0:n.pauseDuration;r&&(t-=r)}return t},DiveMode:function(e){var t;return null===(t=e.getDiveHeaderExtension())||void 0===t?void 0:t.diveMode},DiveNumberInSeries:function(e){var t;return null===(t=e.getDiveHeaderExtension())||void 0===t?void 0:t.diveNumberInSeries},DiveSurfaceTime:function(e){var t;return null===(t=e.getDiveHeaderExtension())||void 0===t?void 0:t.surfaceTime},DiveVisibility:or,DiveMaxDepthTemperature:function(e){var t;return null===(t=e.getDiveHeaderExtension())||void 0===t?void 0:t.maxDepthTemperature},SkiRuns:function(e){var t;return null===(t=e.getSkiExtension())||void 0===t?void 0:t.statistics.numberOfRuns},SkiDistance:function(e){var t;return null===(t=e.getSkiExtension())||void 0===t?void 0:t.statistics.descentDistanceMeters},SkiTime:function(e){var t;return null===(t=e.getSkiExtension())||void 0===t?void 0:t.statistics.descentDurationSeconds},AvgSkiSpeed:function(e){var t;return null===(t=e.getSkiExtension())||void 0===t?void 0:t.statistics.avgSpeed},MaxSkiSpeed:function(e){var t;return null===(t=e.getSkiExtension())||void 0===t?void 0:t.statistics.maxSpeed},AscentTime:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.ascentTime},DescentTime:function(e){var t;return null===(t=e.getSummary())||void 0===t?void 0:t.descentTime},EstVO2peak:function(e){var t;return null===(t=e.getFitness())||void 0===t?void 0:t.estimatedVo2Max}},cr=function(e,t){var n,r=t.getSummary();r&&(n=r[e.charAt(0).toLowerCase()+e.slice(1)]);return"undefined"===typeof n&&lr[e]&&(n=lr[e](t)),n},sr=Object(d.a)((function(e){return{root:{justifyContent:"space-between",display:"flex"},button:{padding:e.spacing(.5,1)},chevron:{height:"3rem",width:"3rem",transition:"transform 0.3s ease-out"},rotate:{transform:"rotate(180deg)"}}}),{name:"Pagination"});var ur=function(e){var t=sr(e),n=Object(E.a)(T.a.TRANSLATIONS).t,r=e.setAllVisible,i=e.isAllVisible,o=e.count;return Object(g.jsxs)("div",{className:t.root,children:[Object(g.jsx)(Kt.a,{className:t.button,color:"body",variant:"text",onClick:function(){return r(!i)},children:n(i?"SHOW_LESS":"SHOW_MORE")}),Object(g.jsx)(Kt.a,{className:t.button,variant:"text",color:"body",onClick:function(){return r(!i)},endIcon:Object(g.jsx)(B,{className:ne()(t.chevron,Object(a.a)({},t.rotate,i))}),children:n("N_ITEMS",{count:o})})]})},dr=Object(d.a)((function(e){return{root:{},grid:{paddingTop:10,display:"flex",flexWrap:"wrap",overflow:"hidden"},gridItemCommon:{flexGrow:1,width:115,borderLeftWidth:1,borderLeftStyle:"solid",borderBottomWidth:1,borderBottomStyle:"solid",marginLeft:-1,marginBottom:-1},gridItem:{padding:e.spacing(1),paddingTop:20,paddingBottom:20,borderColor:e.palette.divider},placeHolder:{borderColor:"transparent"},bigItem:{marginTop:24,marginBottom:24}}}));var fr=function(e){var t=dr(e),n=e.activityConfig,i=e.workout,a=u.a.useContext(Vn.a),o=u.a.useState(1/0),l=Object(r.a)(o,2),c=l[0],s=l[1],d=u.a.useState(!1),f=Object(r.a)(d,2),p=f[0],v=f[1],m=qe(n.Key)||qe("Fallback");if(!m)throw new Error("No summary items");var b=m.map((function(e){return[e,cr(e,i)]}));a||(b=b.filter((function(e){var t=Object(r.a)(e,2),n=t[0],i=t[1];return Object(Zn.a)(n,i)})));var h=b.slice(0,3),O=b.slice(3),C=O.slice(0,c),j=O.slice(c),L=u.a.useCallback((function(e){return s(2*e||1/0)}),[]);return Object(g.jsxs)("div",{className:t.root,children:[Object(g.jsx)("div",{children:h.map((function(e){var n=Object(r.a)(e,2),i=n[0],a=n[1];return Object(g.jsx)(Gn,{value:a,name:i,classes:{root:t.bigItem}},i)}))}),Object(g.jsxs)(rr,{className:t.grid,onRowLengthChanged:L,children:[C.map((function(e){var n=Object(r.a)(e,2),i=n[0],a=n[1];return Object(g.jsx)(Gn,{value:a,name:i,small:!0,classes:{root:ne()(t.gridItemCommon,t.gridItem)}},i)})),p&&j.map((function(e){var n=Object(r.a)(e,2),i=n[0],a=n[1];return Object(g.jsx)(Gn,{value:a,name:i,small:!0,classes:{root:ne()(t.gridItemCommon,t.gridItem)}},i)})),Array.from({length:10}).map((function(e,n){return Object(g.jsx)("div",{className:ne()(t.gridItemCommon,t.placeHolder)},n)}))]}),!!j.length&&Object(g.jsx)(ur,{isAllVisible:p,setAllVisible:v,count:j.length})]})},pr=Object(d.a)((function(e){return{root:{position:"relative",maxWidth:"100%"},description:{},section:{position:"relative",maxWidth:"100%",width:900,marginBottom:e.spacing(4.5),"&$wideSection":{paddingLeft:0,paddingRight:0}},wideSection:{},photosSection:{},popularProductsSection:{},popularProductsTitle:{marginBottom:e.spacing(4)},userChip:{paddingLeft:0},activityName:Object(a.a)({textTransform:"uppercase",textAlign:"center",fontWeight:700,position:"sticky",background:e.palette.background.default,top:0,paddingTop:e.spacing(2),paddingBottom:e.spacing(2),zIndex:3},e.breakpoints.up(Le.a),{textAlign:"left"})}}),{name:"WorkoutSummary"});var vr,mr=u.a.memo(function(e){return function(t){return Object(g.jsx)(f.a.Provider,{value:Object(i.a)(Object(i.a)({},u.a.useContext(f.a)),{},{Source:"LeftPane"}),children:Object(g.jsx)(e,Object(i.a)({},t))})}}((function(e){var t,n=Object(E.a)([T.a.PHRASES,T.a.TRANSLATIONS]).t,r=e.workout,i=e.showUserChip,a=pr(e),c=Object(o.a)(),s=Object(l.a)(c.breakpoints.up(Le.a)),u=r.workout.description,d=Ge(r.workout.activityId)||Ge(Ue);if(!d)throw new Error("Unspecified activity not found");var f=r.workout.availableExtensions.includes(je.b.MultisportMarkerExtension);return Object(g.jsxs)("div",{className:a.root,ref:e.scrollRef,children:[Object(g.jsxs)("header",{className:a.section,children:[Object(g.jsx)(S.a,{component:"h2",variant:s?"h1":"subtitle2",className:a.activityName,children:n(d.PhraseID)}),u&&Object(g.jsx)(S.a,{component:"div",variant:"h6",className:a.description,children:Object(g.jsx)("strong",{children:u})})]}),!!(null===(t=r.workout.photos)||void 0===t?void 0:t.length)&&Object(g.jsx)(An,{photos:r.workout.photos,classes:{root:ne()(a.section,a.wideSection)}}),i&&Object(g.jsx)("div",{className:a.section,children:Object(g.jsx)(yt,{classes:{root:a.userChip},details:Object(g.jsx)(Et,{hideWorkoutName:!0,workout:r}),name:r.workout.fullname,avatar:r.workout.userPhoto})}),Object(g.jsx)("div",{className:a.section,children:Object(g.jsx)(fr,{workout:r,activityConfig:d})}),Object(g.jsx)("div",{className:ne()(a.section,a.wideSection),children:f&&Object(g.jsx)(tr,{workout:r})}),Object(g.jsx)(S.a,{variant:"h4",className:ne()(a.section,a.popularProductsTitle),children:n("".concat(T.a.TRANSLATIONS,":MOST_POPULAR_PRODUCTS_TITLE"))}),Object(g.jsx)(on,{classes:{root:ne()(a.section,a.wideSection)}})]})}))),br=["title","titleId"];function hr(){return(hr=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function gr(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Or(e,t){var n=e.title,r=e.titleId,i=gr(e,br);return s.createElement("svg",hr({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":r},i),n?s.createElement("title",{id:r},n):null,vr||(vr=s.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M27.7948 4L28.6811 8.72697L31.5057 9.73961L35.5396 6.97959L40.9062 12.3462L38.1633 16.355L39.2422 19.0374L44 19.9295V27.519L39.273 28.4053L38.2604 31.23L41.0204 35.2639L35.6538 40.6305L31.6672 37.9028L28.6918 39.2159L27.7948 44H20.2052L19.3045 39.196L16.5911 37.9147L12.6219 40.6305L7.25532 35.2639L9.98901 31.2685L8.77377 28.4141L4 27.519V19.9295L8.80401 19.0288L10.0853 16.3154L7.36954 12.3462L12.7361 6.97959L16.7552 9.72944L19.3155 8.74539L20.2052 4H27.7948ZM25.7199 6.5H22.2801L21.5154 10.5781L16.4268 12.5339L13.0368 10.2145L10.6044 12.6468L12.958 16.0867L10.5195 21.2507L6.5 22.0043V25.4442L10.5498 26.2035L12.8258 31.5496L10.4902 34.9632L12.9226 37.3956L16.3624 35.042L21.5264 37.4805L22.2801 41.5H25.7199L26.4773 37.4606L31.9307 35.0539L35.3532 37.3956L37.7855 34.9632L35.4762 31.5881L37.4034 26.2123L41.5 25.4442V22.0043L37.4343 21.242L35.3448 16.0471L37.6713 12.6468L35.2389 10.2145L31.8638 12.5238L26.4881 10.5966L25.7199 6.5ZM24 15C28.9706 15 33 19.0294 33 24C33 28.9706 28.9706 33 24 33C19.0294 33 15 28.9706 15 24C15 19.0294 19.0294 15 24 15ZM24 17.5C20.4101 17.5 17.5 20.4101 17.5 24C17.5 27.5899 20.4101 30.5 24 30.5C27.5899 30.5 30.5 27.5899 30.5 24C30.5 20.4101 27.5899 17.5 24 17.5Z",fill:"#303030"})))}var Cr=s.forwardRef(Or),jr=(n.p,u.a.lazy((function(){return Promise.all([n.e(0),n.e(9),n.e(2)]).then(n.bind(null,788))}))),Lr=Object(d.a)((function(e){return Object(a.a)({root:{position:"relative",height:"fill-available",fallbacks:[{height:"100vh"}]},footerWrapper:{bottom:0,display:"flex",flexWrap:"wrap",position:"absolute",alignItems:"end",width:"100%"},footer:{padding:e.spacing(1),marginTop:e.spacing(1),backgroundColor:e.palette.background.default,borderTopRightRadius:e.spacing(1.5),borderTopLeftRadius:e.spacing(1.5),position:"relative",width:"100%"},footerSide:{flexBasis:100,flexShrink:0,flexGrow:0,position:"relative"},footerRight:{flexBasis:100,flexShrink:0,flexGrow:100,position:"relative"},siteFooter:{padding:e.spacing(1.2)},pathDial:{position:"absolute",left:e.spacing(2),bottom:0},styleDial:{position:"absolute",right:e.spacing(2),bottom:0},summary:{paddingTop:0,position:"relative",flexBasis:800,flexGrow:0},mapWrapper:{backgroundColor:e.palette.action.disabledBackground,position:"relative",height:"fill-available",fallbacks:[{height:"100vh"}]},main:{height:"fill-available",display:"block",fallbacks:[{height:"100vh"}]},footerCenter:{width:"100%"},summaryLogoWrapper:{display:"none",textAlign:"center",paddingTop:25},summaryWrapper:{display:"flex",flexDirection:"column",backgroundColor:e.palette.background.default,alignItems:"center",position:"relative"},settingsIcon:{height:"2.5rem",width:"2.5rem"},userChip:{display:"flex",position:"absolute",alignItems:"center",margin:e.spacing(2),top:0},fixedContainer:{position:"fixed",top:0,left:0,width:"100%"},summarySection:{paddingLeft:e.spacing(1.2),paddingRight:e.spacing(1.2)},buttonWrapper:{flexGrow:0,borderRadius:1e3,backgroundColor:e.palette.background.default,position:"relative",display:"flex",alignItems:"center"},settingsButton:{},useChipRoot:{marginRight:e.spacing(1)}},e.breakpoints.up(Le.a),{footerCenter:{marginRight:"auto",marginLeft:"auto",width:680,maxWidth:"100%",position:"relative",paddingLeft:e.spacing(2),paddingRight:e.spacing(2)},footerRight:{flexGrow:0,order:3},fixedContainer:{position:"relative"},main:{display:"flex",flexDirection:"row-reverse",height:"100vh"},mapWrapper:{flexGrow:1e4,position:"relative"},footerWrapper:{bottom:e.spacing(2)},footer:{maxWidth:"100%",width:680,borderRadius:999},summaryWrapper:{flexBasis:375,flexGrow:1,overflowY:"auto",overflowX:"hidden"},summaryLogo:{display:"block"},summaryLogoWrapper:{top:0,display:"block",width:"100%"}})})),yr={DownhillSkiing:!0,NordicSkiing:!0,IceHockey:!0,IceSkating:!0,SkiTouring:!0,SnowShoeing:!0,Snowboarding:!0,TelemarkSkiing:!0};t.default=u.a.memo(function(e){return function(t){var n=t.workout,r={ActivityType:null===n||void 0===n?void 0:n.workout.activityId,Duration:n?Math.round((n.workout.totalTime||0)/60):void 0,Distance:n?Math.round(n.workout.totalDistance):void 0,TotalAscent:n?Math.round(n.workout.totalAscent):void 0,AverageHR:null===n||void 0===n?void 0:n.workout.hrdata.avg};return Object(g.jsx)(f.a.Provider,{value:Object(i.a)(Object(i.a)({},u.a.useContext(f.a)),r),children:Object(g.jsx)(e,Object(i.a)({},t))})}}((function(e){var t=e.workout,n=e.workoutLoading,i=Lr(),a=u.a.useState(null),d=Object(r.a)(a,2),b=d[0],h=d[1],O=u.a.useState(dt.a.satellite),C=Object(r.a)(O,2),j=C[0],y=C[1],w=Object(o.a)(),x=u.a.useRef(null),S=u.a.useRef(null),E=u.a.useState(null),T=Object(r.a)(E,2),I=T[0],N=T[1],R=u.a.useState(!1),_=Object(r.a)(R,2),k=_[0],P=_[1],A=Object(l.a)(w.breakpoints.up(Le.a))||Boolean(navigator.userAgent.match(/SamsungBrowser/i)),M=p(),H=Object(r.a)(M,3),D=H[0],V=H[1],Z=H[2],B=Object(f.c)().logEvent;u.a.useEffect((function(){if(t){B(f.b.SharedWorkoutScreen);var e=Ge(t.workout.activityId);e&&yr[e.Key]&&y(dt.a.ski);var n=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Ge(e.workout.activityId)||Ge(Ue);if(!n)throw new Error("Unspecified activity not found");var r=Qe(n.Key)||Qe(et);if(!r)throw new Error("".concat(et," graphs not found"));return t&&(r=r.filter((function(e,t,n){return n.findIndex((function(t){var n,r;return(null===(n=Fe.a[t])||void 0===n?void 0:n.getExtension)===(null===(r=Fe.a[e])||void 0===r?void 0:r.getExtension)}))===t}))),r}(t,!0).filter(function(e){return function(t){var n;return null===(n=Fe.a[t])||void 0===n?void 0:n.getExtension(e)}}(t));(null===n||void 0===n?void 0:n.length)&&(N(n),h(n[0])),P(t.showMap())}}),[t]);var W=[dt.a.satellite,dt.a.offroad,dt.a.ski],X=u.a.useMemo((function(){return{root:i.summary,section:i.summarySection}}),[i.summary]);return Object(g.jsxs)("main",{className:i.main,ref:S,children:[Object(g.jsx)(ee,{open:D,onClose:Z}),k&&Object(g.jsxs)("div",{className:i.mapWrapper,children:[Object(g.jsxs)("div",{className:i.fixedContainer,children:[!n&&t&&Object(g.jsx)(s.Suspense,{fallback:Object(g.jsx)(L,{active:!0,linear:!0}),children:Object(g.jsx)(jr,{graph:b,style:j,workout:t})}),Object(g.jsxs)("div",{className:i.userChip,children:[(null===t||void 0===t?void 0:t.workout.fullname)&&Object(g.jsx)(yt,{details:Object(g.jsx)(Et,{workout:t}),name:t.workout.fullname,avatar:t.workout.userPhoto,classes:{root:i.useChipRoot}}),Object(g.jsx)("div",{className:i.buttonWrapper,children:Object(g.jsx)(c.a,{size:"large",onClick:V,className:i.settingsButton,children:Object(g.jsx)(Cr,{className:i.settingsIcon})})})]})]}),!n&&t&&Object(g.jsxs)("div",{className:i.footerWrapper,children:[Object(g.jsx)("div",{className:i.footerSide,children:Object(g.jsx)(ut,{graphs:I,onChange:h,value:b,workout:t,classes:{root:i.pathDial}})}),Object(g.jsx)("div",{className:i.footerRight,children:Object(g.jsx)(dt.b,{styles:W,onChange:y,value:j,classes:{root:i.styleDial}})}),Object(g.jsx)("div",{className:i.footerCenter,children:Object(g.jsx)(Be,{classes:{root:i.footer},summaryScrollRef:x,workout:t,showMapControl:t.workout.availableExtensions.includes(je.b.LocationStreamExtension)})})]})]}),Object(g.jsxs)("div",{className:i.summaryWrapper,children:[Object(g.jsx)("a",{href:"https://suunto.com",target:"_blank",className:i.summaryLogoWrapper,children:Object(g.jsx)("img",{src:"dark"===w.palette.mode?m.a:v.a,alt:"Suunto logo"})}),Object(g.jsx)(L,{active:n}),!n&&t&&Object(g.jsx)(mr,{showUserChip:!k,classes:X,workout:t,scrollRef:x}),Object(g.jsx)(Ce,{classes:{root:i.siteFooter}})]}),!A&&Object(g.jsx)(Ct,{scrollToRef:S,scrollTop:130})]})})))}}]);
//# sourceMappingURL=Workout.ba1d8edd.chunk.js.map