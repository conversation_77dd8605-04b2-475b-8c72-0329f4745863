var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgRisingArrowOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M40.0873 7.9126L35.9764 28.7468C35.8427 29.4241 35.1853 29.8648 34.508 29.7312C33.8731 29.6059 33.446 29.0203 33.5052 28.3895L33.5236 28.2628L36.477 13.2896L10.1339 39.6339C9.67827 40.0895 8.95845 40.1199 8.46761 39.725L8.36612 39.6339C7.9105 39.1783 7.88013 38.4585 8.27499 37.9676L8.36612 37.8661L34.71 11.5216L19.742 14.4763C19.1071 14.6017 18.4896 14.2222 18.3047 13.6162L18.2737 13.492C18.1483 12.8571 18.5278 12.2396 19.1338 12.0547L19.258 12.0237L40.0873 7.9126Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgRisingArrowOutline);
export default __webpack_public_path__ + "static/media/rising_arrow_outline.86a68fa6.svg";
export { ForwardRef as ReactComponent };