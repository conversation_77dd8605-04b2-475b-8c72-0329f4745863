import React from 'react';
import { makeStyles } from '@mui/styles';
import { Workout } from '../../../models/Workout';
import MultisportSummarySheet from './MultisportSummarySheet';

type MultisportSummaryProps = {
  classes?: Record<string, string>;
  workout: Workout;
};

const useStyles = makeStyles(
  {
    root: {},
  },
  { name: 'MultisportSummary' },
);

function MultisportSummary(props: MultisportSummaryProps): React.ReactElement {
  const classes = useStyles(props);
  const { workout } = props;
  const multisportExtension = workout.getMultisportMarkerExtension();

  return (
    <div className={classes.root}>
      {multisportExtension?.Markers.map((multisportMarker) => (
        <MultisportSummarySheet
          key={multisportMarker.StartTime.toString()}
          multisportMarker={multisportMarker}
        />
      ))}
    </div>
  );
}

export default React.memo(MultisportSummary);
