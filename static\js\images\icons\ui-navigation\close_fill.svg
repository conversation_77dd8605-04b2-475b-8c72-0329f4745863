var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgCloseFill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24 21.879L37.6893 8.18934C38.2751 7.60355 39.2249 7.60355 39.8107 8.18934C40.3598 8.73851 40.3942 9.60758 39.9136 10.1967L39.8107 10.3107L26.121 24L39.8107 37.6893C40.3598 38.2385 40.3942 39.1076 39.9136 39.6967L39.8107 39.8107C39.2615 40.3598 38.3924 40.3942 37.8033 39.9136L37.6893 39.8107L24 26.121L10.3107 39.8107C9.76149 40.3598 8.89242 40.3942 8.30326 39.9136L8.18934 39.8107C7.64017 39.2615 7.60584 38.3924 8.08637 37.8033L8.18934 37.6893L21.879 24L8.18934 10.3107C7.64017 9.76149 7.60584 8.89242 8.08637 8.30326L8.18934 8.18934C8.73851 7.64017 9.60758 7.60584 10.1967 8.08637L10.3107 8.18934L24 21.879L37.6893 8.18934L24 21.879Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgCloseFill);
export default __webpack_public_path__ + "static/media/close_fill.c792febb.svg";
export { ForwardRef as ReactComponent };