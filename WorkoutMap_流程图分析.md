# WorkoutMap.ts 流程图分析

本文档通过 Mermaid 流程图详细分析了 `WorkoutMap.ts` 文件的架构和工作流程。

## 1. 类架构图

```mermaid
classDiagram
    class WorkoutMap {
        +point: Marker
        +pts: number[][]
        +mapbox: Map
        +graph: string
        +minMaxMarkers: Marker[]
        +workout: Workout
        +cameraPath: CameraPath
        +playing: boolean
        +elevationImage: Image
        +elevationContext: CanvasRenderingContext2D
        +lineLayer: LineLayer
        +demSource: string
        +style: string
        +div: HTMLDivElement
        
        +constructor(div, workout, style, position, measurementSystem, classes, readyCallback)
        +createMap(div, style): Map
        +destructor(): void
        +updateTerrain(): void
        +setClasses(classes): void
        +setStyle(style): Map
        +setGraph(newGraph, labels): void
        +setMeasurementSystem(measurementSystem): void
        +formatValue(value): string
        +addMarker(pt): Marker
        +setPlaying(playing): void
        +getProgressMarkerPositionByLineString(): [number, number, number]
        +moveProgressMarker(coords): void
        -calculateNewPosition(): object
        +isCameraAtCurrentPosition(): boolean
        +setPosition(position): void
    }
    
    class Map {
        +addSource(id, source)
        +addLayer(layer, idAbove)
        +setTerrain(terrain)
        +getCenter(): LngLat
        +getZoom(): number
        +fitBounds(bounds)
        +on(event, handler)
        +once(event, handler)
    }
    
    class CameraPath {
        +cameraPath: Path
        +lookAtCurve: Curve
        +narrowLookAtCurve: Curve
        +lookAtPointCenteringBehaviour: Curve
        +cameraEasing(position): number
        +getLatLonAltFromPseudoCartesianCoordinates(point): LatLonAlt
    }
    
    class LineLayer {
        +updateData(force)
        +setPosition(position)
        +progressed: boolean
    }
    
    class Workout {
        +getRoute(graph, workout, labels): Route
        +getLocation(): Location
        +workout: WorkoutData
    }
    
    WorkoutMap --> Map : 使用
    WorkoutMap --> CameraPath : 包含
    WorkoutMap --> LineLayer : 管理
    WorkoutMap --> Workout : 处理
```

## 2. 初始化流程图

```mermaid
flowchart TD
    A[开始: 构造函数] --> B[接收参数]
    B --> C[初始化属性]
    C --> D[获取默认路线数据]
    D --> E[设置地图样式]
    E --> F[创建 Mapbox 地图实例]
    
    F --> G[设置地图基本参数]
    G --> H[计算轨迹边界]
    H --> I[调整地图边界]
    I --> J[设置事件监听器]
    
    J --> K[添加归属控件]
    K --> L[等待样式加载完成]
    L --> M[添加 MML DEM 数据源]
    M --> N[添加 Mapbox DEM 数据源]
    
    N --> O[更新地形]
    O --> P[设置数据更新定时器]
    P --> Q[移动进度标记到初始位置]
    Q --> R[初始化地形变化图像]
    
    R --> S[创建 Canvas 上下文]
    S --> T[等待样式加载后更新地形]
    T --> U[初始化完成]
    
    style A fill:#e1f5fe
    style U fill:#c8e6c9
```

## 3. 地形更新流程图

```mermaid
flowchart TD
    A[触发地形更新] --> B[获取地图中心位置]
    B --> C[设置默认值]
    C --> D{缩放级别 >= 10?}
    
    D -->|是| E[检查是否在芬兰境内]
    D -->|否| F[使用 Mapbox DEM]
    
    E --> G{在 MML 边界内?}
    G -->|是| H[使用 MML DEM]
    G -->|否| F
    
    H --> I[获取地形变化值]
    F --> I
    
    I --> J[计算地形夸张度]
    J --> K{数据源是否变化?}
    
    K -->|是| L[更新地形设置]
    K -->|否| M[结束]
    
    L --> N[设置插值夸张度]
    N --> O[更新 demSource]
    O --> M
    
    style A fill:#fff3e0
    style M fill:#c8e6c9
    style H fill:#e8f5e8
    style F fill:#e8f5e8
```

## 4. 进度更新流程图

```mermaid
flowchart TD
    A[设置新位置] --> B[更新 position 属性]
    B --> C[计算新的相机位置]
    C --> D[获取进度标记坐标]
    D --> E[查询地形高程]
    E --> F[计算相机进度]
    F --> G[计算居中行为]
    
    G --> H[计算相机角度]
    H --> I{有地形高程数据?}
    I -->|是| J[调整相机角度]
    I -->|否| K[使用原始角度]
    
    J --> L[计算相机位置和俯仰角]
    K --> L
    
    L --> M[移动进度标记]
    M --> N[更新兴趣点标记可见性]
    N --> O[更新路线图层进度]
    
    O --> P[更新相机位置]
    P --> Q[设置相机角度和俯仰角]
    Q --> R[进度更新完成]
    
    style A fill:#fff3e0
    style R fill:#c8e6c9
    style J fill:#e8f5e8
    style K fill:#e8f5e8
```

## 5. 数据格式化流程图

```mermaid
flowchart TD
    A[输入数值] --> B{有图表类型?}
    B -->|否| C[返回原始数值字符串]
    B -->|是| D[获取格式化样式]
    
    D --> E{有格式化样式?}
    E -->|否| C
    E -->|是| F[创建格式化选项]
    
    F --> G[选择测量系统]
    G --> H[转换数值到 SI 单位]
    H --> I[构建格式化样式名称]
    I --> J[执行格式化]
    
    J --> K{格式化成功?}
    K -->|是| L[返回格式化结果]
    K -->|否| C
    
    style A fill:#fff3e0
    style L fill:#c8e6c9
    style C fill:#ffcdd2
```

## 6. 地图样式设置流程图

```mermaid
flowchart TD
    A[设置地图样式] --> B{样式类型检查}
    B --> C{是卫星图或无样式?}
    
    C -->|是| D[使用默认卫星图样式]
    C -->|否| E[构建自定义样式 URL]
    
    E --> F{是中国环境?}
    F -->|是| G[添加 CN 后缀]
    F -->|否| H[使用原始 URL]
    
    D --> I{样式是否变化?}
    G --> I
    H --> I
    
    I -->|否| J[返回当前地图实例]
    I -->|是| K[清理旧地图]
    
    K --> L[重置相关属性]
    L --> M[创建新地图实例]
    M --> N[返回新地图实例]
    
    style A fill:#fff3e0
    style N fill:#c8e6c9
    style J fill:#e8f5e8
```

## 7. 兴趣点标记添加流程图

```mermaid
flowchart TD
    A[添加兴趣点标记] --> B[获取样式类名]
    B --> C[创建 DOM 结构]
    C --> D[创建标签容器]
    D --> E[创建标签包装器]
    E --> F[创建标签连接线]
    F --> G[创建标签主体]
    
    G --> H[创建标签标题]
    H --> I[设置标题文本]
    I --> J[创建标签数值]
    J --> K[格式化数值]
    K --> L[设置数值文本]
    
    L --> M[创建 Mapbox 标记]
    M --> N[设置标记位置]
    N --> O[添加到地图]
    O --> P[保存标记信息]
    P --> Q[返回标记对象]
    
    style A fill:#fff3e0
    style Q fill:#c8e6c9
```

## 8. 相机位置计算流程图

```mermaid
flowchart TD
    A[计算相机位置] --> B[获取相机路径数据]
    B --> C[获取进度标记坐标]
    C --> D[查询地形高程]
    D --> E[计算相机进度]
    E --> F[获取画布尺寸]
    
    F --> G[计算居中行为]
    G --> H[获取相机路径点]
    H --> I[计算相机角度]
    I --> J{有地形高程?}
    
    J -->|是| K[获取窄视角路径点]
    J -->|否| L[使用原始角度]
    
    K --> M[计算窄视角角度]
    M --> N[角度范围调整]
    N --> O[插值计算最终角度]
    
    L --> P[计算相机位置]
    O --> P
    P --> Q[计算俯仰角]
    Q --> R[返回计算结果]
    
    style A fill:#fff3e0
    style R fill:#c8e6c9
    style L fill:#e8f5e8
    style O fill:#e8f5e8
```

## 总结

通过以上流程图分析，我们可以看到 `WorkoutMap.ts` 文件具有以下特点：

1. **模块化设计**: 类结构清晰，职责分明
2. **智能地形处理**: 根据地理位置和缩放级别自动选择最佳地形数据源
3. **流畅的动画**: 通过相机路径和进度跟踪实现平滑的动画效果
4. **高性能渲染**: 使用定时器和事件优化来确保流畅的用户体验
5. **多语言支持**: 支持不同的测量系统和格式化选项
6. **可扩展性**: 支持多种地图样式和数据源

这个组件是 Suunto 运动轨迹应用的核心地图渲染引擎，提供了丰富的功能和优秀的用户体验。 