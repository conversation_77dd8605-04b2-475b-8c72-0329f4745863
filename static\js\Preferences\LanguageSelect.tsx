import { NativeSelect } from '@mui/material';
import React from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import Languages from '../i18n/Languages';
import { ReactComponent as DownIcon } from '../images/icons/ui-navigation/chevron_down_outline.svg';

type LanguageSelectProps = {
  classes?: Record<string, string>;
  id: string;
};

const useStyles = makeStyles(
  {
    root: {},
    downIcon: {
      width: '2rem',
      height: '2rem',
      marginTop: '-0.4rem',
    },
    select: {
      textTransform: 'uppercase',
      padding: 0,
    },
  },
  { name: 'LanguageSelect' },
);

function LanguageSelect(props: LanguageSelectProps): React.ReactElement {
  const classes = useStyles(props);
  const { id } = props;
  const { i18n } = useTranslation();
  const defaultLanguage =
    localStorage.getItem('i18nextLng')?.split('-')[0] || i18n.language || 'zh';

  const handleChangeLanguage = ({ target: { value } }: React.ChangeEvent<HTMLSelectElement>) =>
    value && i18n.changeLanguage(value);

  return (
    <NativeSelect
      id={id}
      defaultValue={i18n.language}
      value={defaultLanguage}
      onChange={handleChangeLanguage}
      disableUnderline
      classes={{
        root: classes.root,
        icon: classes.downIcon,
        select: classes.select,
      }}
      IconComponent={DownIcon}
    >
      {Object.values(Languages).map(({ language, label }) => (
        <option key={language} value={language}>
          {label}
        </option>
      ))}
    </NativeSelect>
  );
}

export default LanguageSelect;
