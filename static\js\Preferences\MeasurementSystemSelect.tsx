import { FormControlLabel, Radio, RadioGroup } from '@mui/material';
import React, { useContext } from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import namespaces from '../i18n/namespaces';
import {
  MeasurementSystem,
  MeasurementSystemContext,
} from '../MeasurementSystem/MeasurementSystem';

type MeasurementSystemInputProps = {
  classes?: Record<string, string>;
  id: string;
};

const useStyles = makeStyles(
  {
    root: {},
    radioLabel: {
      marginRight: 0,
      textTransform: 'uppercase',
      display: 'flex',
      flexDirection: 'row-reverse',
      marginLeft: 0,
      justifyContent: 'space-between',
      flexGrow: 1,
    },
    input: {
      flexGrow: 1,
    },
  },
  { name: 'MeasurementSystemInput' },
);

function MeasurementSystemInput(props: MeasurementSystemInputProps): React.ReactElement {
  const classes = useStyles(props);
  const { id } = props;
  const [measurementSystem, setMeasurementSystem] = useContext(MeasurementSystemContext);
  const { t } = useTranslation([namespaces.PHRASES]);
  const handleChangeMeasurementSystem = ({
    target: { value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    if (value) {
      setMeasurementSystem(value as MeasurementSystem);
    }
  };
  return (
    <RadioGroup
      color="primary"
      value={measurementSystem}
      id={id}
      className={classes.root}
      onChange={handleChangeMeasurementSystem}
    >
      <FormControlLabel
        className={classes.radioLabel}
        value={MeasurementSystem.metric}
        control={<Radio size="large" />}
        label={<span>{t('TXT_METRIC')}</span>}
      />
      <FormControlLabel
        className={classes.radioLabel}
        value={MeasurementSystem.imperial}
        control={<Radio size="large" />}
        label={<span>{t('TXT_IMPERIAL')}</span>}
      />
    </RadioGroup>
  );
}

export default MeasurementSystemInput;
