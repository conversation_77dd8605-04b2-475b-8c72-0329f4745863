import { makeStyles } from '@mui/styles';
import React from 'react';

interface TextPlaceholderProps {
  classes?: Record<string, string>;
}

const useStyles = makeStyles(
  (theme) => ({
    root: {
      background: theme.palette.action.disabledBackground,
      display: 'inline-block',
    },
  }),
  { name: 'TextPlaceholder' },
);

function TextPlaceholder(props: TextPlaceholderProps): React.ReactElement {
  const classes = useStyles(props);

  return <span className={classes.root} />;
}

export default TextPlaceholder;
