import activitySummaries from '@suunto/suunto-information-model/Specifications/SuuntoAppConfiguration/activitySummaries.json';

const getActivitySummaryByActivityName = (activity: string) => {
  return activitySummaries.find(({ Activities }) => Activities.includes(activity));
};

export const getSummaryItemsByActivityName = (activity: string): Array<string> | undefined => {
  return getActivitySummaryByActivityName(activity)?.Items;
};

const TranslateKeyMap: Record<string, string> = {
  Heartrate: 'HeartRate',
};

export const translateKey = (key: string): string => TranslateKeyMap[key] || key;

export const getGraphsByActivityName = (activity: string): Array<string> | undefined => {
  const activitySummary = getActivitySummaryByActivityName(activity);
  if (!activitySummary) return undefined;
  const { Graphs = [], ZoneGraphs = [] } = activitySummary;
  return [...ZoneGraphs, ...Graphs].map(translateKey);
};
