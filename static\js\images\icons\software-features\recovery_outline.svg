var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgRecoveryOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M26.75 4.02502C27.4404 4.02502 28 4.58467 28 5.27502C28 5.92223 27.5081 6.45456 26.8778 6.51857L26.75 6.52502H25.249L25.25 8.54336C34.4695 9.18482 41.75 16.8675 41.75 26.25C41.75 36.0528 33.8028 44 24 44C14.1972 44 6.25 36.0528 6.25 26.25C6.25 16.8678 13.5299 9.18538 22.749 8.54343L22.749 6.52502H21.25C20.5596 6.52502 20 5.96538 20 5.27502C20 4.62782 20.4919 4.09549 21.1222 4.03148L21.25 4.02502H26.75ZM24 11C15.578 11 8.75 17.828 8.75 26.25C8.75 34.6721 15.578 41.5 24 41.5C32.422 41.5 39.25 34.6721 39.25 26.25C39.25 17.828 32.422 11 24 11ZM25.275 14.4676L25.3661 14.3661C25.8217 13.9105 26.5415 13.8802 27.0324 14.275L27.1339 14.3661L31.2678 18.5L27.1339 22.6339C26.6457 23.1221 25.8543 23.1221 25.3661 22.6339C24.9105 22.1783 24.8801 21.4585 25.275 20.9676L25.3661 20.8661L26.482 19.75H24C20.4378 19.75 17.5 22.6878 17.5 26.25C17.5 29.8123 20.4378 32.75 24 32.75C27.4848 32.75 30.372 29.9386 30.4959 26.4814L30.5 26.25H33C33 31.193 28.9429 35.25 24 35.25C19.0571 35.25 15 31.193 15 26.25C15 21.3953 18.9135 17.3952 23.7361 17.2539L24 17.25H26.483L25.3661 16.1339C24.9105 15.6783 24.8801 14.9585 25.275 14.4676L25.3661 14.3661L25.275 14.4676ZM37.2505 8.2503L37.3549 8.3381L41.3549 12.0881C41.8586 12.5603 41.8841 13.3513 41.4119 13.855C40.9712 14.325 40.2528 14.3786 39.7495 13.9997L39.6451 13.9119L35.6451 10.1619C35.1414 9.68978 35.1159 8.89874 35.5881 8.3951C36.0288 7.92503 36.7472 7.87147 37.2505 8.2503Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgRecoveryOutline);
export default __webpack_public_path__ + "static/media/recovery_outline.91c92a99.svg";
export { ForwardRef as ReactComponent };