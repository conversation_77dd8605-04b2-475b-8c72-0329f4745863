import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { CopyrightItem } from '../parseSiteFooter';

type CopyrightsProps = {
  classes?: Record<string, string>;
  copyrights?: CopyrightItem[];
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      textAlign: 'center',
    },
    item: {
      marginLeft: theme.spacing(0.4),
      marginRight: theme.spacing(0.4),
      textTransform: 'uppercase',
    },
  }),
  { name: 'Copyrights' },
);

function Copyrights(props: CopyrightsProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { copyrights } = props;
  if (!copyrights) return null;
  return (
    <section className={classes.root}>
      {copyrights.map(({ title }) => (
        <span key={title} className={classes.item}>
          {title}
        </span>
      ))}
    </section>
  );
}

export default Copyrights;
