import React, { ReactNode } from 'react';
import { Avatar, Typography } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';

export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((str, i, arr) => {
      if (str && (i === 0 || i === arr.length - 1)) return str[0].toUpperCase();
      else return '';
    })
    .join('');
};

type UserChipProps = {
  classes?: Record<string, string>;
  name: string;
  avatar?: string | null;
  details?: string | ReactNode;
  ChipClasses?: Record<string, string>;
};

const useStyles = makeStyles((theme) => ({
  root: {
    background: theme.palette.background.default,
    padding: theme.spacing(1),
    display: 'flex',
    alignItems: 'center',
    borderRadius: 999,
    paddingRight: theme.spacing(2),
    lineHeight: 1,
  },
  avatar: {
    marginRight: theme.spacing(1),
    height: '3.5rem',
    width: '3.5rem',
  },
  label: {},
  info: {
    lineHeight: 7 / 6,
  },
  details: {},
}));

function UserChip(props: UserChipProps): React.ReactElement {
  const classes = useStyles(props);
  return (
    <Typography component="div" variant="body2" className={classes.root}>
      <Avatar classes={{ root: classes.avatar }} alt={props.name} src={props.avatar || undefined}>
        {getInitials(props.name)}
      </Avatar>
      <div className={classes.info}>
        <strong className={classes.label}>{props.name}</strong>
        {!!props.details && <div className={classes.info}>{props.details}</div>}
      </div>
    </Typography>
  );
}

export default UserChip;
