import React, { Suspense } from 'react';
import { Backdrop, Icon<PERSON>utton, Modal } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import { useTranslation } from 'react-i18next';
import { ThemeProvider, Theme } from '@mui/material';
import namespaces from '../../i18n/namespaces';
import { ReactComponent as CloseIcon } from '../../images/icons/ui-navigation/close_circle_outline.svg';
import LoadingIndicator from '../../LoadingIndicator/LoadingIndicator';
import { dark } from '../../theme/theme';
import { Photo } from '../../types/WorkoutPayload';
import Carousel from './Carousel';

declare module '@mui/styles/defaultTheme' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface DefaultTheme extends Theme {}
}

type TheatreProps = {
  classes?: Record<string, string>;
  onClose: (event: React.MouseEvent<HTMLButtonElement>) => void;
  open: boolean;
  photos: Photo[];
  selectedPhoto?: Photo;
  onChange: (photo: Photo | null) => void;
};

const useStyles = makeStyles(
  {
    root: {
      [[
        '& .alice-carousel',
        '& .alice-carousel > div',
        '& .alice-carousel .alice-carousel__wrapper',
      ].join(',')]: {
        height: '100%',
      },
      [['& .alice-carousel__prev-btn', '& .alice-carousel__next-btn'].join(',')]: {
        width: 0,
        top: 0,
        position: 'absolute',
        padding: 0,
        display: 'flex',
        alignItems: 'center',
      },
      '& .alice-carousel__prev-btn': {
        left: 0,
      },
      '& .alice-carousel__next-btn': {
        left: '100%',
        width: 'auto',
      },
    },
    closeButton: {
      position: 'absolute',
      top: '0',
      right: '0',
    },
    carouselWrapper: {
      display: 'flex',
      height: '100%',
      width: '100%',
      '&:focus-visible': {
        outline: 'none',
      },
    },
    buttonRoot: {
      position: 'absolute',
    },
    closeIcon: {
      width: '4rem',
      height: '4rem',
    },
    loadingIndicator: {
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      position: 'absolute',
    },
  },
  { name: 'Theatre' },
);

function Theatre(props: TheatreProps): React.ReactElement {
  const classes = useStyles(props);
  const { t } = useTranslation([namespaces.CONTROLS]);
  const { onClose, open, photos, selectedPhoto, onChange } = props;
  const theme = React.useMemo(() => createTheme(dark), []);
  return (
    <ThemeProvider theme={theme}>
      <Modal className={classes.root} onClose={onClose} open={open} BackdropComponent={Backdrop}>
        <div className={classes.carouselWrapper}>
          <Suspense
            fallback={<LoadingIndicator active classes={{ root: classes.loadingIndicator }} />}
          >
            <Carousel onChange={onChange} photos={photos} selectedPhoto={selectedPhoto} />
          </Suspense>
          <IconButton
            onClick={onClose}
            aria-label={t('CLOSE')}
            className={classes.closeButton}
            size="large"
          >
            <CloseIcon className={classes.closeIcon} />
          </IconButton>
        </div>
      </Modal>
    </ThemeProvider>
  );
}

export default Theatre;
