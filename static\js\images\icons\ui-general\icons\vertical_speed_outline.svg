var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgVerticalSpeedOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M9.87454 11.9842C17.6625 4.19626 30.3351 4.19626 38.1231 11.9842C45.8205 19.6816 45.91 31.6435 38.3916 39.4401L38.1231 39.7135L36.3553 37.9458C43.167 31.1341 43.167 20.5636 36.3553 13.752C29.5437 6.94034 18.4539 6.94034 11.6423 13.752C4.91912 20.4752 4.83181 30.8602 11.3804 37.6785L11.6423 37.9458L9.87454 39.7135C2.08658 31.9256 2.08658 19.7722 9.87454 11.9842ZM23.9988 17.7944L32.7296 29.4354H15.268L23.9988 17.7944ZM23.9988 21.961L20.268 26.9354H27.7296L23.9988 21.961Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgVerticalSpeedOutline);
export default __webpack_public_path__ + "static/media/vertical_speed_outline.11ed9903.svg";
export { ForwardRef as ReactComponent };