import { Typography, useMediaQuery, useTheme } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import classNames from 'classnames';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIds, getActivityConfigBySTId } from '../../activities/helper';
import { AmplitudeContext } from '../../Amplitude/Amplitude';
import namespaces from '../../i18n/namespaces';
import { Workout } from '../../models/Workout';
import { WorkoutExtensionType } from '../../types/WorkoutPayload';
import UserChip from '../UserChip/UserChip';
import WorkoutDetails from '../UserChip/WorkoutDetails';
import PopularProducts from '../PopularProducts/PopularProducts';
import Photos from '../../Photos/Photos';
import { TWO_PANEL_BREAKPOINT } from '../workoutConfig';
import MultisportSummary from './Multisport/MultisportSummary';
import SummaryItems from './SummaryItems/SummaryItems';

type WorkoutSummaryProps = {
  classes?: Record<string, string>;
  workout: Workout;
  scrollRef: React.MutableRefObject<HTMLDivElement | null>;
  showUserChip: boolean;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      position: 'relative',
      maxWidth: '100%',
    },
    description: {},
    section: {
      position: 'relative',
      maxWidth: '100%',
      width: 900,
      marginBottom: theme.spacing(4.5),
      '&$wideSection': {
        paddingLeft: 0,
        paddingRight: 0,
      },
    },
    wideSection: {},
    photosSection: {},
    popularProductsSection: {},
    popularProductsTitle: {
      marginBottom: theme.spacing(4),
    },
    userChip: {
      paddingLeft: 0,
    },
    activityName: {
      textTransform: 'uppercase',
      textAlign: 'center',
      fontWeight: 700,
      position: 'sticky',
      background: theme.palette.background.default,
      top: 0,
      paddingTop: theme.spacing(2),
      paddingBottom: theme.spacing(2),
      zIndex: 3,
      [theme.breakpoints.up(TWO_PANEL_BREAKPOINT)]: {
        textAlign: 'left',
      },
    },
  }),
  { name: 'WorkoutSummary' },
);

function withAmplitude(Component: React.FunctionComponent<WorkoutSummaryProps>) {
  return (props: WorkoutSummaryProps) => (
    <AmplitudeContext.Provider
      value={{ ...React.useContext(AmplitudeContext), Source: 'LeftPane' }}
    >
      <Component {...props} />
    </AmplitudeContext.Provider>
  );
}

function WorkoutSummary(props: WorkoutSummaryProps): React.ReactElement {
  const { t } = useTranslation([namespaces.PHRASES, namespaces.TRANSLATIONS]);
  const { workout, showUserChip } = props;
  const classes = useStyles(props);
  const theme = useTheme();
  const isSidePanel = useMediaQuery(theme.breakpoints.up(TWO_PANEL_BREAKPOINT));

  const { description } = workout.workout;
  const activityConfig =
    getActivityConfigBySTId(workout.workout.activityId) ||
    getActivityConfigBySTId(ActivityIds.UNSPECIFIED);

  if (!activityConfig) {
    throw new Error('Unspecified activity not found');
  }

  const isMultisport = workout.workout.availableExtensions.includes(
    WorkoutExtensionType.MultisportMarkerExtension,
  );

  return (
    <div className={classes.root} ref={props.scrollRef}>
      <header className={classes.section}>
        <Typography
          component="h2"
          variant={isSidePanel ? 'h1' : 'subtitle2'}
          className={classes.activityName}
        >
          {t(activityConfig.PhraseID)}
        </Typography>
        {description && (
          <Typography component="div" variant="h6" className={classes.description}>
            <strong>{description}</strong>
          </Typography>
        )}
      </header>
      {!!workout.workout.photos?.length && (
        <Photos
          photos={workout.workout.photos}
          classes={{ root: classNames(classes.section, classes.wideSection) }}
        />
      )}
      {showUserChip && (
        <div className={classes.section}>
          <UserChip
            classes={{ root: classes.userChip }}
            details={<WorkoutDetails hideWorkoutName workout={workout} />}
            name={workout.workout.fullname}
            avatar={workout.workout.userPhoto}
          />
        </div>
      )}
      <div className={classes.section}>
        <SummaryItems workout={workout} activityConfig={activityConfig} />
      </div>
      <div className={classNames(classes.section, classes.wideSection)}>
        {isMultisport && <MultisportSummary workout={workout} />}
      </div>
      <Typography
        variant="h4"
        className={classNames(classes.section, classes.popularProductsTitle)}
      >
        {t(`${namespaces.TRANSLATIONS}:MOST_POPULAR_PRODUCTS_TITLE`)}
      </Typography>
      <PopularProducts classes={{ root: classNames(classes.section, classes.wideSection) }} />
    </div>
  );
}

export default React.memo(withAmplitude(WorkoutSummary));
