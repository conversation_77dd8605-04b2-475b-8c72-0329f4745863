import Slider from '@mui/material/Slider';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import noOp from '../../../helpers/noOp';
import useToggle from '../../../helpers/useToggle';

type SliderProps = React.ComponentProps<typeof Slider>;
type ScrollableSliderProps = Omit<
  SliderProps,
  'onTouchStartCapture' | 'onTouchEnd' | 'onTouchCancel' | 'onTouchMove'
>;

const useStyles = makeStyles(
  {
    noScroll: {
      overflow: 'hidden !important',
    },
    root: {
      touchAction: 'auto',
    },
  },
  { name: 'ScrollableSlider' },
);

const VERTICAL_TOLERANCE = 30;

function ScrollableSlider(props: ScrollableSliderProps): React.ReactElement {
  const classes = useStyles(props);
  const yStart = React.useRef<number>(0);
  const [isScrollDisabled, disableScroll, enableScroll] = useToggle();

  const onTouchStart = (event: React.TouchEvent) => {
    yStart.current = event.touches[0].clientY;
    event.nativeEvent.preventDefault = noOp;
    disableScroll();
  };

  const onTouchMove = (event: React.TouchEvent) => {
    const dy = event.touches[0].clientY - yStart.current;
    if (Math.abs(dy) > VERTICAL_TOLERANCE) {
      enableScroll();
    }
  };

  React.useEffect(() => {
    if (isScrollDisabled) {
      document.documentElement.classList.add(classes.noScroll);
    }
    return () => {
      document.documentElement.classList.remove(classes.noScroll);
    };
  }, [classes.noScroll, disableScroll]);

  return (
    <Slider
      onTouchStartCapture={onTouchStart}
      onTouchEnd={enableScroll}
      onTouchCancel={enableScroll}
      onTouchMove={onTouchMove}
      classes={{ ...props.classes, root: classes.root }}
      {...props}
    />
  );
}

export default ScrollableSlider;
