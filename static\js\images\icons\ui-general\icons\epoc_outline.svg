var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgEpocOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M4.05298 13.285V34.75H11.964V32.482H6.50998V24.868H11.181V22.6H6.50998V15.553H11.964V13.285H4.05298ZM16.008 34.75V15.553H18.222C19.707 15.553 19.977 15.985 19.977 17.659V23.329C19.977 24.967 19.7266 25.4533 18.382 25.487L16.791 25.489V27.757H18.33C21.4824 27.757 22.3855 26.5556 22.4321 24.153L22.434 17.011C22.434 14.446 21.57 13.285 18.33 13.285H13.551V34.75H16.008ZM28.854 35.047C32.3837 35.047 33.1637 33.3508 33.1996 31.2373L33.201 16.957C33.201 14.77 32.472 12.988 28.854 12.988H28.26C24.669 12.988 23.913 14.77 23.913 16.957V31.078L23.9189 31.4028C23.9957 33.4387 24.8485 35.047 28.26 35.047H28.854ZM28.233 32.779C26.532 32.779 26.37 32.104 26.37 30.43V17.605C26.37 15.931 26.505 15.256 28.233 15.256H28.854C30.4942 15.256 30.7285 15.8836 30.7432 17.4294L30.744 30.43C30.744 32.104 30.528 32.779 28.854 32.779H28.233ZM39.783 35.047H39.405C35.922 35.047 35.193 33.373 35.193 31.105V16.957L35.1987 16.6175C35.2728 14.4988 36.0961 12.988 39.405 12.988H39.783C42.915 12.988 43.779 14.662 43.887 15.796L44.103 18.361H41.646L41.484 16.471C41.43 15.931 41.268 15.256 39.783 15.256H39.351C37.843 15.256 37.6619 15.8819 37.6506 17.4231L37.65 30.457C37.65 32.158 37.785 32.779 39.351 32.779H39.783C41.241 32.779 41.322 32.185 41.376 31.591L41.565 29.08H44.022L43.779 32.266L43.7571 32.4625C43.5977 33.5402 42.7307 35.047 39.783 35.047Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgEpocOutline);
export default __webpack_public_path__ + "static/media/epoc_outline.8e390c98.svg";
export { ForwardRef as ReactComponent };