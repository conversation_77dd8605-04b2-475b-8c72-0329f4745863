import mapboxgl from '!mapbox-gl';

export const getImageFromMap = (map: mapboxgl.Map, canvas: HTMLCanvasElement): Promise<void> =>
  new Promise<void>((resolve, reject) => {
    map.once('render', () => {
      // Todo: aseta canvaksen koko
      const mapCanvas = map.getCanvas();
      canvas.width = mapCanvas.width;
      canvas.height = mapCanvas.height;

      const gc = canvas.getContext('2d');
      if (gc) {
        gc.drawImage(
          mapCanvas,
          0,
          0,
          mapCanvas.width,
          mapCanvas.height,
          0,
          0,
          canvas.width,
          canvas.height,
        );
        resolve();
      } else {
        reject();
      }
    });
    map.setBearing(map.getBearing());
  });
