import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import TextPlaceholder from '../../../../Placeholder/TextPlaceholder';
import SummaryItemView from './SummaryItemView';

const SummaryItem = React.lazy(() => import('./SummaryItem'));

const usePlaceholderStyles = makeStyles(
  (theme) => ({
    commonPlaceholder: {
      marginTop: theme.spacing(1.1),
    },
    icon: {
      width: '1.5rem',
      height: '1.5rem',
      borderRadius: 999,
    },
    value: {
      height: '1.5rem',
      marginBottom: '1.1rem',
      width: '9rem',
      maxWidth: '100%',
      '&$small': {
        marginBottom: 0,
        marginTop: 0,
        height: '1.1rem',
      },
    },
    small: {},
  }),
  { name: 'SummaryItemWrapper' },
);

const SummaryItemPlaceholder = (
  props: React.ComponentProps<typeof SummaryItem>,
): React.ReactElement => {
  const classes = usePlaceholderStyles();

  return (
    <SummaryItemView
      {...props}
      Icon={() => (
        <TextPlaceholder classes={{ root: [classes.commonPlaceholder, classes.icon].join(' ') }} />
      )}
      value={
        <TextPlaceholder
          classes={{
            root: classNames(classes.commonPlaceholder, classes.value, {
              [classes.small]: props.small,
            }),
          }}
        />
      }
    />
  );
};

function Index(props: React.ComponentProps<typeof SummaryItem>): React.ReactElement | null {
  return (
    <React.Suspense fallback={<SummaryItemPlaceholder {...props} />}>
      <SummaryItem {...props} />
    </React.Suspense>
  );
}

export default Index;
