var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSunsetOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24.0001 29C30.1162 29 35.0924 33.8806 35.2464 39.9596L35.2501 40.25H42.7501C43.4405 40.25 44.0001 40.8096 44.0001 41.5C44.0001 42.1472 43.5082 42.6795 42.8779 42.7435L42.7501 42.75H5.2501C4.55974 42.75 4.00009 42.1904 4.00009 41.5C4.00009 40.8528 4.49197 40.3205 5.12229 40.2565L5.2501 40.25H12.7501C12.7501 34.0368 17.7869 29 24.0001 29ZM24.0001 31.5C19.2539 31.5 15.3903 35.2788 15.2538 39.992L15.2501 40.25H32.7501C32.7501 35.4175 28.8326 31.5 24.0001 31.5ZM43.9359 32.3547C44.1406 32.9687 43.8423 33.6293 43.2646 33.8893L43.1454 33.9359L39.3954 35.1859C38.7405 35.4042 38.0326 35.0502 37.8142 34.3953C37.6096 33.7813 37.9079 33.1207 38.4856 32.8607L38.6048 32.8141L42.3548 31.5641C43.0097 31.3458 43.7176 31.6998 43.9359 32.3547ZM5.52209 31.5299L5.64538 31.5641L9.39538 32.8141C10.0503 33.0325 10.4043 33.7404 10.1859 34.3953C9.98128 35.0093 9.34631 35.3587 8.7281 35.2201L8.60481 35.1859L4.85481 33.9359C4.19988 33.7175 3.84593 33.0096 4.06424 32.3547C4.26891 31.7407 4.90388 31.3913 5.52209 31.5299ZM37.384 23.1161C37.8396 23.5717 37.87 24.2915 37.4751 24.7824L37.384 24.8839L34.884 27.3839C34.3958 27.872 33.6044 27.872 33.1162 27.3839C32.6606 26.9283 32.6302 26.2085 33.0251 25.7176L33.1162 25.6161L35.6162 23.1161C36.1044 22.628 36.8958 22.628 37.384 23.1161ZM12.2825 23.025L12.384 23.1161L14.884 25.6161C15.3721 26.1043 15.3721 26.8957 14.884 27.3839C14.4284 27.8395 13.7085 27.8699 13.2177 27.475L13.1162 27.3839L10.6162 24.8839C10.1281 24.3957 10.1281 23.6043 10.6162 23.1161C11.0718 22.6605 11.7916 22.6301 12.2825 23.025ZM23.8723 4.00645L24.0001 4C24.6473 4 25.1796 4.49187 25.2436 5.12219L25.2501 5.25L25.2498 18.482L30.6162 13.1161C31.1044 12.628 31.8958 12.628 32.384 13.1161C32.8396 13.5717 32.87 14.2915 32.4751 14.7824L32.384 14.8839L24.0001 23.2678L15.6162 14.8839C15.1281 14.3957 15.1281 13.6043 15.6162 13.1161C16.0718 12.6605 16.7916 12.6301 17.2825 13.025L17.384 13.1161L22.7498 18.482L22.7501 5.25C22.7501 4.60279 23.242 4.07047 23.8723 4.00645L24.0001 4L23.8723 4.00645Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSunsetOutline);
export default __webpack_public_path__ + "static/media/sunset_outline.ebacf645.svg";
export { ForwardRef as ReactComponent };