# static/js/Workout/Mapbox 文件夹详细导读

## 概述

`static/js/Workout/Mapbox` 文件夹包含了 Suunto 地图应用中 Mapbox 地图组件的 React 集成层。这个模块负责将底层的 WorkoutMap 类包装成 React 组件，提供地图的生命周期管理、用户交互处理、视觉效果和截图功能。

## 文件结构

```
static/js/Workout/Mapbox/
├── Mapbox.tsx                       # 主要的 React 地图组件
├── CrossFadeOverlay.tsx             # 交叉淡入淡出效果组件
└── getImageFromMap.ts               # 地图截图工具函数
```

---

## 1. Mapbox.tsx - 主要地图组件

### 核心功能架构

#### 组件接口定义
```typescript
interface MapBoxProps {
  workout: Workout;     // 运动数据对象
  graph: string | null; // 当前显示的图表类型
  style: string;        // 地图样式
}
```

#### 状态管理
```typescript
function Mapbox({ workout, graph, style }: MapBoxProps): ReactElement {
  const mapContainer = React.useRef<HTMLDivElement>(null);
  const [map, setMap] = React.useState<WorkoutMap | null>(null);
  const [top, setTop] = React.useState<number>(0);
  const [isScreenshotTaken, setIsScreenshotTaken] = React.useState<boolean>(false);
  
  // 上下文集成
  const [measurementSystem] = useContext(MeasurementSystemContext);
  const { position, isPlaying, setIsPlaying, isMapInitialized, setIsMapInitialized } =
    React.useContext(PositionContext);
}
```

### 地图初始化流程

#### 地图实例创建
```typescript
React.useLayoutEffect(() => {
  if (!mapContainer.current) return;
  
  // 创建 WorkoutMap 实例
  const map = new WorkoutMap(
    mapContainer.current,
    workout,
    style,
    position,
    measurementSystem,
    makeClasses(mapClasses),
    () => setIsMapInitialized(true), // 初始化完成回调
  );
  
  // 绑定用户交互事件
  map.mapbox.on('mousedown', checkMapInteraction);
  map.mapbox.on('touchstart', checkMapInteraction);
  map.mapbox.on('zoomstart', checkMapInteraction);
  
  setMap(map);

  return () => {
    map.destructor(); // 清理资源
  };
}, []);
```

**设计特点**:
- 使用 `useLayoutEffect` 确保 DOM 准备就绪
- 自动绑定用户交互事件
- 提供清理函数防止内存泄漏

### 用户交互处理

#### 交互检测机制
```typescript
function checkMapInteraction(event: mapboxgl.MapboxEvent & mapboxgl.EventData): void {
  if (!event.automatic) setIsPlaying(false);
}
```

**交互类型**:
- **鼠标交互**: `mousedown` 事件
- **触摸交互**: `touchstart` 事件  
- **缩放交互**: `zoomstart` 事件

**行为逻辑**:
- 用户手动操作时自动暂停播放
- 区分自动操作和手动操作
- 保持播放状态的一致性

### 视差滚动效果

#### 滚动监听实现
```typescript
React.useEffect(() => {
  const onScroll = () => {
    setTop(window.scrollY * -0.25); // 视差系数 -0.25
  };
  onScroll();
  window.addEventListener('scroll', onScroll);
  return () => {
    window.removeEventListener('scroll', onScroll);
  };
}, []);
```

#### 视觉效果应用
```typescript
<div 
  ref={mapContainer} 
  className={classes.root} 
  style={{ transform: `translateY(${top}px)` }}
>
```

**效果特点**:
- 地图以页面滚动速度的 25% 向上移动
- 创造深度感和沉浸式体验
- 平滑的视差动画效果

### 响应式数据更新

#### 图表数据更新
```typescript
React.useEffect(() => {
  if (map && graph) {
    map.setMeasurementSystem(measurementSystem);
    map.setClasses(makeClasses(mapClasses));
    const labels = { max: t('TXT_MAX'), min: t('TXT_MIN') };
    map.setGraph(graph, labels);
  }
}, [graph, map, theme, measurementSystem, t]);
```

#### 样式切换处理
```typescript
React.useEffect(() => {
  if (map && graph) {
    map.setStyle(style);
    // 样式变更后需要重新设置图表
    const labels = { max: t('TXT_MAX'), min: t('TXT_MIN') };
    map.setGraph(graph, labels);
  }
}, [style, map]);
```

#### 播放位置同步
```typescript
React.useEffect(() => {
  if (isScreenshotTaken || !isPlaying) {
    map?.setPosition(position);
  }
}, [position, map, isPlaying, isScreenshotTaken]);
```

**更新策略**:
- 条件更新避免不必要的重渲染
- 截图期间暂停位置更新
- 确保数据一致性

---

## 2. CrossFadeOverlay.tsx - 交叉淡入淡出组件

### 功能概述

CrossFadeOverlay 组件实现了地图播放时的平滑过渡效果，通过截图和淡入淡出动画，消除地图跳跃感，提供流畅的视觉体验。

### 组件接口
```typescript
type CrossFadeOverlayProps = {
  classes?: Record<string, string>;
  map: WorkoutMap;                    // 地图实例
  isMapInitialized: boolean;          // 地图是否已初始化
  isPlaying: boolean;                 // 是否正在播放
  setIsScreenshotTaken: (isScreenshotTaken: boolean) => void; // 截图状态回调
};
```

### 样式定义
```typescript
const DURATION = 1200; // 动画持续时间

const useStyles = makeStyles({
  root: {
    transition: ['opacity', 'filter'].map((prop) => `${prop} ${DURATION}ms ease-in`).join(','),
    zIndex: 10,
    opacity: 1,
    filter: 'blur(0px)',
    display: 'none',
    '&$isFading': {
      opacity: 0,
      filter: 'blur(64px)', // 模糊效果增强过渡
    },
    '&$isPresent': {
      display: 'block',
    },
  },
  isFading: {},
  isPresent: {},
});
```

**视觉效果**:
- **透明度过渡**: 从 1 到 0
- **模糊过渡**: 从 0px 到 64px
- **动画时长**: 1200ms
- **缓动函数**: ease-in

### 核心逻辑实现

#### 截图和淡出流程
```typescript
React.useEffect(() => {
  if (isMapInitialized && isPlaying && canvasRef.current) {
    if (!map.isCameraAtCurrentPosition()) {
      // 相机位置需要跳跃时执行截图淡出
      getImageFromMap(map.mapbox, canvasRef.current).then(() => {
        setShowCanvas(true);
        requestAnimationFrame(() => {
          setIsFading(true);
          setIsScreenshotTaken(true);
          setTimeout(() => {
            setShowCanvas(false);
            setIsFading(false);
          }, DURATION);
        });
      });
    } else {
      // 相机位置连续时直接标记截图完成
      setIsScreenshotTaken(true);
    }
  }
  if (!isPlaying) {
    setIsScreenshotTaken(false);
  }
}, [isMapInitialized, isPlaying]);
```

**执行条件**:
1. 地图已初始化
2. 正在播放动画
3. 相机位置需要跳跃

**执行流程**:
1. 截取当前地图画面
2. 显示截图 Canvas
3. 启动淡出动画
4. 动画完成后隐藏 Canvas

### 渲染输出
```typescript
return (
  <canvas
    ref={canvasRef}
    className={classNames(classes.root, {
      [classes.isFading]: isFading,
      [classes.isPresent]: showCanvas,
    })}
  />
);
```

---

## 3. getImageFromMap.ts - 地图截图工具

### 函数签名
```typescript
export const getImageFromMap = (
  map: mapboxgl.Map, 
  canvas: HTMLCanvasElement
): Promise<void>
```

### 实现原理
```typescript
export const getImageFromMap = (map: mapboxgl.Map, canvas: HTMLCanvasElement): Promise<void> =>
  new Promise<void>((resolve, reject) => {
    map.once('render', () => {
      // 获取地图的 Canvas 元素
      const mapCanvas = map.getCanvas();
      
      // 设置目标 Canvas 尺寸
      canvas.width = mapCanvas.width;
      canvas.height = mapCanvas.height;

      // 获取 2D 绘图上下文
      const gc = canvas.getContext('2d');
      if (gc) {
        // 将地图 Canvas 内容复制到目标 Canvas
        gc.drawImage(
          mapCanvas,
          0, 0, mapCanvas.width, mapCanvas.height,  // 源区域
          0, 0, canvas.width, canvas.height         // 目标区域
        );
        resolve();
      } else {
        reject();
      }
    });
    
    // 触发地图重新渲染
    map.setBearing(map.getBearing());
  });
```

### 技术细节

#### 异步截图机制
- 监听地图的 `render` 事件
- 确保地图完全渲染后再截图
- 使用 Promise 处理异步操作

#### Canvas 操作
- 动态设置 Canvas 尺寸
- 使用 `drawImage` 复制像素数据
- 支持不同尺寸的缩放

#### 渲染触发
- 通过 `setBearing` 强制触发渲染
- 确保截图时机的准确性

---

## 组件协作关系

### 数据流向
```
Mapbox.tsx (主组件)
    ↓
WorkoutMap (底层地图类)
    ↓
CrossFadeOverlay (视觉效果)
    ↓
getImageFromMap (截图工具)
```

### 状态同步
```typescript
// 主组件状态
const [isScreenshotTaken, setIsScreenshotTaken] = useState(false);

// 传递给 CrossFadeOverlay
<CrossFadeOverlay
  setIsScreenshotTaken={setIsScreenshotTaken}
  isMapInitialized={isMapInitialized}
  isPlaying={isPlaying}
  map={map}
/>

// 影响位置更新
React.useEffect(() => {
  if (isScreenshotTaken || !isPlaying) {
    map?.setPosition(position);
  }
}, [position, map, isPlaying, isScreenshotTaken]);
```

### 生命周期管理
1. **初始化阶段**: Mapbox 创建 WorkoutMap 实例
2. **运行阶段**: CrossFadeOverlay 处理视觉效果
3. **清理阶段**: Mapbox 调用 destructor 清理资源

## 性能优化策略

### 1. 渲染优化
- 使用 `useLayoutEffect` 避免闪烁
- 条件渲染减少不必要更新
- Canvas 复用避免频繁创建

### 2. 内存管理
- 及时清理事件监听器
- 调用 destructor 释放 WebGL 资源
- 使用 WeakRef 避免循环引用

### 3. 用户体验
- 视差滚动增强沉浸感
- 平滑的淡入淡出过渡
- 智能的交互检测

## 最佳实践

### 1. 组件设计
- 单一职责原则：每个组件专注特定功能
- 依赖注入：通过 props 传递依赖
- 生命周期管理：确保资源正确清理

### 2. 状态管理
- 使用 Context 共享全局状态
- 本地状态处理组件特定逻辑
- 状态同步确保数据一致性

### 3. 性能考虑
- 避免不必要的重渲染
- 使用 requestAnimationFrame 优化动画
- 合理使用 useEffect 依赖数组

这个 Mapbox 模块展示了如何将复杂的地图功能优雅地集成到 React 应用中，提供了流畅的用户体验和优秀的性能表现。

## 深度技术分析

### 1. 地图样式系统集成

#### 样式类映射机制
```typescript
const makeClasses = (classes: MapClasses) => ({
  labelContainer: classes.labelContainer,
  labelWrapper: classes.labelWrapper,
  labelStick: classes.labelStick,
  label: classes.label,
  labelValue: classes.labelValue,
  labelTitle: classes.labelTitle,
});
```

#### 动态样式更新
```typescript
// 样式更新时的完整流程
React.useEffect(() => {
  if (map && graph) {
    // 1. 更新测量系统
    map.setMeasurementSystem(measurementSystem);

    // 2. 更新样式类
    map.setClasses(makeClasses(mapClasses));

    // 3. 重新设置图表数据
    const labels = { max: t('TXT_MAX'), min: t('TXT_MIN') };
    map.setGraph(graph, labels);
  }
}, [graph, map, theme, measurementSystem, t]);
```

**更新触发条件**:
- 图表类型变化 (`graph`)
- 主题切换 (`theme`)
- 测量系统变化 (`measurementSystem`)
- 语言切换 (`t`)

### 2. 高级动画系统

#### 视差滚动的数学原理
```typescript
const onScroll = () => {
  setTop(window.scrollY * -0.25);
};
```

**计算公式**:
```
地图偏移量 = 页面滚动距离 × (-0.25)
```

**视觉效果**:
- 页面向下滚动 100px → 地图向上移动 25px
- 创造"地图在页面后方"的深度错觉
- 负值确保地图与页面滚动方向相反

#### 交叉淡入淡出的时序控制
```typescript
getImageFromMap(map.mapbox, canvasRef.current).then(() => {
  setShowCanvas(true);                    // T+0ms: 显示截图
  requestAnimationFrame(() => {
    setIsFading(true);                    // T+16ms: 开始淡出
    setIsScreenshotTaken(true);           // T+16ms: 标记截图完成
    setTimeout(() => {
      setShowCanvas(false);               // T+1216ms: 隐藏截图
      setIsFading(false);                 // T+1216ms: 重置状态
    }, DURATION);
  });
});
```

**时序分析**:
1. **T+0ms**: 截图完成，显示 Canvas
2. **T+16ms**: 下一帧开始淡出动画
3. **T+1216ms**: 动画完成，清理状态

### 3. 内存管理和资源优化

#### WeakMap 缓存策略
```typescript
// 在实际项目中可能的缓存实现
const mapInstanceCache = new WeakMap<HTMLDivElement, WorkoutMap>();

const getOrCreateMap = (container: HTMLDivElement, workout: Workout) => {
  let map = mapInstanceCache.get(container);
  if (!map) {
    map = new WorkoutMap(container, workout, ...);
    mapInstanceCache.set(container, map);
  }
  return map;
};
```

#### 事件监听器管理
```typescript
React.useLayoutEffect(() => {
  const map = new WorkoutMap(...);

  // 绑定事件监听器
  const handlers = {
    mousedown: checkMapInteraction,
    touchstart: checkMapInteraction,
    zoomstart: checkMapInteraction,
  };

  Object.entries(handlers).forEach(([event, handler]) => {
    map.mapbox.on(event as any, handler);
  });

  return () => {
    // 清理所有事件监听器
    Object.entries(handlers).forEach(([event, handler]) => {
      map.mapbox.off(event as any, handler);
    });
    map.destructor();
  };
}, []);
```

### 4. 错误处理和降级策略

#### Canvas 上下文检查
```typescript
const gc = canvas.getContext('2d');
if (gc) {
  gc.drawImage(mapCanvas, ...);
  resolve();
} else {
  reject(new Error('Failed to get 2D context'));
}
```

#### 地图初始化失败处理
```typescript
const createMapWithFallback = (container: HTMLDivElement) => {
  try {
    return new WorkoutMap(container, workout, style, ...);
  } catch (error) {
    console.error('Map initialization failed:', error);
    // 显示静态地图或错误信息
    return createStaticMapFallback(container);
  }
};
```

### 5. 性能监控和调试

#### 渲染性能追踪
```typescript
const useRenderPerformance = (componentName: string) => {
  React.useEffect(() => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      console.log(`${componentName} render time: ${endTime - startTime}ms`);
    };
  });
};

// 在组件中使用
function Mapbox(props: MapBoxProps) {
  useRenderPerformance('Mapbox');
  // ... 组件逻辑
}
```

#### 内存使用监控
```typescript
const useMemoryMonitor = () => {
  React.useEffect(() => {
    const monitor = setInterval(() => {
      if (performance.memory) {
        console.log('Memory usage:', {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
        });
      }
    }, 5000);

    return () => clearInterval(monitor);
  }, []);
};
```

### 6. 高级用户交互

#### 手势识别增强
```typescript
const useAdvancedGestures = (map: WorkoutMap | null) => {
  React.useEffect(() => {
    if (!map) return;

    let touchStartTime = 0;
    let touchStartPos = { x: 0, y: 0 };

    const handleTouchStart = (e: TouchEvent) => {
      touchStartTime = Date.now();
      touchStartPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      };
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const touchEndTime = Date.now();
      const touchEndPos = {
        x: e.changedTouches[0].clientX,
        y: e.changedTouches[0].clientY,
      };

      const duration = touchEndTime - touchStartTime;
      const distance = Math.sqrt(
        Math.pow(touchEndPos.x - touchStartPos.x, 2) +
        Math.pow(touchEndPos.y - touchStartPos.y, 2)
      );

      // 检测快速滑动手势
      if (duration < 300 && distance > 50) {
        const direction = touchEndPos.x > touchStartPos.x ? 'right' : 'left';
        handleSwipeGesture(direction);
      }
    };

    map.mapbox.getContainer().addEventListener('touchstart', handleTouchStart);
    map.mapbox.getContainer().addEventListener('touchend', handleTouchEnd);

    return () => {
      map.mapbox.getContainer().removeEventListener('touchstart', handleTouchStart);
      map.mapbox.getContainer().removeEventListener('touchend', handleTouchEnd);
    };
  }, [map]);
};
```

### 7. 可访问性增强

#### 键盘导航支持
```typescript
const useKeyboardNavigation = (map: WorkoutMap | null, isPlaying: boolean, setIsPlaying: (playing: boolean) => void) => {
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!map) return;

      switch (e.key) {
        case ' ':
        case 'Enter':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
        case 'ArrowLeft':
          e.preventDefault();
          // 向前跳跃 5%
          map.setPosition(Math.max(0, map.getCurrentPosition() - 0.05));
          break;
        case 'ArrowRight':
          e.preventDefault();
          // 向后跳跃 5%
          map.setPosition(Math.min(1, map.getCurrentPosition() + 0.05));
          break;
        case 'Home':
          e.preventDefault();
          map.setPosition(0);
          break;
        case 'End':
          e.preventDefault();
          map.setPosition(1);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [map, isPlaying, setIsPlaying]);
};
```

#### ARIA 标签支持
```typescript
const MapboxWithA11y = (props: MapBoxProps) => {
  const mapRef = useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (mapRef.current) {
      mapRef.current.setAttribute('role', 'application');
      mapRef.current.setAttribute('aria-label', 'Interactive workout map');
      mapRef.current.setAttribute('aria-describedby', 'map-instructions');
    }
  }, []);

  return (
    <>
      <div id="map-instructions" className="sr-only">
        Use arrow keys to navigate the workout route. Press space to play/pause.
      </div>
      <div ref={mapRef}>
        <Mapbox {...props} />
      </div>
    </>
  );
};
```

### 8. 测试策略

#### 单元测试示例
```typescript
describe('CrossFadeOverlay', () => {
  it('should trigger fade animation when camera position changes', async () => {
    const mockMap = {
      isCameraAtCurrentPosition: jest.fn().mockReturnValue(false),
      mapbox: mockMapboxInstance,
    };

    const { getByRole } = render(
      <CrossFadeOverlay
        map={mockMap}
        isMapInitialized={true}
        isPlaying={true}
        setIsScreenshotTaken={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(getByRole('img')).toHaveClass('isFading');
    });
  });
});
```

#### 集成测试
```typescript
describe('Mapbox Integration', () => {
  it('should initialize map and handle user interactions', async () => {
    const workout = createMockWorkout();

    const { container } = render(
      <PositionContext.Provider value={mockPositionContext}>
        <Mapbox workout={workout} graph="HeartRate" style="satellite" />
      </PositionContext.Provider>
    );

    // 等待地图初始化
    await waitFor(() => {
      expect(container.querySelector('.mapboxgl-map')).toBeInTheDocument();
    });

    // 模拟用户交互
    fireEvent.mouseDown(container.querySelector('.mapboxgl-canvas'));

    expect(mockPositionContext.setIsPlaying).toHaveBeenCalledWith(false);
  });
});
```

这个 Mapbox 模块通过精心设计的架构和先进的技术实现，为 Suunto 地图应用提供了企业级的地图展示能力，是现代 React 地图应用开发的优秀范例。
