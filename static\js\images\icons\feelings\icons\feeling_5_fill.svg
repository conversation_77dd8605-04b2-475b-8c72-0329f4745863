var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgFeeling5Fill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M6 24C6 33.9415 14.0592 42 24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24ZM31.4099 19.8386C30.9487 18.5321 29.1114 18.4955 28.5971 19.7851L28.5634 19.8689C28.4087 20.2532 27.9718 20.4393 27.5876 20.2846C27.2033 20.13 27.0172 19.6931 27.1718 19.3089L27.2047 19.2273C28.2311 16.6535 31.902 16.7266 32.8244 19.3392C32.9623 19.7298 32.7574 20.1582 32.3669 20.2961C31.9763 20.434 31.5479 20.2292 31.4099 19.8386ZM16.5709 19.7851C17.0847 18.4956 18.9226 18.5322 19.3838 19.8386C19.5217 20.2292 19.9501 20.434 20.3407 20.2961C20.7313 20.1582 20.9361 19.7298 20.7982 19.3392C19.8758 16.7266 16.2039 16.6535 15.1784 19.2275L15.1457 19.3089C14.991 19.6931 15.1772 20.13 15.5614 20.2846C15.9457 20.4393 16.3826 20.2532 16.5372 19.8689L16.5709 19.7851ZM15.8336 26.8655C15.7754 26.5921 15.9979 26.3346 16.2785 26.3346H31.6807C31.9612 26.3346 32.1837 26.5921 32.1255 26.8655C31.3293 30.6321 27.9843 33.4705 23.9796 33.4705C19.974 33.4705 16.629 30.6321 15.8336 26.8655Z",
    fill: "#11DD55"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgFeeling5Fill);
export default __webpack_public_path__ + "static/media/feeling_5_fill.e2fcafa0.svg";
export { ForwardRef as ReactComponent };