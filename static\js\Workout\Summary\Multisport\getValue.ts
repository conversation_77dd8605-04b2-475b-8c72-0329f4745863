import { MultisportMarkerTotals } from '../../../types/WorkoutPayload';
const avgSpeed = ({ speed }: MultisportMarkerTotals) => speed?.avg;
const maxSpeed = ({ speed }: MultisportMarkerTotals) => speed?.max;
export type SummaryItemValue = string | number | null | undefined;

const unknownValue = () => undefined;

const resolveValueMap: Record<
  string,
  (multisportMarker: MultisportMarkerTotals) => SummaryItemValue
> = {
  Duration: ({ duration }) => duration,
  Distance: ({ distance }) => distance,
  AvgPace: avgSpeed,
  AvgHeartrate: ({ hr }) => hr?.avg,
  MaxHeartRate: ({ hr }) => hr?.max,
  Energy: ({ energy }) => energy,
  RecoveryTime: ({ recoveryTime }) => recoveryTime,
  Pte: unknownValue,
  PerformanceLevel: unknownValue,
  AvgSpeed: avgSpeed,
  AvgCadence: ({ cadence }) => cadence?.avg,
  Steps: unknownValue,
  AscentAltitude: ({ ascent }) => ascent,
  DescentAltitude: ({ descent }) => descent,
  HighAltitude: ({ altitude }) => altitude?.max,
  LowAltitude: ({ altitude }) => altitude?.min,
  AvgTemperature: ({ temperature }) => temperature?.avg,
  PeakEpoc: unknownValue,
  Feeling: unknownValue,
  MoveType: unknownValue,
  'Catch:Fish': unknownValue,
  'Catch:BigGame': unknownValue,
  'Catch:SmallGame': unknownValue,
  'Catch:Bird': unknownValue,
  'Catch:ShotCount': unknownValue,
  AvgPower: ({ power }) => power?.avg,
  AvgSWOLF: ({ swolf }) => swolf?.avg,
  AvgNauticalSpeed: avgSpeed,
  MaxNauticalSpeed: maxSpeed,
  NauticalDistance: ({ distance }) => distance,
  MaxSpeed: maxSpeed,
  MaxDepth: unknownValue,
  DiveTime: unknownValue,
  DiveMode: unknownValue,
  DiveNumberInSeries: unknownValue,
  DiveSurfaceTime: unknownValue,
  DiveVisibility: unknownValue,
  DiveMaxDepthTemperature: unknownValue,
  SkiRuns: unknownValue,
  SkiDistance: unknownValue,
  SkiTime: unknownValue,
  AvgSkiSpeed: unknownValue,
  MaxSkiSpeed: unknownValue,
  AscentTime: ({ ascentTime }) => ascentTime,
  DescentTime: ({ descentTime }) => descentTime,
  EstVO2peak: unknownValue,
};

export default (
  itemKey: string,
  multisportMarkerTotals: MultisportMarkerTotals | null,
): SummaryItemValue => {
  let value: SummaryItemValue;

  if (multisportMarkerTotals && resolveValueMap[itemKey]) {
    value = resolveValueMap[itemKey](multisportMarkerTotals);
    if (typeof value === 'object' && value !== null)
      throw new Error(itemKey + JSON.stringify(value));
  }

  return value;
};
