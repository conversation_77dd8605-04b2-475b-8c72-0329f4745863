import { Icon<PERSON><PERSON>on, <PERSON>lide<PERSON> } from '@mui/material';
import { useTranslation } from 'react-i18next';
import namespaces from '../../../i18n/namespaces';
import { ReactComponent as PlayIcon } from '../../../images/icons/media-and-store/play_fill.svg';
import { ReactComponent as PauseIcon } from '../../../images/icons/media-and-store/pause_fill.svg';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import LoadingIndicator from '../../../LoadingIndicator/LoadingIndicator';
import { PositionContext } from '../../usePosition';
import ScrollableSlider from './ScrollableSlider';

type MapControlProps = {
  classes?: Record<string, string>;
  useScrollableSlider: boolean;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      display: 'flex',
      alignItems: 'center',
      paddingRight: theme.spacing(2),
      paddingLeft: theme.spacing(0.5),
    },
    iconButton: {
      color: theme.palette.text.primary,
      marginRight: theme.spacing(2),
    },
    loadingIndicatorRoot: {
      position: 'absolute',
    },
    loadingIndicator: {
      width: '100%',
      height: '100%',
    },
    icon: {
      width: '2.7rem',
      height: '2.7rem',
    },
    thumb: {
      height: theme.spacing(1.2),
      width: theme.spacing(1.2),
    },
    rail: {
      backgroundColor: theme.palette.divider,
      height: 6,
      boxShadow: '1px 1px 2px 0px #0000004A inset',
      marginTop: 0,
      borderRadius: 999,
    },
  }),
  { name: 'MapControl' },
);

function MapControl(props: MapControlProps): React.ReactElement {
  const classes = useStyles(props);
  const { useScrollableSlider } = props;
  const { t } = useTranslation(namespaces.CONTROLS);
  const { position, isPlaying, setPosition, setIsPlaying, isMapInitialized } =
    React.useContext(PositionContext);

  const ActionIcon = isPlaying ? PauseIcon : PlayIcon;
  const SliderComponent = useScrollableSlider ? ScrollableSlider : Slider;
  return (
    <div className={classes.root}>
      <IconButton
        title={isPlaying ? t('PAUSE') : t('PLAY')}
        classes={{ root: classes.iconButton }}
        onClick={() => {
          if (!isPlaying && position == 1) setPosition(0);
          setIsPlaying(!isPlaying);
        }}
        size="small"
        edge="start"
      >
        <ActionIcon className={classes.icon} />
        <LoadingIndicator
          active={!isMapInitialized && isPlaying}
          classes={{
            root: classes.loadingIndicatorRoot,
            indicatorWrapper: classes.loadingIndicator,
          }}
        />
      </IconButton>
      <SliderComponent
        classes={{ thumb: classes.thumb, rail: classes.rail }}
        track={false}
        max={1}
        min={0}
        step={0.0001}
        color="body"
        onChange={(event, value) => {
          setIsPlaying(false);
          setPosition(typeof value === 'number' ? value : value[0]);
        }}
        value={position}
      />
    </div>
  );
}

export default MapControl;
