import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { DeveloperContext } from '../../../Developer/Developer';
import { Workout } from '../../../models/Workout';
import { getSummaryItemsByActivityName } from '../helpers';
import Slide from '../Slide';
import getValue from './getValue';
import { hasValue } from './helpers';
import Pagination from './Pagination';
import SummaryItem from './SummaryItem/';

type SummaryItemsProps = {
  classes?: Record<string, string>;
  activityConfig: ActivityConfig;
  workout: Workout;
};

const GRID_ITEM_PADDING = 1;
const useStyles = makeStyles((theme) => ({
  root: {},
  grid: {
    paddingTop: 10,
    display: 'flex',
    flexWrap: 'wrap',
    overflow: 'hidden',
  },
  gridItemCommon: {
    flexGrow: 1,
    width: 115,
    borderLeftWidth: 1,
    borderLeftStyle: 'solid',
    borderBottomWidth: 1,
    borderBottomStyle: 'solid',
    marginLeft: -1,
    marginBottom: -1,
  },
  gridItem: {
    padding: theme.spacing(GRID_ITEM_PADDING),
    paddingTop: 20,
    paddingBottom: 20,
    borderColor: theme.palette.divider,
  },
  placeHolder: {
    borderColor: 'transparent',
  },
  bigItem: {
    marginTop: 24,
    marginBottom: 24,
  },
}));

type nameValue = [string, any];

const BIG_ITEMS_COUNT = 3;
const INITIALLY_VISIBLE_ROWS = 2;

function SummaryItems(props: SummaryItemsProps): React.ReactElement {
  const classes = useStyles(props);
  const { activityConfig, workout } = props;
  const developer = React.useContext(DeveloperContext);
  const [visibleSmallItems, setVisibleSmallItems] = React.useState(Infinity);
  const [isAllVisible, setAllVisible] = React.useState<boolean>(false);
  const summaryItemKeys =
    getSummaryItemsByActivityName(activityConfig.Key) || getSummaryItemsByActivityName('Fallback');
  if (!summaryItemKeys) throw new Error('No summary items');
  let summaryItems: nameValue[] = summaryItemKeys.map((name) => [name, getValue(name, workout)]);
  if (!developer) {
    summaryItems = summaryItems.filter(([name, value]) => hasValue(name, value));
  }

  const summaryItemsBig = summaryItems.slice(0, BIG_ITEMS_COUNT);
  const summaryItemsSmall = summaryItems.slice(BIG_ITEMS_COUNT);
  const summaryItemsSmallFirstPart = summaryItemsSmall.slice(0, visibleSmallItems);
  const summaryItemsSmallRest = summaryItemsSmall.slice(visibleSmallItems);

  const handleRowLengthChanged = React.useCallback(
    (rowLength) => setVisibleSmallItems(rowLength * INITIALLY_VISIBLE_ROWS || Infinity),
    [],
  );

  return (
    <div className={classes.root}>
      <div>
        {summaryItemsBig.map(([name, value]) => (
          <SummaryItem key={name} value={value} name={name} classes={{ root: classes.bigItem }} />
        ))}
      </div>
      <Slide className={classes.grid} onRowLengthChanged={handleRowLengthChanged}>
        {summaryItemsSmallFirstPart.map(([name, value]) => (
          <SummaryItem
            key={name}
            value={value}
            name={name}
            small
            classes={{ root: classNames(classes.gridItemCommon, classes.gridItem) }}
          />
        ))}
        {isAllVisible &&
          summaryItemsSmallRest.map(([name, value]) => (
            <SummaryItem
              key={name}
              value={value}
              name={name}
              small
              classes={{ root: classNames(classes.gridItemCommon, classes.gridItem) }}
            />
          ))}
        {Array.from({ length: 10 }).map((v, index) => (
          <div key={index} className={classNames(classes.gridItemCommon, classes.placeHolder)} />
        ))}
      </Slide>
      {!!summaryItemsSmallRest.length && (
        <Pagination
          isAllVisible={isAllVisible}
          setAllVisible={setAllVisible}
          count={summaryItemsSmallRest.length}
        />
      )}
    </div>
  );
}

export default SummaryItems;
