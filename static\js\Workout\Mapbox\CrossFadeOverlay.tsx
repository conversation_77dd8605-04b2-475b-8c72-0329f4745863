import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { WorkoutMap } from '../WorkoutMap';
import { getImageFromMap } from './getImageFromMap';

type CrossFadeOverlayProps = {
  classes?: Record<string, string>;
  map: WorkoutMap;
  isMapInitialized: boolean;
  isPlaying: boolean;
  setIsScreenshotTaken: (isScreenshotTaken: boolean) => void;
};

const DURATION = 1200;

const useStyles = makeStyles(
  {
    root: {
      transition: ['opacity', 'filter'].map((prop) => `${prop} ${DURATION}ms ease-in`).join(','),
      zIndex: 10,
      opacity: 1,
      filter: 'blur(0px)',
      display: 'none',
      '&$isFading': {
        opacity: 0,
        filter: 'blur(64px)',
      },
      '&$isPresent': {
        display: 'block',
      },
    },
    isFading: {},
    isPresent: {},
  },
  { name: 'CrossFadeOverlay' },
);

function CrossFadeOverlay(props: CrossFadeOverlayProps): React.ReactElement | null {
  const { isMapInitialized, isPlaying, map, setIsScreenshotTaken } = props;
  const classes = useStyles(props);
  const canvasRef = React.createRef<HTMLCanvasElement>();
  const [showCanvas, setShowCanvas] = React.useState(false);
  const [isFading, setIsFading] = React.useState(false);
  React.useEffect(() => {
    if (isMapInitialized && isPlaying && canvasRef.current) {
      if (!map.isCameraAtCurrentPosition()) {
        getImageFromMap(map.mapbox, canvasRef.current).then(() => {
          setShowCanvas(true);
          requestAnimationFrame(() => {
            setIsFading(true);
            setIsScreenshotTaken(true);
            setTimeout(() => {
              setShowCanvas(false);
              setIsFading(false);
            }, DURATION);
          });
        });
      } else {
        setIsScreenshotTaken(true);
      }
    }
    if (!isPlaying) {
      setIsScreenshotTaken(false);
    }
  }, [isMapInitialized, isPlaying]);

  return (
    <canvas
      ref={canvasRef}
      className={classNames(classes.root, {
        [classes.isFading]: isFading,
        [classes.isPresent]: showCanvas,
      })}
    />
  );
}

export default CrossFadeOverlay;
