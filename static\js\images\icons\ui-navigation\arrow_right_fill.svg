var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgArrowRightFill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M21.8208 8.32279L21.9218 8.20716C22.4618 7.64891 23.3301 7.60011 23.9272 8.07076L24.0428 8.17181L40.4076 24L24.0428 39.8282C23.4474 40.4041 22.4978 40.3883 21.9218 39.7928C21.3819 39.2346 21.362 38.3651 21.8523 37.784L21.9572 37.6718L34.541 25.5L9.25 25.5C8.4703 25.5 7.82955 24.9051 7.75687 24.1445L7.75 24C7.75 23.2203 8.34489 22.5796 9.10554 22.5069L9.25 22.5L34.541 22.5L21.9572 10.3282C21.3989 9.78824 21.3501 8.91987 21.8208 8.32279L21.9218 8.20716L21.8208 8.32279Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgArrowRightFill);
export default __webpack_public_path__ + "static/media/arrow_right_fill.0af797c8.svg";
export { ForwardRef as ReactComponent };