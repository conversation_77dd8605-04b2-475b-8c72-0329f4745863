// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import sttalgUntyped from '@suunto-internal/sim_formatting';
import summaryItems from '@suunto/suunto-information-model/Specifications/SuuntoAppConfiguration/summaryItems.json';
import React from 'react';
import { useTranslation } from 'react-i18next';
import vibePhrasesUntyped from '../../../../config/vibePhrases.json';
import { DeveloperContext } from '../../../../Developer/Developer';
import namespaces from '../../../../i18n/namespaces';
import feelingIcons from '../../../../images/icons/feelings';
import {
  MeasurementSystem,
  MeasurementSystemContext,
} from '../../../../MeasurementSystem/MeasurementSystem';
import { hasValue } from '../helpers';
import Icons from '../Icons';
import SummaryItemView from './SummaryItemView';

const vibePhrases: Record<string, string> = vibePhrasesUntyped;
const sttalg: any = sttalgUntyped;

type SummaryItemProps = {
  classes?: Record<string, string>;
  name: string;
  small?: boolean;
  value: number | string | undefined | null;
};

export const convertOrigValueToSiValue = (
  name: string,
  value: number | string | undefined | null,
) => {
  let resultValue = value;
  switch (name) {
    case 'HeartRate':
    case 'AvgHeartrate':
    case 'MaxHeartrate': {
      const bpmValue = value;
      if (typeof bpmValue === 'number') {
        const { formatting } = sttalg.com.suunto.sim;
        const convertResult = formatting.Units.BPM.convert(bpmValue, formatting.Units.HZ);
        if (convertResult instanceof formatting.ConversionSuccess) {
          ({ value: resultValue } = convertResult);
        }
      }
      break;
    }
    case 'Energy': {
      const kcalValue = value;
      if (typeof kcalValue === 'number') {
        const { formatting } = sttalg.com.suunto.sim;
        const convertResult = formatting.Units.KCAL.convert(kcalValue, formatting.Units.J);
        if (convertResult instanceof formatting.ConversionSuccess) {
          ({ value: resultValue } = convertResult);
        }
      }
      break;
    }
  }
  return resultValue;
};

const getFeelingIcon = (feeling: number | string) => feelingIcons[`FEELING_${feeling}_FILL`];

const format = (
  summaryItemName: string,
  value: number | string,
  t: (key: string) => string,
  measurementSystem: MeasurementSystem,
) => {
  const formatting = sttalg.com.suunto.sim.formatting;
  const formatInstructions = summaryItems[summaryItemName];
  let formatResult;
  switch (formatInstructions) {
    case summaryItems.Feeling: {
      const Icon = getFeelingIcon(value);
      if (Icon) {
        formatResult = new formatting.FormatSuccess({}, <Icon title={t(vibePhrases[value])} />, {});
      }
      break;
    }
    default: {
      const { ValueFormat, ValueFormatStyle } = formatInstructions;
      const formatterStyleName = [ValueFormat, ValueFormatStyle].join('');

      const formattingOptions = new formatting.FormattingOptions(
        measurementSystem === MeasurementSystem.imperial
          ? formatting.MeasurementSystem.IMPERIAL
          : formatting.MeasurementSystem.METRIC,
        false,
      );
      try {
        formatResult = sttalg.com.suunto.sim.formatting.formatWithStyle(
          formatterStyleName,
          value,
          formattingOptions,
        );
      } catch (e) {
        console.error(e);
      }
    }
  }
  return formatResult;
};

function SummaryItem(props: SummaryItemProps): React.ReactElement | null {
  const { t } = useTranslation([namespaces.PHRASES, namespaces.UNITS, namespaces.TRANSLATIONS]);
  const { value, name, small = false, classes } = props;
  const [measurementSystem] = React.useContext(MeasurementSystemContext);
  const developer = React.useContext(DeveloperContext);
  const siValue = convertOrigValueToSiValue(name, value);
  const commonProps = { classes, small, name };
  if (!hasValue(name, siValue)) {
    if (developer) {
      return <SummaryItemView {...commonProps} value={value || 'unknown'} />;
    } else {
      return null;
    }
  }
  const formatResult = format(name, siValue, t, measurementSystem);
  if (!(formatResult instanceof sttalg.com.suunto.sim.formatting.FormatSuccess)) {
    return <SummaryItemView {...commonProps} value={siValue} />;
  }
  return (
    <SummaryItemView
      {...commonProps}
      Icon={Icons[`ICON_${formatResult.icon.name}`]}
      value={formatResult.value}
      unit={
        formatResult.unit.id &&
        t(`${namespaces.UNITS}:${formatResult.unit.id}`, { defaultValue: '' })
      }
    />
  );
}

export default SummaryItem;
