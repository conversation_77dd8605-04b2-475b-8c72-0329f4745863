import { CircularProgress, LinearProgress } from '@mui/material';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';

type LoadingIndicatorProps = {
  classes?: Record<string, string>;
  active?: boolean;
  linear?: boolean;
};

const useStyles = makeStyles(
  {
    root: ({ linear }: LoadingIndicatorProps) => ({
      textAlign: 'center',
      alignItems: 'center',
      justifyContent: 'center',
      display: 'flex',
      width: '100%',
      height: linear ? undefined : '100%',
      top: 0,
      left: 0,
    }),
    indicatorWrapper: ({ linear }: LoadingIndicatorProps) => ({
      width: linear ? '100%' : 40,
      height: linear ? undefined : 40,
    }),
    indicator: {},
  },
  { name: 'LoadingIndicator' },
);

const inlineStyle = { width: '100%', height: '100%' };
function LoadingIndicator(props: LoadingIndicatorProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { active = false, linear = false } = props;
  if (!active) return null;
  const Component = linear ? LinearProgress : CircularProgress;
  return (
    <div className={classes.root}>
      <div className={classes.indicatorWrapper}>
        <Component
          color="primary"
          className={classes.indicator}
          style={linear ? undefined : inlineStyle}
        />
      </div>
    </div>
  );
}

export default LoadingIndicator;
