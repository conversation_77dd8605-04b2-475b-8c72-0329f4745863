/**
 * WorkoutMap.ts - 运动轨迹地图组件
 * 
 * 这个文件实现了一个基于 Mapbox GL JS 的运动轨迹地图组件，主要功能包括：
 * - 显示运动轨迹路线
 * - 支持地形渲染（使用 MML DEM 和 Mapbox DEM 数据源）
 * - 相机路径动画和进度标记
 * - 多种地图样式支持
 * - 实时数据更新和格式化
 * - 支持不同的测量系统（公制/英制）
 * 
 * 主要特性：
 * - 高性能的地图渲染
 * - 智能地形数据源切换
 * - 流畅的相机动画
 * - 实时进度跟踪
 * - 多语言支持
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
// 导入 Suunto 内部格式化库，用于数值格式化
import sttalgUntyped from '@suunto-internal/sim_formatting';
// 导入 Mapbox GL JS 核心组件
import { Map, Marker, MercatorCoordinate, AttributionControl } from '!mapbox-gl';
// 导入 Suunto 信息模型的格式化类型定义
import FormatTypes from '@suunto/suunto-information-model/Specifications/ValuesAndFormatting/FormatTypes.json';
// 导入 Mapbox GL JS 的样式文件
import 'mapbox-gl/dist/mapbox-gl.css';
// 导入相机路径相关组件
import { CameraPath, TRACK_PROGRESS_FACTOR } from '../camera/CameraPath';
// 导入轨迹处理组件
import { Track } from '../camera/Track';
// 导入角度转换工具
import { degrees } from '../camera/Vec3';
// 导入数学工具函数
import { scale } from '../helpers/math';
// 导入运动数据模型
import { Workout, POI } from '../models/Workout';
// 导入测量系统枚举
import { MeasurementSystem } from '../MeasurementSystem/MeasurementSystem';
// 导入路线图层组件
import { LineLayer } from './LineLayer';
// 导入地图样式选择器
import { MapStyles } from './MapStyleSelector/MapStyleSelector';
// 导入数值转换工具
import { convertOrigValueToSiValue } from './Summary/SummaryItems/SummaryItem/SummaryItem';
// 导入地形变化图（用于地形夸张度计算）
import varianceMap from 'shared/dem_std_zoom_4_512.png';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
// 将无类型导入转换为 any 类型，用于后续的类型安全处理
const sttalg: any = sttalgUntyped;

/**
 * 地图标签样式类名接口
 * 定义了地图上各种标签元素的 CSS 类名
 */
export interface MapClasses {
  /** 标签容器类名 */
  labelContainer: string;
  /** 标签包装器类名 */
  labelWrapper: string;
  /** 标签连接线类名 */
  labelStick: string;
  /** 标签主体类名 */
  label: string;
  /** 标签数值类名 */
  labelValue: string;
  /** 标签标题类名 */
  labelTitle: string;
}

/** 路线图层 ID 常量 */
const ROUTE_LAYER_ID = 'route';

/**
 * MML DEM 数据源边界坐标
 * 
 * 这些坐标框完全位于芬兰境内，因为 MML 高程数据仅在芬兰境内有效，
 * 在芬兰境外提供的数据不准确，我们不希望在那里显示这些数据。
 * 
 * 每个数组包含四个值：[西南纬度, 西南经度, 东北纬度, 东北经度]
 */
const DEM_SOURCE_MML_BOUNDS_COORDINATES: number[][] = [
  [61.8237, 24.7277, 64.6913, 29.7954],
  [65.946, 23.9773, 68.5443, 28.3443],
  [64.6598, 25.5232, 66.3274, 29.3682],
  [60.9131, 21.8551, 61.907, 28.1602],
  [61.7958, 21.8698, 63.197, 24.8014],
  [63.1637, 22.7243, 63.6386, 29.9574],
  [67.313, 23.9249, 67.8967, 29.3682],
  [68.5319, 26.1015, 69.6914, 28.3149],
  [63.912, 24.1827, 64.2597, 30.1195],
  [66.2919, 28.2412, 68.0844, 28.8747],
  [63.1371, 23.6376, 64.009, 24.8309],
  [62.2176, 29.6775, 63.3692, 30.4583],
  [61.347, 28.0129, 61.9348, 29.0883],
  [68.2023, 23.2546, 68.6008, 24.6394],
  [69.0378, 28.1197, 69.7259, 28.6721],
  [62.5386, 30.3994, 63.1571, 30.9886],
  [69.6863, 26.5581, 69.8772, 28.1491],
  [68.5336, 25.3465, 68.8122, 26.1567],
  [68.8866, 21.1775, 69.2325, 21.6636],
  [68.7455, 21.5089, 69.0004, 21.9582],
  [68.5147, 22.1203, 68.6545, 22.9526],
  [69.3798, 28.6279, 69.6709, 29.0183],
  [68.4241, 22.7832, 68.6076, 23.262],
  [68.6411, 21.7888, 68.8388, 22.1866],
];

/**
 * MML DEM 数据源 URL 模板
 * 用于构建 MML 地形数据的瓦片 URL
 */
const DEM_SOURCE_MML_URL_TEMPLATE =
  'https://tileserver-test.sports-tracker.com/mml-dem/{z}/{x}/{y}.png';

/**
 * MML DEM 数据源最大缩放级别
 * 超过此级别将使用 Mapbox DEM 数据源
 */
const DEM_SOURCE_MML_MAX_ZOOM = 14;

/**
 * 运动轨迹地图类
 * 
 * 负责管理运动轨迹的地图显示，包括：
 * - 地图初始化和样式设置
 * - 轨迹路线渲染
 * - 地形数据源管理
 * - 相机路径动画
 * - 进度标记更新
 * - 数据格式化
 */
export class WorkoutMap {
  /** 当前进度标记点 */
  point: Marker | undefined;
  /** 轨迹点坐标数组 */
  pts: number[][] = [];
  /** Mapbox 地图实例 */
  mapbox: Map;
  /** 当前显示的图表类型 */
  graph: string | null = null;
  /** 最小/最大标记点数组 */
  minMaxMarkers: { marker: Marker; div: HTMLDivElement; position: number }[] = [];
  /** 运动数据对象 */
  workout: Workout;
  /** 地图样式加载完成的 Promise */
  styleLoadPromise: Promise<undefined> = Promise.resolve(void 0);
  /** 地图空闲状态的 Promise */
  idlePromise: Promise<undefined> = Promise.resolve(void 0);
  /** 位置中心标记 */
  locationCenterMarker: Marker | undefined;
  /** 相机路径对象 */
  cameraPath: CameraPath;
  /** 是否正在播放动画 */
  playing = false;
  /** 地形变化图像对象 */
  elevationImage = new Image();
  /** 地形变化图像的 Canvas 上下文 */
  elevationContext?: CanvasRenderingContext2D | null;
  /** 路线图层对象 */
  lineLayer: LineLayer | undefined;
  /** 当前使用的地形数据源 */
  demSource = '';
  /** 当前地图样式 */
  style: string | undefined;
  /** 数据更新定时器句柄 */
  updateDataIntervalHandle: number | undefined = undefined;
  /** 地图容器 DOM 元素 */
  div: HTMLDivElement;

  /**
   * 构造函数
   * 
   * @param div - 地图容器 DOM 元素
   * @param workout - 运动数据对象
   * @param style - 地图样式
   * @param position - 初始位置（0-1 之间的值）
   * @param measurementSystem - 测量系统（公制/英制）
   * @param classes - 地图标签样式类名
   * @param readyCallback - 地图准备完成的回调函数
   */
  constructor(
    div: HTMLDivElement,
    workout: Workout,
    style: string,
    private position = 0,
    private measurementSystem: MeasurementSystem,
    private classes: MapClasses,
    private readyCallback: () => void,
  ) {
    this.workout = workout;
    this.cameraPath = workout.cameraPath;
    
    // 获取默认路线数据
    const route = Workout.getRoute('', workout, { min: '', max: '' });
    this.pts = route.pts as number[][];

    this.div = div;
    this.mapbox = this.setStyle(style);

    // 初始化地形变化图像
    const img = this.elevationImage;

    img.onload = () => {
      // 创建 Canvas 来处理地形变化图像
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const gc = canvas.getContext('2d');
      if (gc) {
        gc.drawImage(img, 0, 0);
        this.elevationContext = gc;
      }
      // 等待样式加载完成后更新地形
      this.styleLoadPromise.then(() => this.updateTerrain());
    };

    img.src = varianceMap;
  }

  /**
   * 创建 Mapbox 地图实例
   * 
   * @param div - 地图容器 DOM 元素
   * @param style - 地图样式 URL
   * @returns Mapbox 地图实例
   */
  createMap(div: HTMLDivElement, style: string): Map {
    const workout = this.workout;

    // 创建地图实例，设置基本参数
    const map = new Map({
      container: div,
      style,
      center: [workout.workout.centerPosition.x, workout.workout.centerPosition.y],
      pitch: 45, // 地图倾斜角度
      bearing: 0, // 地图旋转角度
      zoom: 12, // 初始缩放级别
      interactive: true, // 启用交互
      attributionControl: false, // 禁用默认的归属控件
      logoPosition: 'top-right', // Logo 位置
    });

    // 获取位置数据并计算边界
    const locations = workout.getLocation();
    const points = locations ? locations.locationPoints : [];

    if (points.length > 1) {
      const bounds = Track.getBounds(points);
      const lonExtent = bounds.lonMax - bounds.lonMin;
      const latExtent = bounds.latMax - bounds.latMin;
      const margin = 1 / 6; // 边界边距
      
      // 调整地图边界以包含所有轨迹点
      map.fitBounds([
        [bounds.lonMin - lonExtent * margin, bounds.latMin - latExtent * margin],
        [bounds.lonMax + lonExtent * margin, bounds.latMax + latExtent * margin],
      ]);
    }

    // 设置样式加载和空闲状态的 Promise
    this.styleLoadPromise = new Promise((resolve) => map.on('style.load', resolve));
    this.idlePromise = new Promise((resolve) =>
      this.styleLoadPromise.then(() => map.once('idle', resolve)),
    );

    // 地图空闲时更新地形
    map.on('idle', () => this.updateTerrain());

    // 添加归属控件
    map.addControl(new AttributionControl(), 'top-right');
    
    // 样式加载完成后添加地形数据源
    this.styleLoadPromise.then(() => {
      // 添加 MML DEM 数据源（芬兰地区的高精度地形数据）
      map.addSource('mml-dem', {
        type: 'raster-dem',
        tiles: [DEM_SOURCE_MML_URL_TEMPLATE],
        tileSize: 512,
        maxzoom: DEM_SOURCE_MML_MAX_ZOOM,
        encoding: 'mapbox',
      });

      // 添加 Mapbox DEM 数据源（全球地形数据）
      map.addSource('mapbox-dem', {
        type: 'raster-dem',
        url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
        tileSize: 512,
        maxzoom: 14,
      });

      this.updateTerrain();

      // 设置数据更新定时器（每 100ms 更新一次）
      this.updateDataIntervalHandle = window.setInterval(() => {
        if (this.lineLayer) this.lineLayer.updateData();
      }, 100);

      // 移动进度标记到初始位置
      this.moveProgressMarker([
        workout.workout.centerPosition.x,
        workout.workout.centerPosition.y,
        0,
      ]);
    });

    return map;
  }

  /**
   * 析构函数，清理资源
   */
  destructor(): void {
    window.clearInterval(this.updateDataIntervalHandle);
  }

  /**
   * 更新地形渲染
   * 
   * 根据当前地图中心位置和缩放级别，智能选择地形数据源，
   * 并计算地形夸张度以获得更好的视觉效果
   */
  updateTerrain(): void {
    const center = this.mapbox.getCenter();
    let exaggeration = 1.35; // 默认地形夸张度
    let source = 'mapbox-dem'; // 默认使用 Mapbox DEM

    // 在缩放级别 >= 10 时，检查是否在芬兰境内
    if (this.mapbox.getZoom() >= 10) {
      for (const box of DEM_SOURCE_MML_BOUNDS_COORDINATES) {
        if (
          center.lat >= box[0] &&
          center.lat <= box[2] &&
          center.lng >= box[1] &&
          center.lng <= box[3]
        ) {
          source = 'mml-dem'; // 在芬兰境内使用 MML DEM
          break;
        }
      }
    }

    // 根据地形变化图像调整夸张度
    if (this.elevationContext) {
      // 获取地形变化图像的宽度，用于坐标转换
      // 这个图像是一个预加载的 PNG 文件，包含了全球地形的变化程度信息
      // 图像中每个像素的蓝色通道值表示该位置的地形变化程度
      const size = this.elevationImage.width;
      
      // 将当前地图中心的地理坐标（经度、纬度）转换为墨卡托投影坐标
      // 墨卡托投影是一种常用的地图投影方式，将球面坐标转换为平面坐标
      // xy.x 和 xy.y 的值范围是 0-1，表示在地图投影中的相对位置
      const xy = MercatorCoordinate.fromLngLat(center);

      // 从地形变化图像中获取当前位置的地形变化值
      // 这个过程分为几个步骤：
      // 1. xy.x * size：将墨卡托坐标转换为图像像素的 X 坐标
      // 2. xy.y * size：将墨卡托坐标转换为图像像素的 Y 坐标
      // 3. getImageData(x, y, 1, 1)：获取指定像素位置的图像数据
      //    - 参数1：像素 X 坐标
      //    - 参数2：像素 Y 坐标
      //    - 参数3：获取的宽度（1个像素）
      //    - 参数4：获取的高度（1个像素）
      // 4. .data[2]：获取像素的蓝色通道值（RGB 中的 B）
      //    - data[0] = 红色通道值 (0-255)
      //    - data[1] = 绿色通道值 (0-255)
      //    - data[2] = 蓝色通道值 (0-255) ← 我们使用这个
      //    - data[3] = 透明度通道值 (0-255)
      // 5. / 255：将 0-255 的整数值转换为 0-1 的小数值
      const variance =
        this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1).data[2] / 255;
      
      // 根据地形变化值计算地形夸张度
      // 夸张度计算公式：exaggeration = 1 + (1 - variance) * 0.7
      // 
      // 这个公式的逻辑是：
      // - variance = 0（地形变化最大，如高山）：exaggeration = 1 + (1-0) * 0.7 = 1.7（最大夸张）
      // - variance = 0.5（中等地形变化，如丘陵）：exaggeration = 1 + (1-0.5) * 0.7 = 1.35（中等夸张）
      // - variance = 1（地形变化最小，如平原）：exaggeration = 1 + (1-1) * 0.7 = 1.0（无夸张）
      //
      // 为什么要这样计算？
      // 1. 地形变化大的地方（山地）需要更大的夸张度来突出地形特征
      // 2. 地形变化小的地方（平原）需要较小的夸张度来保持真实性
      // 3. 0.7 是经过测试的最佳系数，在视觉效果和真实性之间取得平衡
      exaggeration = 1 + (1 - variance) * 0.7;
    }

    // 缩放因子（当前设为 0，等待 Mapbox 修复相关问题）
    // 相关 issue: https://github.com/mapbox/mapbox-gl-js/issues/11044
    const zoomFactor = 0;

    // 如果数据源发生变化，更新地形设置
    if (source != this.demSource) {
      this.mapbox.setTerrain({
        source,
        exaggeration: [
          'interpolate', // 插值函数
          ['linear'], // 线性插值
          ['zoom'], // 基于缩放级别
          8, // 缩放级别 8
          exaggeration + zoomFactor, // 对应的夸张度
          12, // 缩放级别 12
          exaggeration, // 对应的夸张度
        ],
      });
      this.demSource = source;
    }
  }

  /**
   * 设置地图标签样式类名
   * 
   * @param classes - 新的样式类名对象
   */
  setClasses(classes: MapClasses): void {
    this.classes = classes;
  }

  /**
   * 设置地图样式
   * 
   * @param style - 地图样式名称或 URL
   * @returns Mapbox 地图实例
   */
  setStyle(style?: string): Map {
    // 处理不同的样式类型
    if (style == MapStyles.satellite || !style) {
      // 卫星图样式
      style = 'mapbox://styles/asdigital/ckw3gar2y19y614p40q2bz53a';
    } else {
      // 自定义样式，根据环境添加后缀
      style = process.env.REACT_APP_API_URL + '/api/style/' + style;
      if (process.env.REACT_APP_MAP_STYLES_ENV == 'china') style += 'CN';
    }

    // 如果样式没有变化，直接返回当前地图实例
    if (style == this.style) return this.mapbox;
    this.style = style;
    this.demSource = '';
    this.point = void 0;

    // 移除旧地图实例
    if (this.mapbox) this.mapbox.remove();

    // 创建新地图实例
    const map = this.createMap(this.div, style);
    this.mapbox = map;

    return map;
  }

  /**
   * 设置图表类型和标签
   * 
   * @param newGraph - 新的图表类型
   * @param labels - 标签的最小值和最大值
   */
  setGraph(newGraph: string, labels: { min: string; max: string }): void {
    this.graph = newGraph;
    
    // 清理旧的标记
    this.locationCenterMarker?.remove();
    this.locationCenterMarker = undefined;
    this.minMaxMarkers.forEach(({ marker }) => marker.remove());

    // 等待地图空闲后更新路线
    this.idlePromise.then(() => {
      // 获取新的路线数据
      const route = Workout.getRoute(newGraph, this.workout, labels);
      const poi = route.poi; // 兴趣点
      const pts = route.pts as number[][];
      
      // 添加兴趣点标记
      for (const pt of poi) {
        this.addMarker(pt);
      }
      this.pts = pts;
      
      // 创建新的路线图层
      const layer = new LineLayer(ROUTE_LAYER_ID, pts);
      const oldLayer = this.mapbox.getLayer(ROUTE_LAYER_ID);
      if (oldLayer) {
        this.mapbox.removeLayer(ROUTE_LAYER_ID);
      }
      this.lineLayer = layer;
      
      // 移动进度标记到新位置
      this.moveProgressMarker(this.getProgressMarkerPositionByLineString());

      // 获取地图样式中的图层
      const layers = this.mapbox.getStyle().layers;
      let idAbove: string | undefined;

      // 找到第一个符号图层作为插入位置
      if (layers) {
        for (const other of layers) {
          if (other.type == 'symbol') {
            idAbove = other.id;
            break;
          }
        }
      }

      // 添加路线图层
      this.mapbox.addLayer(layer, idAbove);
      this.mapbox.once('idle', () => {
        layer.updateData(true);
        this.readyCallback();
      });
    });
  }

  /**
   * 设置测量系统
   * 
   * @param measurementSystem - 测量系统（公制/英制）
   */
  setMeasurementSystem(measurementSystem: MeasurementSystem): void {
    this.measurementSystem = measurementSystem;
  }

  /**
   * 格式化数值显示
   * 
   * 根据当前图表类型和测量系统，将数值格式化为用户友好的字符串
   * 
   * @param value - 要格式化的数值
   * @returns 格式化后的字符串
   */
  formatValue(value: number): string {
    if (!this.graph) {
      return value.toString();
    }
    
    // 获取格式化样式
    const formatStyle = FormatTypes[this.graph]?.DefaultStyle;

    if (formatStyle) {
      const formatting = sttalg.com.suunto.sim.formatting;
      // 创建格式化选项
      const formattingOptions = new formatting.FormattingOptions(
        this.measurementSystem === MeasurementSystem.imperial
          ? formatting.MeasurementSystem.IMPERIAL
          : formatting.MeasurementSystem.METRIC,
        false,
      );
      
      // 转换数值到 SI 单位
      if (typeof value == 'number') {
        value = convertOrigValueToSiValue(this.graph, value) as number;
      }
      
      // 格式化数值
      const formatterStyleName = [this.graph, formatStyle].join('');
      const formatResult = sttalg.com.suunto.sim.formatting.formatWithStyle(
        formatterStyleName,
        value,
        formattingOptions,
      );

      if (formatResult instanceof formatting.FormatSuccess) {
        return formatResult.value.toString();
      }
    }
    return value.toString();
  }

  /**
   * 添加兴趣点标记
   * 
   * @param pt - 兴趣点数据
   * @returns 创建的标记对象
   */
  addMarker(pt: POI): Marker {
    const classes = this.classes;

    // 创建标记的 DOM 结构
    const labelContainer = document.createElement('div');
    labelContainer.className = classes.labelContainer;

    const labelWrapper = document.createElement('div');
    labelWrapper.className = classes.labelWrapper;
    labelContainer.appendChild(labelWrapper);

    const labelStick = document.createElement('div');
    labelStick.className = classes.labelStick;
    labelWrapper.appendChild(labelStick);

    const label = document.createElement('div');
    label.className = classes.label;
    labelWrapper.appendChild(label);

    const labelTitle = document.createElement('label');
    labelTitle.className = classes.labelTitle;
    labelTitle.innerText = pt.label;
    label.appendChild(labelTitle);

    const labelValue = document.createElement('div');
    labelValue.className = classes.labelValue;
    labelValue.innerText = this.formatValue(pt.value);
    label.appendChild(labelValue);
    
    // 创建并添加标记到地图
    const marker = new Marker(labelContainer).setLngLat([pt.lon, pt.lat]).addTo(this.mapbox);

    // 保存标记信息
    this.minMaxMarkers.push({ marker, div: labelWrapper, position: pt.position });
    return marker;
  }

  /**
   * 设置播放状态
   * 
   * @param playing - 是否正在播放
   */
  setPlaying(playing: boolean): void {
    this.playing = playing;
  }

  /**
   * 根据轨迹进度计算进度标记位置
   * 
   * 根据当前进度在轨迹线上插值计算标记的精确位置
   * 
   * @returns 进度标记的坐标 [经度, 纬度, 距离]
   */
  getProgressMarkerPositionByLineString(): [number, number, number] {
    const pts = this.pts;
    const ptLast = pts[pts.length - 1];
    // 计算轨迹进度（0-1）
    const trackProgress = Math.min(1.0, TRACK_PROGRESS_FACTOR * this.position);
    // 将进度转换为距离
    const kmPosition = scale(trackProgress, [0, 1], [0, ptLast[4]]);
    let ptPrev = pts[0];

    // 在轨迹点中查找当前位置
    for (const pt of pts) {
      const km = pt[4];
      const kmPrev = ptPrev[4];
      if (km >= kmPosition) {
        // 在两个轨迹点之间进行线性插值
        const posAlongLine = (kmPosition - kmPrev) / (km - kmPrev || 1);
        return [
          ptPrev[1] + (pt[1] - ptPrev[1]) * posAlongLine, // 经度
          ptPrev[0] + (pt[0] - ptPrev[0]) * posAlongLine, // 纬度
          kmPosition, // 距离
        ];
      }

      ptPrev = pt;
    }

    // 如果超出轨迹范围，返回最后一个点
    return [ptLast[1], ptLast[0], kmPosition];
  }

  /**
   * 移动进度标记到指定位置
   * 
   * @param coords - 目标坐标 [经度, 纬度, 距离]
   */
  moveProgressMarker(coords: [number, number, number]): void {
    // 创建或更新进度标记
    if (!this.point) {
      this.point = new Marker().setLngLat([coords[0], coords[1]]).addTo(this.mapbox);
    } else {
      this.point.setLngLat([coords[0], coords[1]]);
    }

    // 更新兴趣点标记的可见性
    for (const { div, position } of this.minMaxMarkers) {
      div.style.opacity = position <= coords[2] || !this.lineLayer?.progressed ? '1' : '0';
    }

    // 更新路线图层的进度
    if (this.lineLayer) this.lineLayer.setPosition(coords[2]);
  }

  /**
   * 计算新的相机位置
   * 
   * 根据当前进度和相机路径计算相机的精确位置、角度和俯仰角
   * 
   * @returns 包含相机位置、角度、俯仰角和标记坐标的对象
   */
  private calculateNewPosition() {
    const cameraPath = this.cameraPath;
    const path = cameraPath.cameraPath;
    const position = this.position;
    const markerCoordinates = this.getProgressMarkerPositionByLineString();

    // 查询标记位置的地形高程
    const markerElevation = this.mapbox.queryTerrainElevation(
      { lng: markerCoordinates[0], lat: markerCoordinates[1] },
      { exaggerated: true },
    );
    
    // 计算相机进度
    const cameraProgress = cameraPath.cameraEasing(position);
    const canvas = this.mapbox.getCanvas();

    // 计算居中行为（基于画布宽高比）
    let centeringBehaviour = scale(
      canvas.width / canvas.height,
      [1.25, 0.5],
      [cameraPath.lookAtPointCenteringBehaviour.getValue(cameraProgress), 1],
    );
    if (centeringBehaviour < 0) centeringBehaviour = 0;
    if (centeringBehaviour > 1) centeringBehaviour = 1;
    
    // 计算相机角度
    const a = cameraPath.lookAtCurve.getPoint(cameraProgress);
    const b = path.getPoint(cameraProgress);
    const c = b.subtract(a).normalize();
    let bearing = 180 + degrees(Math.atan2(c.x, c.y));
    
    // 如果有地形高程数据，调整角度
    if (markerElevation !== null) {
      const a2 = cameraPath.narrowLookAtCurve.getPoint(cameraProgress);
      const c2 = b.subtract(a2).normalize();
      let bearing2 = 180 + degrees(Math.atan2(c2.x, c2.y));
      if (bearing2 > bearing + 90) bearing2 -= 360;
      if (bearing2 < bearing - 90) bearing2 += 360;
      bearing = scale(centeringBehaviour, [0, 1], [bearing, bearing2]);
    }
    
    // 计算相机位置和俯仰角
    const cameraPosition = cameraPath.getLatLonAltFromPseudoCartesianCoordinates(b);
    const pitch = degrees(Math.atan(Math.sqrt(c.x * c.x + c.y * c.y) / c.z));

    return { bearing, pitch, cameraPosition, markerCoordinates };
  }

  /**
   * 检查相机是否在当前位置
   * 
   * 用于优化性能，避免不必要的相机更新
   * 
   * @returns 如果相机位置接近目标位置则返回 true
   */
  isCameraAtCurrentPosition(): boolean {
    const { cameraPosition } = this.calculateNewPosition();
    if (!cameraPosition) return false;

    // 将相机位置转换为墨卡托坐标
    const xyz = MercatorCoordinate.fromLngLat(
      {
        lng: cameraPosition.lon,
        lat: cameraPosition.lat,
      },
      cameraPosition.alt,
    );

    // 获取当前相机位置
    const { position: xyzOld } = this.mapbox.getFreeCameraOptions();
    if (!xyzOld) return false;

    // 计算位置差异
    const dx = xyz.x - xyzOld.x;
    const dy = xyz.y - xyzOld.y;

    // 根据缩放级别计算容差
    return dx * dx + dy * dy < Math.pow(2, -14 - this.mapbox.getZoom());
  }

  /**
   * 设置当前进度位置
   * 
   * 更新地图的进度位置，包括进度标记和相机位置
   * 
   * @param position - 新的进度位置（0-1 之间的值）
   */
  setPosition(position: number): void {
    this.position = position;

    // 计算新的相机位置
    const { cameraPosition, bearing, pitch, markerCoordinates } = this.calculateNewPosition();
    if (cameraPosition && bearing && pitch && markerCoordinates) {
      // 移动进度标记
      this.moveProgressMarker(markerCoordinates);

      // 更新相机位置和角度
      const camera = this.mapbox.getFreeCameraOptions();
      camera.position = MercatorCoordinate.fromLngLat(
        {
          lng: cameraPosition.lon,
          lat: cameraPosition.lat,
        },
        cameraPosition.alt,
      );
      camera.setPitchBearing(pitch, bearing);
      this.mapbox.setFreeCameraOptions(camera, { automatic: true });
    }
  }
}
