// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import sttalgUntyped from '@suunto-internal/sim_formatting';
import { Map, Marker, MercatorCoordinate, AttributionControl } from '!mapbox-gl';
import FormatTypes from '@suunto/suunto-information-model/Specifications/ValuesAndFormatting/FormatTypes.json';
import 'mapbox-gl/dist/mapbox-gl.css';
import { CameraPath, TRACK_PROGRESS_FACTOR } from '../camera/CameraPath';
import { Track } from '../camera/Track';
import { degrees } from '../camera/Vec3';
import { scale } from '../helpers/math';
import { Workout, POI } from '../models/Workout';
import { MeasurementSystem } from '../MeasurementSystem/MeasurementSystem';
import { LineLayer } from './LineLayer';
import { MapStyles } from './MapStyleSelector/MapStyleSelector';
import { convertOrigValueToSiValue } from './Summary/SummaryItems/SummaryItem/SummaryItem';
import varianceMap from 'shared/dem_std_zoom_4_512.png';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const sttalg: any = sttalgUntyped;
export interface MapClasses {
  labelContainer: string;
  labelWrapper: string;
  labelStick: string;
  label: string;
  labelValue: string;
  labelTitle: string;
}
const ROUTE_LAYER_ID = 'route';

// Coordinates of boxes covering MML DEM source bounds.
// All of the boxes are completely inside the borders of Finland,
// because MML elevations are provided but ridiculous outside Finland
// and we don't want to show them there.

const DEM_SOURCE_MML_URL_TEMPLATE =
  'https://tileserver-test.sports-tracker.com/mml-dem/{z}/{x}/{y}.png';
// const DEM_SOURCE_MML_TILE_SIZE = 512;
// const DEM_SOURCE_MML_MIN_ZOOM = 11;
const DEM_SOURCE_MML_MAX_ZOOM = 14;

// SW lat, SW lng, NE lat, NE lng
const DEM_SOURCE_MML_BOUNDS_COORDINATES: number[][] = [
  [61.8237, 24.7277, 64.6913, 29.7954],
  [65.946, 23.9773, 68.5443, 28.3443],
  [64.6598, 25.5232, 66.3274, 29.3682],
  [60.9131, 21.8551, 61.907, 28.1602],
  [61.7958, 21.8698, 63.197, 24.8014],
  [63.1637, 22.7243, 63.6386, 29.9574],
  [67.313, 23.9249, 67.8967, 29.3682],
  [68.5319, 26.1015, 69.6914, 28.3149],
  [63.912, 24.1827, 64.2597, 30.1195],
  [66.2919, 28.2412, 68.0844, 28.8747],
  [63.1371, 23.6376, 64.009, 24.8309],
  [62.2176, 29.6775, 63.3692, 30.4583],
  [61.347, 28.0129, 61.9348, 29.0883],
  [68.2023, 23.2546, 68.6008, 24.6394],
  [69.0378, 28.1197, 69.7259, 28.6721],
  [62.5386, 30.3994, 63.1571, 30.9886],
  [69.6863, 26.5581, 69.8772, 28.1491],
  [68.5336, 25.3465, 68.8122, 26.1567],
  [68.8866, 21.1775, 69.2325, 21.6636],
  [68.7455, 21.5089, 69.0004, 21.9582],
  [68.5147, 22.1203, 68.6545, 22.9526],
  [69.3798, 28.6279, 69.6709, 29.0183],
  [68.4241, 22.7832, 68.6076, 23.262],
  [68.6411, 21.7888, 68.8388, 22.1866],
];

export class WorkoutMap {
  point: Marker | undefined;
  pts: number[][] = [];
  mapbox: Map;
  graph: string | null = null;
  minMaxMarkers: { marker: Marker; div: HTMLDivElement; position: number }[] = [];
  workout: Workout;
  styleLoadPromise: Promise<undefined> = Promise.resolve(void 0);
  idlePromise: Promise<undefined> = Promise.resolve(void 0);
  locationCenterMarker: Marker | undefined;
  cameraPath: CameraPath;
  playing = false;
  elevationImage = new Image();
  elevationContext?: CanvasRenderingContext2D | null;
  lineLayer: LineLayer | undefined;
  demSource = '';
  style: string | undefined;
  updateDataIntervalHandle: number | undefined = undefined;
  div: HTMLDivElement;

  constructor(
    div: HTMLDivElement,
    workout: Workout,
    style: string,
    private position = 0,
    private measurementSystem: MeasurementSystem,
    private classes: MapClasses,
    private readyCallback: () => void,
  ) {
    this.workout = workout;
    this.cameraPath = workout.cameraPath;
    const route = Workout.getRoute('', workout, { min: '', max: '' });
    this.pts = route.pts as number[][];

    this.div = div;
    this.mapbox = this.setStyle(style);

    const img = this.elevationImage;

    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const gc = canvas.getContext('2d');
      if (gc) {
        gc.drawImage(img, 0, 0);
        this.elevationContext = gc;
      }
      this.styleLoadPromise.then(() => this.updateTerrain());
    };

    img.src = varianceMap;
  }

  createMap(div: HTMLDivElement, style: string): Map {
    const workout = this.workout;

    const map = new Map({
      container: div,
      style,
      center: [workout.workout.centerPosition.x, workout.workout.centerPosition.y],
      pitch: 45,
      bearing: 0,
      zoom: 12,
      interactive: true,
      attributionControl: false,
      logoPosition: 'top-right',
    });

    const locations = workout.getLocation();
    const points = locations ? locations.locationPoints : [];

    if (points.length > 1) {
      const bounds = Track.getBounds(points);
      const lonExtent = bounds.lonMax - bounds.lonMin;
      const latExtent = bounds.latMax - bounds.latMin;
      const margin = 1 / 6;
      map.fitBounds([
        [bounds.lonMin - lonExtent * margin, bounds.latMin - latExtent * margin],
        [bounds.lonMax + lonExtent * margin, bounds.latMax + latExtent * margin],
      ]);
    }

    this.styleLoadPromise = new Promise((resolve) => map.on('style.load', resolve));
    this.idlePromise = new Promise((resolve) =>
      this.styleLoadPromise.then(() => map.once('idle', resolve)),
    );

    map.on('idle', () => this.updateTerrain());

    map.addControl(new AttributionControl(), 'top-right');
    this.styleLoadPromise.then(() => {
      map.addSource('mml-dem', {
        type: 'raster-dem',
        tiles: [DEM_SOURCE_MML_URL_TEMPLATE],
        tileSize: 512,
        maxzoom: DEM_SOURCE_MML_MAX_ZOOM,
        encoding: 'mapbox',
      });

      map.addSource('mapbox-dem', {
        type: 'raster-dem',
        url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
        tileSize: 512,
        maxzoom: 14,
      });

      this.updateTerrain();

      this.updateDataIntervalHandle = window.setInterval(() => {
        if (this.lineLayer) this.lineLayer.updateData();
      }, 100);

      this.moveProgressMarker([
        workout.workout.centerPosition.x,
        workout.workout.centerPosition.y,
        0,
      ]);
    });

    return map;
  }

  destructor(): void {
    window.clearInterval(this.updateDataIntervalHandle);
  }

  updateTerrain(): void {
    const center = this.mapbox.getCenter();
    let exaggeration = 1.35;
    let source = 'mapbox-dem';

    if (this.mapbox.getZoom() >= 10) {
      for (const box of DEM_SOURCE_MML_BOUNDS_COORDINATES) {
        if (
          center.lat >= box[0] &&
          center.lat <= box[2] &&
          center.lng >= box[1] &&
          center.lng <= box[3]
        ) {
          source = 'mml-dem';
          break;
        }
      }
    }

    if (this.elevationContext) {
      const size = this.elevationImage.width;
      const xy = MercatorCoordinate.fromLngLat(center);

      const variance =
        this.elevationContext.getImageData(xy.x * size, xy.y * size, 1, 1).data[2] / 255;
      exaggeration = 1 + (1 - variance) * 0.7;
    }

    // Change this to 0.5 once the following issue is fixed:
    // https://github.com/mapbox/mapbox-gl-js/issues/11044
    const zoomFactor = 0;

    if (source != this.demSource) {
      this.mapbox.setTerrain({
        source,
        exaggeration: [
          'interpolate', //
          ['linear'],
          ['zoom'],
          8,
          exaggeration + zoomFactor,
          12,
          exaggeration,
        ],
      });
      this.demSource = source;
    }
  }

  setClasses(classes: MapClasses): void {
    this.classes = classes;
  }

  setStyle(style?: string): Map {
    if (style == MapStyles.satellite || !style) {
      style = 'mapbox://styles/asdigital/ckw3gar2y19y614p40q2bz53a';
    } else {
      style = process.env.REACT_APP_API_URL + '/api/style/' + style;
      if (process.env.REACT_APP_MAP_STYLES_ENV == 'china') style += 'CN';
    }

    if (style == this.style) return this.mapbox;
    this.style = style;
    this.demSource = '';
    this.point = void 0;

    if (this.mapbox) this.mapbox.remove();

    const map = this.createMap(this.div, style);
    this.mapbox = map;

    return map;
  }

  setGraph(newGraph: string, labels: { min: string; max: string }): void {
    this.graph = newGraph;
    this.locationCenterMarker?.remove();
    this.locationCenterMarker = undefined;
    this.minMaxMarkers.forEach(({ marker }) => marker.remove());

    this.idlePromise.then(() => {
      const route = Workout.getRoute(newGraph, this.workout, labels);
      const poi = route.poi;
      const pts = route.pts as number[][];
      for (const pt of poi) {
        this.addMarker(pt);
      }
      this.pts = pts;
      const layer = new LineLayer(ROUTE_LAYER_ID, pts);
      const oldLayer = this.mapbox.getLayer(ROUTE_LAYER_ID);
      if (oldLayer) {
        this.mapbox.removeLayer(ROUTE_LAYER_ID);
      }
      this.lineLayer = layer;
      this.moveProgressMarker(this.getProgressMarkerPositionByLineString());

      const layers = this.mapbox.getStyle().layers;
      let idAbove: string | undefined;

      if (layers) {
        for (const other of layers) {
          if (other.type == 'symbol') {
            idAbove = other.id;
            break;
          }
        }
      }

      this.mapbox.addLayer(layer, idAbove);
      this.mapbox.once('idle', () => {
        layer.updateData(true);
        this.readyCallback();
      });
    });
  }

  setMeasurementSystem(measurementSystem: MeasurementSystem): void {
    this.measurementSystem = measurementSystem;
  }

  formatValue(value: number): string {
    if (!this.graph) {
      return value.toString();
    }
    const formatStyle = FormatTypes[this.graph]?.DefaultStyle;

    if (formatStyle) {
      const formatting = sttalg.com.suunto.sim.formatting;
      const formattingOptions = new formatting.FormattingOptions(
        this.measurementSystem === MeasurementSystem.imperial
          ? formatting.MeasurementSystem.IMPERIAL
          : formatting.MeasurementSystem.METRIC,
        false,
      );
      if (typeof value == 'number') {
        value = convertOrigValueToSiValue(this.graph, value) as number;
      }
      const formatterStyleName = [this.graph, formatStyle].join('');
      const formatResult = sttalg.com.suunto.sim.formatting.formatWithStyle(
        formatterStyleName,
        value,
        formattingOptions,
      );

      if (formatResult instanceof formatting.FormatSuccess) {
        return formatResult.value.toString();
      }
    }
    return value.toString();
  }

  addMarker(pt: POI): Marker {
    const classes = this.classes;

    const labelContainer = document.createElement('div');
    labelContainer.className = classes.labelContainer;

    const labelWrapper = document.createElement('div');
    labelWrapper.className = classes.labelWrapper;
    labelContainer.appendChild(labelWrapper);

    const labelStick = document.createElement('div');
    labelStick.className = classes.labelStick;
    labelWrapper.appendChild(labelStick);

    const label = document.createElement('div');
    label.className = classes.label;
    labelWrapper.appendChild(label);

    const labelTitle = document.createElement('label');
    labelTitle.className = classes.labelTitle;
    labelTitle.innerText = pt.label;
    label.appendChild(labelTitle);

    const labelValue = document.createElement('div');
    labelValue.className = classes.labelValue;
    labelValue.innerText = this.formatValue(pt.value);
    label.appendChild(labelValue);
    const marker = new Marker(labelContainer).setLngLat([pt.lon, pt.lat]).addTo(this.mapbox);

    this.minMaxMarkers.push({ marker, div: labelWrapper, position: pt.position });
    return marker;
  }

  setPlaying(playing: boolean): void {
    this.playing = playing;
  }

  getProgressMarkerPositionByLineString(): [number, number, number] {
    const pts = this.pts;
    const ptLast = pts[pts.length - 1];
    const trackProgress = Math.min(1.0, TRACK_PROGRESS_FACTOR * this.position);
    const kmPosition = scale(trackProgress, [0, 1], [0, ptLast[4]]);
    let ptPrev = pts[0];

    for (const pt of pts) {
      const km = pt[4];
      const kmPrev = ptPrev[4];
      if (km >= kmPosition) {
        const posAlongLine = (kmPosition - kmPrev) / (km - kmPrev || 1);
        return [
          ptPrev[1] + (pt[1] - ptPrev[1]) * posAlongLine,
          ptPrev[0] + (pt[0] - ptPrev[0]) * posAlongLine,
          kmPosition,
        ];
      }

      ptPrev = pt;
    }

    return [ptLast[1], ptLast[0], kmPosition];
  }

  moveProgressMarker(coords: [number, number, number]): void {
    if (!this.point) {
      this.point = new Marker().setLngLat([coords[0], coords[1]]).addTo(this.mapbox);
    } else {
      this.point.setLngLat([coords[0], coords[1]]);
    }

    for (const { div, position } of this.minMaxMarkers) {
      div.style.opacity = position <= coords[2] || !this.lineLayer?.progressed ? '1' : '0';
    }

    if (this.lineLayer) this.lineLayer.setPosition(coords[2]);
  }

  private calculateNewPosition() {
    const cameraPath = this.cameraPath;
    const path = cameraPath.cameraPath;
    const position = this.position;
    const markerCoordinates = this.getProgressMarkerPositionByLineString();

    const markerElevation = this.mapbox.queryTerrainElevation(
      { lng: markerCoordinates[0], lat: markerCoordinates[1] },
      { exaggerated: true },
    );
    const cameraProgress = cameraPath.cameraEasing(position);
    const canvas = this.mapbox.getCanvas();

    let centeringBehaviour = scale(
      canvas.width / canvas.height,
      [1.25, 0.5],
      [cameraPath.lookAtPointCenteringBehaviour.getValue(cameraProgress), 1],
    );
    if (centeringBehaviour < 0) centeringBehaviour = 0;
    if (centeringBehaviour > 1) centeringBehaviour = 1;
    const a = cameraPath.lookAtCurve.getPoint(cameraProgress);
    const b = path.getPoint(cameraProgress);
    const c = b.subtract(a).normalize();
    let bearing = 180 + degrees(Math.atan2(c.x, c.y));
    if (markerElevation !== null) {
      const a2 = cameraPath.narrowLookAtCurve.getPoint(cameraProgress);
      const c2 = b.subtract(a2).normalize();
      let bearing2 = 180 + degrees(Math.atan2(c2.x, c2.y));
      if (bearing2 > bearing + 90) bearing2 -= 360;
      if (bearing2 < bearing - 90) bearing2 += 360;
      bearing = scale(centeringBehaviour, [0, 1], [bearing, bearing2]);
    }
    const cameraPosition = cameraPath.getLatLonAltFromPseudoCartesianCoordinates(b);
    const pitch = degrees(Math.atan(Math.sqrt(c.x * c.x + c.y * c.y) / c.z));

    return { bearing, pitch, cameraPosition, markerCoordinates };
  }

  isCameraAtCurrentPosition(): boolean {
    const { cameraPosition } = this.calculateNewPosition();
    if (!cameraPosition) return false;

    const xyz = MercatorCoordinate.fromLngLat(
      {
        lng: cameraPosition.lon,
        lat: cameraPosition.lat,
      },
      cameraPosition.alt,
    );

    const { position: xyzOld } = this.mapbox.getFreeCameraOptions();
    if (!xyzOld) return false;

    const dx = xyz.x - xyzOld.x;
    const dy = xyz.y - xyzOld.y;

    return dx * dx + dy * dy < Math.pow(2, -14 - this.mapbox.getZoom());
  }

  setPosition(position: number): void {
    this.position = position;

    const { cameraPosition, bearing, pitch, markerCoordinates } = this.calculateNewPosition();
    if (cameraPosition && bearing && pitch && markerCoordinates) {
      this.moveProgressMarker(markerCoordinates);

      const camera = this.mapbox.getFreeCameraOptions();
      camera.position = MercatorCoordinate.fromLngLat(
        {
          lng: cameraPosition.lon,
          lat: cameraPosition.lat,
        },
        cameraPosition.alt,
      );
      camera.setPitchBearing(pitch, bearing);
      this.mapbox.setFreeCameraOptions(camera, { automatic: true });
    }
  }
}
