import { CircularProgress } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import React from 'react';
import { getPhotoUrl } from '../../helpers/photos';
import { Photo, PhotoSize } from '../../types/WorkoutPayload';

type PhotoSlideProps = {
  classes?: Record<string, string>;
  photo: Photo;
};

const useStyles = makeStyles((theme) => ({
  root: {
    maxWidth: '100vw',
    maxHeight: '100vh',
    margin: 'auto',
    [theme.breakpoints.up('md')]: {
      maxHeight: '80vh',
      maxWidth: '80vw',
    },
  },
  loadingIndicator: {
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    position: 'absolute',
    margin: 'auto',
  },
}));

function PhotoSlide(props: PhotoSlideProps): React.ReactElement {
  const classes = useStyles(props);
  const { photo } = props;

  const [loaded, setLoaded] = React.useState(false);

  return (
    <>
      {!loaded && <CircularProgress className={classes.loadingIndicator} color="primary" />}
      <img
        src={getPhotoUrl(photo, PhotoSize.medium)}
        className={classes.root}
        alt={photo.description}
        role="presentation"
        onLoad={() => setLoaded(true)}
      />
    </>
  );
}

export default PhotoSlide;
