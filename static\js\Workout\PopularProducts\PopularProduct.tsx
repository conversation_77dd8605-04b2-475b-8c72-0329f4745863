import { Button, Typography, useTheme } from '@mui/material';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { useTranslation } from 'react-i18next';
import noOp from '../../helpers/noOp';
import { useLoading } from '../../helpers/useLoading';
import { AmplitudeContext, AmplitudeEvent, useAmplitude } from '../../Amplitude/Amplitude';
import namespaces from '../../i18n/namespaces';
import { fetchPrice, PopularProduct as PopularProductType } from '../../api';
import PricePlaceholder from '../../Placeholder/PricePlaceholder';

type PopularProductProps = {
  classes?: Record<string, string>;
  value: PopularProductType;
  showPrice: boolean;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      margin: theme.spacing(1),
    },
    img: {
      maxWidth: '85%',
      marginBottom: theme.spacing(2),
    },
    link: {
      marginTop: theme.spacing(1.5),
      borderRadius: 200,
    },
    title: {
      textTransform: 'uppercase',
      fontWeight: 'bold',
    },
    imgWrapper: {
      textAlign: 'center',
      lineHeight: 0,
    },
    subtitle: {
      fontWeight: 'bold',
    },
    price: {
      marginTop: theme.spacing(4),
      marginBottom: theme.spacing(1),
      fontWeight: 'bold',
    },
    description: {
      marginTop: theme.spacing(1),
      marginBottom: theme.spacing(1),
    },
  }),
  { name: 'PopularProduct' },
);

const makeImageUrl = (url: string, bgColor: string): string => {
  const imageUrl = new URL(url, 'https://www.suunto.com');
  imageUrl.searchParams.set('height', '250');
  imageUrl.searchParams.set('format', 'jpg');
  imageUrl.searchParams.set('bgcolor', bgColor.slice(1));
  return imageUrl.toString();
};

function withAmplitudeProduct(Component: React.FunctionComponent<PopularProductProps>) {
  return (props: PopularProductProps) => (
    <AmplitudeContext.Provider
      value={{
        ...React.useContext(AmplitudeContext),
        WatchModel: props.value.Name,
        WatchColour: props.value.ProductVariantName,
      }}
    >
      <Component {...props} />
    </AmplitudeContext.Provider>
  );
}

function PopularProduct(props: PopularProductProps): React.ReactElement {
  const classes = useStyles(props);
  const { value, showPrice } = props;
  const { t } = useTranslation(namespaces.TRANSLATIONS);
  const theme = useTheme();
  const [isVisible, setIsVisible] = React.useState<boolean>(false);
  const [price, setPrice] = React.useState<string | null>(null);
  const [priceLoading, setPriceLoading] = useLoading(true);
  const rootRef = React.useRef<HTMLElement>(null);
  const { logEvent } = useAmplitude();

  React.useEffect(() => {
    if (value.Ssids?.length && isVisible && showPrice) {
      fetchPrice(value.Ssids[0])
        .then(({ products }) => {
          const [product] = products;
          const { promo, price } = product.formatted_price;
          setPrice(promo || price);
        })
        .catch(noOp)
        .finally(setPriceLoading());
    }
  }, [value.Ssids, isVisible, showPrice]);

  React.useEffect(() => {
    if (!rootRef.current || isVisible) return;

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      setIsVisible(entry.isIntersecting);
    });

    observer.observe(rootRef.current);

    return () => {
      if (observer && rootRef.current) {
        observer.unobserve(rootRef.current);
      }
    };
  }, [isVisible]);

  return (
    <article className={classes.root} ref={rootRef}>
      <div className={classes.imgWrapper}>
        <img
          className={classes.img}
          src={makeImageUrl(value.MainImagePath, theme.palette.background.default)}
          alt={value.Name + ' ' + value.ProductVariantName}
        />
      </div>
      <Typography variant="h5" className={classes.title}>
        {value.Name}
      </Typography>
      <Typography className={classes.subtitle} variant="h6">
        {value.ProductVariantName}
      </Typography>
      <Typography component="p" variant="h6" className={classes.description}>
        {value.Description}
      </Typography>
      {(price || priceLoading) && showPrice && (
        <Typography className={classes.price} variant="h2">
          {price}
          {priceLoading && <PricePlaceholder />}
        </Typography>
      )}
      <Button
        href={value.Url}
        target="_blank"
        className={classes.link}
        variant="contained"
        color="primary"
        onClick={() => logEvent(AmplitudeEvent.SharedWorkoutPopularProductClicked)}
      >
        {t('VIEW_PRODUCT')}
      </Button>
    </article>
  );
}

export default withAmplitudeProduct(PopularProduct);
