import namespaces from '../../i18n/namespaces';
import generatedSummaryItemPhraseKeys from './generatedSummaryItemPhraseKeys.json';

const overwrites = {
  MaxHeartRate: `${namespaces.PHRASES}:TXT_MAX_HR`,
  AvgHeartrate: `${namespaces.PHRASES}:TXT_AVG_HR`,
  AscentAltitude: `${namespaces.PHRASES}:TXT_TOTAL_ASCENT`,
  DescentAltitude: `${namespaces.TRANSLATIONS}:TOTAL_DESCENT`,
  HighAltitude: `${namespaces.PHRASES}:TXT_MAX_ALTITUDE`,
  LowAltitude: `${namespaces.PHRASES}:TXT_MIN_ALTITUDE`,
  AscentTime: `${namespaces.TRANSLATIONS}:ASCENT_TIME`,
  DescentTime: `${namespaces.TRANSLATIONS}:DESCENT_TIME`,
  EstVO2peak: `${namespaces.PHRASES}:TXT_VO2MAX`,
  Feeling: `${namespaces.PHRASES}:TXT_VIBES`,
  Energy: `${namespaces.PHRASES}:TXT_ENERGY_CONSUMPTION`,
  PeakEpoc: `${namespaces.PHRASES}:TXT_EPOC`,
  DiveSurfaceTime: `${namespaces.PHRASES}:TXT_SURFACE_TIME`,
  DiveMaxDepthTemperature: `${namespaces.PHRASES}:TXT_TEMP_MAX_DEPTH`,
  DiveVisibility: `${namespaces.TRANSLATIONS}:DIVE_VISIBILITY`,
  DiveNumberInSeries: `${namespaces.TRANSLATIONS}:DIVE_NUMBER_IN_SERIES`,
  AvgSWOLF: `${namespaces.PHRASES}:TXT_AVERAGE_SWOLF`,
  SkiTime: `${namespaces.TRANSLATIONS}:SKI_TIME`,
  SkiDistance: `${namespaces.TRANSLATIONS}:SKI_DISTANCE`,
  SkiRuns: `${namespaces.TRANSLATIONS}:SKI_RUNS`,
  AvgSkiSpeed: `${namespaces.TRANSLATIONS}:AVG_SKI_SPEED`,
  MaxSkiSpeed: `${namespaces.TRANSLATIONS}:MAX_SKI_SPEED`,
};

const summaryItemPhraseKeys: Record<string, string> = {
  ...generatedSummaryItemPhraseKeys,
  ...overwrites,
};

export default summaryItemPhraseKeys;
