var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgGaugeOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 3 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M30.8099 8.07066L31.1883 8.20797L30.3118 10.5493C23.9211 8.1568 16.4933 9.65315 11.6339 14.5125C4.90891 21.2375 4.82269 32.3919 11.3753 39.2305L11.6339 39.4947L9.86616 41.2625C2.07749 33.4738 2.07749 20.5334 9.86616 12.7447C15.237 7.37387 23.3128 5.59908 30.4296 7.94125L30.8099 8.07066ZM40.5546 21.994L42.9455 21.2633C45.0671 28.2055 43.4259 35.7529 38.4157 40.9748L38.1339 41.2625L36.3662 39.4947C40.8376 35.0233 42.3904 28.4633 40.6623 22.3596L40.5546 21.994ZM40.3668 11.7185L37.5151 16.6731L35.6645 19.8519C32.3235 25.5453 30.0312 29.1779 28.7267 30.8294L28.6307 30.9496L28.4533 31.1634L28.2932 31.3456L28.1339 31.5125C25.7653 33.8811 21.7348 33.8811 19.3662 31.5125C16.9858 29.1321 16.9082 25.3527 19.2662 22.9947C20.1561 22.1048 22.724 20.5676 27.0861 18.2996L29.0658 17.2826L30.7229 16.4468L33.3322 15.1515L40.3668 11.7185ZM32.6484 20.0444L34.112 17.5543L31.5335 18.8378C26.2388 21.4991 22.8491 23.3774 21.5029 24.3729L21.3119 24.5191L21.1563 24.6486L21.0339 24.7625C19.6689 26.1276 19.714 28.3248 21.1339 29.7447C22.4765 31.0873 24.8004 31.1352 26.1932 29.9089L26.344 29.7674L26.4627 29.643L26.6129 29.471C27.6177 28.2779 29.6462 25.1134 32.6484 20.0444Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgGaugeOutline);
export default __webpack_public_path__ + "static/media/gauge_outline.57822e51.svg";
export { ForwardRef as ReactComponent };