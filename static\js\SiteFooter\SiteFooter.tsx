import { useTheme } from '@mui/material';
import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { useTranslation } from 'react-i18next';
import { fetchSuuntoFooter } from '../api';
import noOp from '../helpers/noOp';
import SuuntoLogo from '../images/suunto_logo.svg';
import SuuntoLogoWhite from '../images/suunto_logo_white.svg';
import parseSiteFooter, { ParseResponse } from './parseSiteFooter';
import Copyrights from './Sections/Copyrights';
import Groups from './Sections/Groups';
import Some from './Sections/Some';
import Terms from './Sections/Terms';

type FooterProps = {
  classes?: Record<string, string>;
};

const useStyles = makeStyles(
  (theme) => ({
    root: {
      position: 'relative',
    },
    someTitle: {
      marginBottom: theme.spacing(1.5),
    },
    groupTitle: {
      textTransform: 'uppercase',
    },
    logoWrapper: {
      display: 'block',
      margin: theme.spacing(2),
      textAlign: 'center',
    },
  }),
  { name: 'Footer' },
);

function SiteFooter(props: FooterProps): React.ReactElement {
  const classes = useStyles(props);
  const [footerConfiguration, setFooterConfiguration] = React.useState<ParseResponse | null>(null);
  const { i18n } = useTranslation();
  const theme = useTheme();

  React.useEffect(() => {
    fetchSuuntoFooter(i18n.language)
      .then((html) => {
        setFooterConfiguration(parseSiteFooter(html));
      })
      .catch(noOp);
  }, [i18n.language]);

  return (
    <footer className={classes.root}>
      <Some
        someLinks={footerConfiguration?.someLinks}
        classes={{
          title: classNames(classes.groupTitle, classes.someTitle),
        }}
      />
      <Groups
        linkGroups={footerConfiguration?.linkGroups}
        classes={{
          title: classNames(classes.groupTitle),
        }}
      />
      <a href="https://suunto.com" target="_blank" className={classes.logoWrapper}>
        <img src={theme.palette.mode === 'dark' ? SuuntoLogoWhite : SuuntoLogo} alt="Suunto logo" />
      </a>
      <Copyrights copyrights={footerConfiguration?.copyrights} />
      <Terms terms={footerConfiguration?.terms} />
    </footer>
  );
}

export default SiteFooter;
