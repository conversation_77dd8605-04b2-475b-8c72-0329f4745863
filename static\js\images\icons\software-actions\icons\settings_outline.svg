var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSettingsOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M27.7948 4L28.6811 8.72697L31.5057 9.73961L35.5396 6.97959L40.9062 12.3462L38.1633 16.355L39.2422 19.0374L44 19.9295V27.519L39.273 28.4053L38.2604 31.23L41.0204 35.2639L35.6538 40.6305L31.6672 37.9028L28.6918 39.2159L27.7948 44H20.2052L19.3045 39.196L16.5911 37.9147L12.6219 40.6305L7.25532 35.2639L9.98901 31.2685L8.77377 28.4141L4 27.519V19.9295L8.80401 19.0288L10.0853 16.3154L7.36954 12.3462L12.7361 6.97959L16.7552 9.72944L19.3155 8.74539L20.2052 4H27.7948ZM25.7199 6.5H22.2801L21.5154 10.5781L16.4268 12.5339L13.0368 10.2145L10.6044 12.6468L12.958 16.0867L10.5195 21.2507L6.5 22.0043V25.4442L10.5498 26.2035L12.8258 31.5496L10.4902 34.9632L12.9226 37.3956L16.3624 35.042L21.5264 37.4805L22.2801 41.5H25.7199L26.4773 37.4606L31.9307 35.0539L35.3532 37.3956L37.7855 34.9632L35.4762 31.5881L37.4034 26.2123L41.5 25.4442V22.0043L37.4343 21.242L35.3448 16.0471L37.6713 12.6468L35.2389 10.2145L31.8638 12.5238L26.4881 10.5966L25.7199 6.5ZM24 15C28.9706 15 33 19.0294 33 24C33 28.9706 28.9706 33 24 33C19.0294 33 15 28.9706 15 24C15 19.0294 19.0294 15 24 15ZM24 17.5C20.4101 17.5 17.5 20.4101 17.5 24C17.5 27.5899 20.4101 30.5 24 30.5C27.5899 30.5 30.5 27.5899 30.5 24C30.5 20.4101 27.5899 17.5 24 17.5Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSettingsOutline);
export default __webpack_public_path__ + "static/media/settings_outline.0d2903ff.svg";
export { ForwardRef as ReactComponent };