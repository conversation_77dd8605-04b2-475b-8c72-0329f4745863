import React from 'react';
import { useTranslation } from 'react-i18next';
import { getActivityConfigBySTId } from '../../activities/helper';
import { Workout } from '../../models/Workout';
import Time from '../../Time/Time';

type WorkoutDetailsProps = {
  workout: Workout;
  hideWorkoutName?: boolean;
};

function WorkoutDetails(props: WorkoutDetailsProps): React.ReactElement {
  const { workout, hideWorkoutName } = props;
  const { t } = useTranslation();
  const activityConfig = getActivityConfigBySTId(workout.workout.activityId);

  const { PhraseID = '' } = activityConfig || {};
  return (
    <>
      {hideWorkoutName ? null : t(PhraseID)} <Time time={workout.workout.startTime} />
    </>
  );
}

export default WorkoutDetails;
