(function (root, factory) {
  if (typeof define === 'function' && define.amd)
    define(['exports', 'kotlin'], factory);
  else if (typeof exports === 'object')
    factory(module.exports, require('kotlin'));
  else {
    if (typeof kotlin === 'undefined') {
      throw new Error("Error loading module 'sim_shared-sim_formatting'. Its dependency 'kotlin' was not found. Please, check whether 'kotlin' is loaded prior to 'sim_shared-sim_formatting'.");
    }
    root['sim_shared-sim_formatting'] = factory(typeof this['sim_shared-sim_formatting'] === 'undefined' ? {} : this['sim_shared-sim_formatting'], kotlin);
  }
}(this, function (_, Kotlin) {
  'use strict';
  var Kind_CLASS = Kotlin.Kind.CLASS;
  var Enum = Kotlin.kotlin.Enum;
  var throwISE = Kotlin.throwISE;
  var isNaN_0 = Kotlin.kotlin.isNaN_yrwdxr$;
  var equals = Kotlin.equals;
  var IllegalArgumentException_init = Kotlin.kotlin.IllegalArgumentException_init_pdl1vj$;
  var Kind_OBJECT = Kotlin.Kind.OBJECT;
  var toString = Kotlin.toString;
  var IllegalArgumentException = Kotlin.kotlin.IllegalArgumentException;
  var JsMath = Math;
  var kotlin_js_internal_DoubleCompanionObject = Kotlin.kotlin.js.internal.DoubleCompanionObject;
  var round = Kotlin.kotlin.math.round_14dthe$;
  var NotImplementedError_init = Kotlin.kotlin.NotImplementedError;
  var LazyThreadSafetyMode = Kotlin.kotlin.LazyThreadSafetyMode;
  var listOf = Kotlin.kotlin.collections.listOf_i5x0yv$;
  var lazy = Kotlin.kotlin.lazy_kls4a0$;
  var listOf_0 = Kotlin.kotlin.collections.listOf_mh5how$;
  var first = Kotlin.kotlin.collections.first_2p1efm$;
  var StringBuilder = Kotlin.kotlin.text.StringBuilder;
  var drop = Kotlin.kotlin.collections.drop_ba2ldo$;
  var take = Kotlin.kotlin.text.take_6ic1pp$;
  var DurationUnit = Kotlin.kotlin.time.DurationUnit;
  var toDuration = Kotlin.kotlin.time.toDuration_n769wd$;
  var to = Kotlin.kotlin.to_ujzrz7$;
  var toDuration_0 = Kotlin.kotlin.time.toDuration_rrkdm6$;
  var lazy_0 = Kotlin.kotlin.lazy_klfg04$;
  var roundToLong = Kotlin.kotlin.math.roundToLong_yrwdxr$;
  var throwCCE = Kotlin.throwCCE;
  var Regex_init = Kotlin.kotlin.text.Regex_init_61zpoe$;
  var nativeSign = Math.sign;
  var nativeTrunc = Math.trunc;
  ConversionSuccess.prototype = Object.create(ConversionResult.prototype);
  ConversionSuccess.prototype.constructor = ConversionSuccess;
  ConversionFailure.prototype = Object.create(ConversionResult.prototype);
  ConversionFailure.prototype.constructor = ConversionFailure;
  FormatSuccess.prototype = Object.create(FormatResult.prototype);
  FormatSuccess.prototype.constructor = FormatSuccess;
  FormatFailure.prototype = Object.create(FormatResult.prototype);
  FormatFailure.prototype.constructor = FormatFailure;
  Icon.prototype = Object.create(Enum.prototype);
  Icon.prototype.constructor = Icon;
  MeasurementSystem.prototype = Object.create(Enum.prototype);
  MeasurementSystem.prototype.constructor = MeasurementSystem;
  Method.prototype = Object.create(Enum.prototype);
  Method.prototype.constructor = Method;
  TemperatureConversionUnit.prototype = Object.create(Unit.prototype);
  TemperatureConversionUnit.prototype.constructor = TemperatureConversionUnit;
  FactorialConversionUnit.prototype = Object.create(Unit.prototype);
  FactorialConversionUnit.prototype.constructor = FactorialConversionUnit;
  Formatter.prototype = Object.create(BaseFormatter.prototype);
  Formatter.prototype.constructor = Formatter;
  RangeSimpleConversion.prototype = Object.create(Range.prototype);
  RangeSimpleConversion.prototype.constructor = RangeSimpleConversion;
  RangeDateTime.prototype = Object.create(Range.prototype);
  RangeDateTime.prototype.constructor = RangeDateTime;
  RangeDuration.prototype = Object.create(Range.prototype);
  RangeDuration.prototype.constructor = RangeDuration;
  RangePercentage.prototype = Object.create(Range.prototype);
  RangePercentage.prototype.constructor = RangePercentage;
  Delimiter.prototype = Object.create(TimeFormatToken.prototype);
  Delimiter.prototype.constructor = Delimiter;
  _a.prototype = Object.create(TimeFormatToken.prototype);
  _a.prototype.constructor = _a;
  _d.prototype = Object.create(TimeFormatToken.prototype);
  _d.prototype.constructor = _d;
  _hh.prototype = Object.create(TimeFormatToken.prototype);
  _hh.prototype.constructor = _hh;
  _h.prototype = Object.create(TimeFormatToken.prototype);
  _h.prototype.constructor = _h;
  HH.prototype = Object.create(TimeFormatToken.prototype);
  HH.prototype.constructor = HH;
  H.prototype = Object.create(TimeFormatToken.prototype);
  H.prototype.constructor = H;
  _mm.prototype = Object.create(TimeFormatToken.prototype);
  _mm.prototype.constructor = _mm;
  _m.prototype = Object.create(TimeFormatToken.prototype);
  _m.prototype.constructor = _m;
  _ss.prototype = Object.create(TimeFormatToken.prototype);
  _ss.prototype.constructor = _ss;
  _s.prototype = Object.create(TimeFormatToken.prototype);
  _s.prototype.constructor = _s;
  _f.prototype = Object.create(TimeFormatToken.prototype);
  _f.prototype.constructor = _f;
  _ff.prototype = Object.create(TimeFormatToken.prototype);
  _ff.prototype.constructor = _ff;
  _fff.prototype = Object.create(TimeFormatToken.prototype);
  _fff.prototype.constructor = _fff;
  Dimension.prototype = Object.create(Enum.prototype);
  Dimension.prototype.constructor = Dimension;
  function ConversionResult() {
  }
  ConversionResult.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ConversionResult',
    interfaces: []
  };
  function ConversionSuccess(value, unit) {
    ConversionResult.call(this);
    this.value = value;
    this.unit = unit;
  }
  ConversionSuccess.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ConversionSuccess',
    interfaces: [ConversionResult]
  };
  ConversionSuccess.prototype.component1 = function () {
    return this.value;
  };
  ConversionSuccess.prototype.component2 = function () {
    return this.unit;
  };
  ConversionSuccess.prototype.copy_qfvghl$ = function (value, unit) {
    return new ConversionSuccess(value === void 0 ? this.value : value, unit === void 0 ? this.unit : unit);
  };
  ConversionSuccess.prototype.toString = function () {
    return 'ConversionSuccess(value=' + Kotlin.toString(this.value) + (', unit=' + Kotlin.toString(this.unit)) + ')';
  };
  ConversionSuccess.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.value) | 0;
    result = result * 31 + Kotlin.hashCode(this.unit) | 0;
    return result;
  };
  ConversionSuccess.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.value, other.value) && Kotlin.equals(this.unit, other.unit)))));
  };
  function ConversionFailure(reason) {
    ConversionResult.call(this);
    this.reason = reason;
  }
  ConversionFailure.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ConversionFailure',
    interfaces: [ConversionResult]
  };
  ConversionFailure.prototype.component1 = function () {
    return this.reason;
  };
  ConversionFailure.prototype.copy_61zpoe$ = function (reason) {
    return new ConversionFailure(reason === void 0 ? this.reason : reason);
  };
  ConversionFailure.prototype.toString = function () {
    return 'ConversionFailure(reason=' + Kotlin.toString(this.reason) + ')';
  };
  ConversionFailure.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.reason) | 0;
    return result;
  };
  ConversionFailure.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && Kotlin.equals(this.reason, other.reason))));
  };
  function format(formatName, value, formattingOptions) {
    switch (formatName) {
      case 'SwimPace':
        return Formatter_getInstance().swimPaceFourdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRateBpm':
        return Formatter_getInstance().heartRateBpmFourdigits_3r6o6c$(value, formattingOptions);
      case 'DiveGasPressure':
        return Formatter_getInstance().diveGasPressureNodecimal_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeed':
        return Formatter_getInstance().downhillSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'Cadence':
        return Formatter_getInstance().cadenceFourdigits_3r6o6c$(value, formattingOptions);
      case 'Strokes':
        return Formatter_getInstance().strokesThreedigits_3r6o6c$(value, formattingOptions);
      case 'PeakTrainingEffect':
        return Formatter_getInstance().peakTrainingEffectFourdigits_3r6o6c$(value, formattingOptions);
      case 'SwimDistance':
        return Formatter_getInstance().swimDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'Count':
        return Formatter_getInstance().countThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDuration':
        return Formatter_getInstance().downhillDurationApproximate_3r6o6c$(value, formattingOptions);
      case 'FlightTime':
        return Formatter_getInstance().flightTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'RecoveryTime':
        return Formatter_getInstance().recoveryTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'Ascent':
        return Formatter_getInstance().ascentFivedigits_3r6o6c$(value, formattingOptions);
      case 'Pace':
        return Formatter_getInstance().paceFourdigits_3r6o6c$(value, formattingOptions);
      case 'VO2':
        return Formatter_getInstance().vO2Fourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistance':
        return Formatter_getInstance().navigationRouteDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'Stiffness':
        return Formatter_getInstance().stiffnessTwodigits_3r6o6c$(value, formattingOptions);
      case 'DownhillAltitude':
        return Formatter_getInstance().downhillAltitudeFivedigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingDeg':
        return Formatter_getInstance().compassHeadingDegAccurate_3r6o6c$(value, formattingOptions);
      case 'Distance':
        return Formatter_getInstance().distanceAccurate_3r6o6c$(value, formattingOptions);
      case 'TrackAndFieldDistance':
        return Formatter_getInstance().trackAndFieldDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'Descent':
        return Formatter_getInstance().descentFivedigits_3r6o6c$(value, formattingOptions);
      case 'EPOC':
        return Formatter_getInstance().ePOCFourdigits_3r6o6c$(value, formattingOptions);
      case 'PoolSwimDistance':
        return Formatter_getInstance().poolSwimDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'StepLength':
        return Formatter_getInstance().stepLengthThreedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETE':
        return Formatter_getInstance().navigationPoiETEHumane_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistance':
        return Formatter_getInstance().navigationPOIDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'Duration':
        return Formatter_getInstance().durationAccurate_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingMil':
        return Formatter_getInstance().compassHeadingMilFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDescent':
        return Formatter_getInstance().downhillDescentFivedigits_3r6o6c$(value, formattingOptions);
      case 'Reactivity':
        return Formatter_getInstance().reactivityOnedigit_3r6o6c$(value, formattingOptions);
      case 'ContactTime':
        return Formatter_getInstance().contactTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'Weigth':
        return Formatter_getInstance().weigthFourdigits_3r6o6c$(value, formattingOptions);
      case 'CadenceSpm':
        return Formatter_getInstance().cadenceSpmFourdigits_3r6o6c$(value, formattingOptions);
      case 'Declination':
        return Formatter_getInstance().declinationFourdigits_3r6o6c$(value, formattingOptions);
      case 'Performance':
        return Formatter_getInstance().performanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'DiveGasConsumption':
        return Formatter_getInstance().diveGasConsumptionOnedecimal_3r6o6c$(value, formattingOptions);
      case 'DiveDuration':
        return Formatter_getInstance().diveDurationAccurate_3r6o6c$(value, formattingOptions);
      case 'DownhillDistance':
        return Formatter_getInstance().downhillDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'HeartRate':
        return Formatter_getInstance().heartRateFourdigits_3r6o6c$(value, formattingOptions);
      case 'Temperature':
        return Formatter_getInstance().temperatureFourdigits_3r6o6c$(value, formattingOptions);
      case 'Undulation':
        return Formatter_getInstance().undulationThreedigits_3r6o6c$(value, formattingOptions);
      case 'DurationMs':
        return Formatter_getInstance().durationMsAccurate_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETE':
        return Formatter_getInstance().navigationRouteETEHumane_3r6o6c$(value, formattingOptions);
      case 'RowingPace':
        return Formatter_getInstance().rowingPaceFourdigits_3r6o6c$(value, formattingOptions);
      case 'DiveDistance':
        return Formatter_getInstance().diveDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'NauticalDistance':
        return Formatter_getInstance().nauticalDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'Energy':
        return Formatter_getInstance().energyFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETA':
        return Formatter_getInstance().navigationPoiETAFourdigits_3r6o6c$(value, formattingOptions);
      case 'StrokeRate':
        return Formatter_getInstance().strokeRateFivedigits_3r6o6c$(value, formattingOptions);
      case 'Speed':
        return Formatter_getInstance().speedFourdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRatePercentage':
        return Formatter_getInstance().heartRatePercentageThreedigits_3r6o6c$(value, formattingOptions);
      case 'TrainingEffect':
        return Formatter_getInstance().trainingEffectFourdigits_3r6o6c$(value, formattingOptions);
      case 'TimeOfDay':
        return Formatter_getInstance().timeOfDayAccurate_3r6o6c$(value, formattingOptions);
      case 'DownhillLapCount':
        return Formatter_getInstance().downhillLapCountThreedigits_3r6o6c$(value, formattingOptions);
      case 'Percentage':
        return Formatter_getInstance().percentageThreedigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedMountain':
        return Formatter_getInstance().verticalSpeedMountainFourdigits_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeed':
        return Formatter_getInstance().nauticalSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'Swolf':
        return Formatter_getInstance().swolfThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGrade':
        return Formatter_getInstance().downhillGradeThreedigits_3r6o6c$(value, formattingOptions);
      case 'Sunset':
        return Formatter_getInstance().sunsetAccurate_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETA':
        return Formatter_getInstance().navigationRouteETAFourdigits_3r6o6c$(value, formattingOptions);
      case 'Sunrise':
        return Formatter_getInstance().sunriseAccurate_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeed':
        return Formatter_getInstance().verticalSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'AirPressure':
        return Formatter_getInstance().airPressureFourdigits_3r6o6c$(value, formattingOptions);
      case 'Power':
        return Formatter_getInstance().powerFourdigits_3r6o6c$(value, formattingOptions);
      case 'Altitude':
        return Formatter_getInstance().altitudeFivedigits_3r6o6c$(value, formattingOptions);
      default:
        return new FormatFailure('Format name [' + formatName + '] not supported');
    }
  }
  function formatWithStyle(formatNameWithStyle, value, formattingOptions) {
    switch (formatNameWithStyle) {
      case 'SwimPaceFourdigits':
        return Formatter_getInstance().swimPaceFourdigits_3r6o6c$(value, formattingOptions);
      case 'SwimPaceFixedNoLeadingZero':
        return Formatter_getInstance().swimPaceFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'SwimPaceFivedigits':
        return Formatter_getInstance().swimPaceFivedigits_3r6o6c$(value, formattingOptions);
      case 'SwimPaceSixdigits':
        return Formatter_getInstance().swimPaceSixdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRateBpmFourdigits':
        return Formatter_getInstance().heartRateBpmFourdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRateBpmFivedigits':
        return Formatter_getInstance().heartRateBpmFivedigits_3r6o6c$(value, formattingOptions);
      case 'DiveGasPressureNodecimal':
        return Formatter_getInstance().diveGasPressureNodecimal_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeedFourdigits':
        return Formatter_getInstance().downhillSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeedThreedigits':
        return Formatter_getInstance().downhillSpeedThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeedApproximate':
        return Formatter_getInstance().downhillSpeedApproximate_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeedFivedigits':
        return Formatter_getInstance().downhillSpeedFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillSpeedSixdigits':
        return Formatter_getInstance().downhillSpeedSixdigits_3r6o6c$(value, formattingOptions);
      case 'CadenceFourdigits':
        return Formatter_getInstance().cadenceFourdigits_3r6o6c$(value, formattingOptions);
      case 'CadenceFivedigits':
        return Formatter_getInstance().cadenceFivedigits_3r6o6c$(value, formattingOptions);
      case 'CadenceSixdigits':
        return Formatter_getInstance().cadenceSixdigits_3r6o6c$(value, formattingOptions);
      case 'StrokesFourdigits':
        return Formatter_getInstance().strokesFourdigits_3r6o6c$(value, formattingOptions);
      case 'StrokesThreedigits':
        return Formatter_getInstance().strokesThreedigits_3r6o6c$(value, formattingOptions);
      case 'StrokesFivedigits':
        return Formatter_getInstance().strokesFivedigits_3r6o6c$(value, formattingOptions);
      case 'StrokesSixdigits':
        return Formatter_getInstance().strokesSixdigits_3r6o6c$(value, formattingOptions);
      case 'PeakTrainingEffectFourdigits':
        return Formatter_getInstance().peakTrainingEffectFourdigits_3r6o6c$(value, formattingOptions);
      case 'PeakTrainingEffectFivedigits':
        return Formatter_getInstance().peakTrainingEffectFivedigits_3r6o6c$(value, formattingOptions);
      case 'PeakTrainingEffectSixdigits':
        return Formatter_getInstance().peakTrainingEffectSixdigits_3r6o6c$(value, formattingOptions);
      case 'SwimDistanceFourdigits':
        return Formatter_getInstance().swimDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'SwimDistanceFivedigits':
        return Formatter_getInstance().swimDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'SwimDistanceSixdigits':
        return Formatter_getInstance().swimDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'CountFourdigits':
        return Formatter_getInstance().countFourdigits_3r6o6c$(value, formattingOptions);
      case 'CountTwodigits':
        return Formatter_getInstance().countTwodigits_3r6o6c$(value, formattingOptions);
      case 'CountThreedigits':
        return Formatter_getInstance().countThreedigits_3r6o6c$(value, formattingOptions);
      case 'CountFivedigits':
        return Formatter_getInstance().countFivedigits_3r6o6c$(value, formattingOptions);
      case 'CountSixdigits':
        return Formatter_getInstance().countSixdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDurationTraining':
        return Formatter_getInstance().downhillDurationTraining_3r6o6c$(value, formattingOptions);
      case 'DownhillDurationFourdigits':
        return Formatter_getInstance().downhillDurationFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDurationApproximate':
        return Formatter_getInstance().downhillDurationApproximate_3r6o6c$(value, formattingOptions);
      case 'DownhillDurationFivedigits':
        return Formatter_getInstance().downhillDurationFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDurationSixdigits':
        return Formatter_getInstance().downhillDurationSixdigits_3r6o6c$(value, formattingOptions);
      case 'FlightTimeFourdigits':
        return Formatter_getInstance().flightTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'RecoveryTimeFourdigits':
        return Formatter_getInstance().recoveryTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'RecoveryTimeFivedigits':
        return Formatter_getInstance().recoveryTimeFivedigits_3r6o6c$(value, formattingOptions);
      case 'RecoveryTimeSixdigits':
        return Formatter_getInstance().recoveryTimeSixdigits_3r6o6c$(value, formattingOptions);
      case 'AscentFourdigits':
        return Formatter_getInstance().ascentFourdigits_3r6o6c$(value, formattingOptions);
      case 'AscentFivedigits':
        return Formatter_getInstance().ascentFivedigits_3r6o6c$(value, formattingOptions);
      case 'AscentSixdigits':
        return Formatter_getInstance().ascentSixdigits_3r6o6c$(value, formattingOptions);
      case 'PaceFourdigits':
        return Formatter_getInstance().paceFourdigits_3r6o6c$(value, formattingOptions);
      case 'PaceFixedNoLeadingZero':
        return Formatter_getInstance().paceFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'PaceFivedigits':
        return Formatter_getInstance().paceFivedigits_3r6o6c$(value, formattingOptions);
      case 'PaceSixdigits':
        return Formatter_getInstance().paceSixdigits_3r6o6c$(value, formattingOptions);
      case 'VO2Fourdigits':
        return Formatter_getInstance().vO2Fourdigits_3r6o6c$(value, formattingOptions);
      case 'VO2Fivedigits':
        return Formatter_getInstance().vO2Fivedigits_3r6o6c$(value, formattingOptions);
      case 'VO2Sixdigits':
        return Formatter_getInstance().vO2Sixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistanceFourdigits':
        return Formatter_getInstance().navigationRouteDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistanceThreedigits':
        return Formatter_getInstance().navigationRouteDistanceThreedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistanceFivedigits':
        return Formatter_getInstance().navigationRouteDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistanceSixdigits':
        return Formatter_getInstance().navigationRouteDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteDistanceAccurate':
        return Formatter_getInstance().navigationRouteDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'StiffnessTwodigits':
        return Formatter_getInstance().stiffnessTwodigits_3r6o6c$(value, formattingOptions);
      case 'DownhillAltitudeFourdigits':
        return Formatter_getInstance().downhillAltitudeFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillAltitudeFivedigits':
        return Formatter_getInstance().downhillAltitudeFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillAltitudeSixdigits':
        return Formatter_getInstance().downhillAltitudeSixdigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingDegFourdigits':
        return Formatter_getInstance().compassHeadingDegFourdigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingDegFivedigits':
        return Formatter_getInstance().compassHeadingDegFivedigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingDegSixdigits':
        return Formatter_getInstance().compassHeadingDegSixdigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingDegAccurate':
        return Formatter_getInstance().compassHeadingDegAccurate_3r6o6c$(value, formattingOptions);
      case 'DistanceFourdigits':
        return Formatter_getInstance().distanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'DistanceAccumulated':
        return Formatter_getInstance().distanceAccumulated_3r6o6c$(value, formattingOptions);
      case 'DistanceThreedigits':
        return Formatter_getInstance().distanceThreedigits_3r6o6c$(value, formattingOptions);
      case 'DistanceMapscale':
        return Formatter_getInstance().distanceMapscale_3r6o6c$(value, formattingOptions);
      case 'DistanceApproximate':
        return Formatter_getInstance().distanceApproximate_3r6o6c$(value, formattingOptions);
      case 'DistanceNodecimal':
        return Formatter_getInstance().distanceNodecimal_3r6o6c$(value, formattingOptions);
      case 'DistanceFivedigits':
        return Formatter_getInstance().distanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'DistanceSixdigits':
        return Formatter_getInstance().distanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'DistanceOnedecimal':
        return Formatter_getInstance().distanceOnedecimal_3r6o6c$(value, formattingOptions);
      case 'DistanceAccurate':
        return Formatter_getInstance().distanceAccurate_3r6o6c$(value, formattingOptions);
      case 'TrackAndFieldDistanceFivedigits':
        return Formatter_getInstance().trackAndFieldDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'DescentFourdigits':
        return Formatter_getInstance().descentFourdigits_3r6o6c$(value, formattingOptions);
      case 'DescentFivedigits':
        return Formatter_getInstance().descentFivedigits_3r6o6c$(value, formattingOptions);
      case 'DescentSixdigits':
        return Formatter_getInstance().descentSixdigits_3r6o6c$(value, formattingOptions);
      case 'EPOCFourdigits':
        return Formatter_getInstance().ePOCFourdigits_3r6o6c$(value, formattingOptions);
      case 'EPOCFivedigits':
        return Formatter_getInstance().ePOCFivedigits_3r6o6c$(value, formattingOptions);
      case 'EPOCSixdigits':
        return Formatter_getInstance().ePOCSixdigits_3r6o6c$(value, formattingOptions);
      case 'PoolSwimDistanceFourdigits':
        return Formatter_getInstance().poolSwimDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'PoolSwimDistanceFivedigits':
        return Formatter_getInstance().poolSwimDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'PoolSwimDistanceSixdigits':
        return Formatter_getInstance().poolSwimDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'StepLengthThreedigits':
        return Formatter_getInstance().stepLengthThreedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEFourdigits':
        return Formatter_getInstance().navigationPoiETEFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEFourdigitsFixed':
        return Formatter_getInstance().navigationPoiETEFourdigitsFixed_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEHours':
        return Formatter_getInstance().navigationPoiETEHours_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEFixedNoLeadingZero':
        return Formatter_getInstance().navigationPoiETEFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEHumane':
        return Formatter_getInstance().navigationPoiETEHumane_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEFivedigits':
        return Formatter_getInstance().navigationPoiETEFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETESixdigits':
        return Formatter_getInstance().navigationPoiETESixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEMinutes':
        return Formatter_getInstance().navigationPoiETEMinutes_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETEFixed':
        return Formatter_getInstance().navigationPoiETEFixed_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistanceFourdigits':
        return Formatter_getInstance().navigationPOIDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistanceThreedigits':
        return Formatter_getInstance().navigationPOIDistanceThreedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistanceFivedigits':
        return Formatter_getInstance().navigationPOIDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistanceSixdigits':
        return Formatter_getInstance().navigationPOIDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPOIDistanceAccurate':
        return Formatter_getInstance().navigationPOIDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'DurationFourdigits':
        return Formatter_getInstance().durationFourdigits_3r6o6c$(value, formattingOptions);
      case 'DurationAccumulated':
        return Formatter_getInstance().durationAccumulated_3r6o6c$(value, formattingOptions);
      case 'DurationHours':
        return Formatter_getInstance().durationHours_3r6o6c$(value, formattingOptions);
      case 'DurationFixedNoLeadingZero':
        return Formatter_getInstance().durationFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'DurationApproximate':
        return Formatter_getInstance().durationApproximate_3r6o6c$(value, formattingOptions);
      case 'DurationMinutes':
        return Formatter_getInstance().durationMinutes_3r6o6c$(value, formattingOptions);
      case 'DurationTraining':
        return Formatter_getInstance().durationTraining_3r6o6c$(value, formattingOptions);
      case 'DurationApproximateNoLeadingZero':
        return Formatter_getInstance().durationApproximateNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'DurationFourdigitsFixed':
        return Formatter_getInstance().durationFourdigitsFixed_3r6o6c$(value, formattingOptions);
      case 'DurationHumane':
        return Formatter_getInstance().durationHumane_3r6o6c$(value, formattingOptions);
      case 'DurationNodecimal':
        return Formatter_getInstance().durationNodecimal_3r6o6c$(value, formattingOptions);
      case 'DurationFourdigitsFixedRounded':
        return Formatter_getInstance().durationFourdigitsFixedRounded_3r6o6c$(value, formattingOptions);
      case 'DurationFivedigits':
        return Formatter_getInstance().durationFivedigits_3r6o6c$(value, formattingOptions);
      case 'DurationSixdigits':
        return Formatter_getInstance().durationSixdigits_3r6o6c$(value, formattingOptions);
      case 'DurationAccurate':
        return Formatter_getInstance().durationAccurate_3r6o6c$(value, formattingOptions);
      case 'DurationFixed':
        return Formatter_getInstance().durationFixed_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingMilFourdigits':
        return Formatter_getInstance().compassHeadingMilFourdigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingMilFivedigits':
        return Formatter_getInstance().compassHeadingMilFivedigits_3r6o6c$(value, formattingOptions);
      case 'CompassHeadingMilSixdigits':
        return Formatter_getInstance().compassHeadingMilSixdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDescentFourdigits':
        return Formatter_getInstance().downhillDescentFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDescentFivedigits':
        return Formatter_getInstance().downhillDescentFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDescentSixdigits':
        return Formatter_getInstance().downhillDescentSixdigits_3r6o6c$(value, formattingOptions);
      case 'ReactivityOnedigit':
        return Formatter_getInstance().reactivityOnedigit_3r6o6c$(value, formattingOptions);
      case 'ContactTimeFourdigits':
        return Formatter_getInstance().contactTimeFourdigits_3r6o6c$(value, formattingOptions);
      case 'WeigthFourdigits':
        return Formatter_getInstance().weigthFourdigits_3r6o6c$(value, formattingOptions);
      case 'WeigthFivedigits':
        return Formatter_getInstance().weigthFivedigits_3r6o6c$(value, formattingOptions);
      case 'WeigthSixdigits':
        return Formatter_getInstance().weigthSixdigits_3r6o6c$(value, formattingOptions);
      case 'CadenceSpmFourdigits':
        return Formatter_getInstance().cadenceSpmFourdigits_3r6o6c$(value, formattingOptions);
      case 'CadenceSpmFivedigits':
        return Formatter_getInstance().cadenceSpmFivedigits_3r6o6c$(value, formattingOptions);
      case 'CadenceSpmSixdigits':
        return Formatter_getInstance().cadenceSpmSixdigits_3r6o6c$(value, formattingOptions);
      case 'DeclinationFourdigits':
        return Formatter_getInstance().declinationFourdigits_3r6o6c$(value, formattingOptions);
      case 'DeclinationFivedigits':
        return Formatter_getInstance().declinationFivedigits_3r6o6c$(value, formattingOptions);
      case 'DeclinationSixdigits':
        return Formatter_getInstance().declinationSixdigits_3r6o6c$(value, formattingOptions);
      case 'PerformanceFourdigits':
        return Formatter_getInstance().performanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'PerformanceFivedigits':
        return Formatter_getInstance().performanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'PerformanceSixdigits':
        return Formatter_getInstance().performanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'DiveGasConsumptionOnedecimal':
        return Formatter_getInstance().diveGasConsumptionOnedecimal_3r6o6c$(value, formattingOptions);
      case 'DiveDurationAccurate':
        return Formatter_getInstance().diveDurationAccurate_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceFourdigits':
        return Formatter_getInstance().downhillDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceThreedigits':
        return Formatter_getInstance().downhillDistanceThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceApproximate':
        return Formatter_getInstance().downhillDistanceApproximate_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceFivedigits':
        return Formatter_getInstance().downhillDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceSixdigits':
        return Formatter_getInstance().downhillDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillDistanceAccurate':
        return Formatter_getInstance().downhillDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'HeartRateFourdigits':
        return Formatter_getInstance().heartRateFourdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRateFivedigits':
        return Formatter_getInstance().heartRateFivedigits_3r6o6c$(value, formattingOptions);
      case 'HeartRateSixdigits':
        return Formatter_getInstance().heartRateSixdigits_3r6o6c$(value, formattingOptions);
      case 'TemperatureFourdigits':
        return Formatter_getInstance().temperatureFourdigits_3r6o6c$(value, formattingOptions);
      case 'TemperatureFivedigits':
        return Formatter_getInstance().temperatureFivedigits_3r6o6c$(value, formattingOptions);
      case 'TemperatureSixdigits':
        return Formatter_getInstance().temperatureSixdigits_3r6o6c$(value, formattingOptions);
      case 'UndulationThreedigits':
        return Formatter_getInstance().undulationThreedigits_3r6o6c$(value, formattingOptions);
      case 'DurationMsFourdigits':
        return Formatter_getInstance().durationMsFourdigits_3r6o6c$(value, formattingOptions);
      case 'DurationMsApproximate':
        return Formatter_getInstance().durationMsApproximate_3r6o6c$(value, formattingOptions);
      case 'DurationMsFivedigits':
        return Formatter_getInstance().durationMsFivedigits_3r6o6c$(value, formattingOptions);
      case 'DurationMsSixdigits':
        return Formatter_getInstance().durationMsSixdigits_3r6o6c$(value, formattingOptions);
      case 'DurationMsAccurate':
        return Formatter_getInstance().durationMsAccurate_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEFourdigits':
        return Formatter_getInstance().navigationRouteETEFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEFourdigitsFixed':
        return Formatter_getInstance().navigationRouteETEFourdigitsFixed_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEHours':
        return Formatter_getInstance().navigationRouteETEHours_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEFixedNoLeadingZero':
        return Formatter_getInstance().navigationRouteETEFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEHumane':
        return Formatter_getInstance().navigationRouteETEHumane_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEFivedigits':
        return Formatter_getInstance().navigationRouteETEFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETESixdigits':
        return Formatter_getInstance().navigationRouteETESixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEMinutes':
        return Formatter_getInstance().navigationRouteETEMinutes_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETEFixed':
        return Formatter_getInstance().navigationRouteETEFixed_3r6o6c$(value, formattingOptions);
      case 'RowingPaceFourdigits':
        return Formatter_getInstance().rowingPaceFourdigits_3r6o6c$(value, formattingOptions);
      case 'RowingPaceFixedNoLeadingZero':
        return Formatter_getInstance().rowingPaceFixedNoLeadingZero_3r6o6c$(value, formattingOptions);
      case 'RowingPaceFivedigits':
        return Formatter_getInstance().rowingPaceFivedigits_3r6o6c$(value, formattingOptions);
      case 'RowingPaceSixdigits':
        return Formatter_getInstance().rowingPaceSixdigits_3r6o6c$(value, formattingOptions);
      case 'DiveDistanceAccurate':
        return Formatter_getInstance().diveDistanceAccurate_3r6o6c$(value, formattingOptions);
      case 'NauticalDistanceFourdigits':
        return Formatter_getInstance().nauticalDistanceFourdigits_3r6o6c$(value, formattingOptions);
      case 'NauticalDistanceFivedigits':
        return Formatter_getInstance().nauticalDistanceFivedigits_3r6o6c$(value, formattingOptions);
      case 'NauticalDistanceSixdigits':
        return Formatter_getInstance().nauticalDistanceSixdigits_3r6o6c$(value, formattingOptions);
      case 'EnergyFourdigits':
        return Formatter_getInstance().energyFourdigits_3r6o6c$(value, formattingOptions);
      case 'EnergyAccumulated':
        return Formatter_getInstance().energyAccumulated_3r6o6c$(value, formattingOptions);
      case 'EnergyFivedigits':
        return Formatter_getInstance().energyFivedigits_3r6o6c$(value, formattingOptions);
      case 'EnergySixdigits':
        return Formatter_getInstance().energySixdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETAFourdigits':
        return Formatter_getInstance().navigationPoiETAFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETAFivedigits':
        return Formatter_getInstance().navigationPoiETAFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationPoiETASixdigits':
        return Formatter_getInstance().navigationPoiETASixdigits_3r6o6c$(value, formattingOptions);
      case 'StrokeRateFourdigits':
        return Formatter_getInstance().strokeRateFourdigits_3r6o6c$(value, formattingOptions);
      case 'StrokeRateFivedigits':
        return Formatter_getInstance().strokeRateFivedigits_3r6o6c$(value, formattingOptions);
      case 'StrokeRateSixdigits':
        return Formatter_getInstance().strokeRateSixdigits_3r6o6c$(value, formattingOptions);
      case 'SpeedFourdigits':
        return Formatter_getInstance().speedFourdigits_3r6o6c$(value, formattingOptions);
      case 'SpeedThreedigits':
        return Formatter_getInstance().speedThreedigits_3r6o6c$(value, formattingOptions);
      case 'SpeedApproximate':
        return Formatter_getInstance().speedApproximate_3r6o6c$(value, formattingOptions);
      case 'SpeedFivedigits':
        return Formatter_getInstance().speedFivedigits_3r6o6c$(value, formattingOptions);
      case 'SpeedSixdigits':
        return Formatter_getInstance().speedSixdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRatePercentageFourdigits':
        return Formatter_getInstance().heartRatePercentageFourdigits_3r6o6c$(value, formattingOptions);
      case 'HeartRatePercentageThreedigits':
        return Formatter_getInstance().heartRatePercentageThreedigits_3r6o6c$(value, formattingOptions);
      case 'HeartRatePercentageFivedigits':
        return Formatter_getInstance().heartRatePercentageFivedigits_3r6o6c$(value, formattingOptions);
      case 'HeartRatePercentageSixdigits':
        return Formatter_getInstance().heartRatePercentageSixdigits_3r6o6c$(value, formattingOptions);
      case 'TrainingEffectFourdigits':
        return Formatter_getInstance().trainingEffectFourdigits_3r6o6c$(value, formattingOptions);
      case 'TrainingEffectFivedigits':
        return Formatter_getInstance().trainingEffectFivedigits_3r6o6c$(value, formattingOptions);
      case 'TrainingEffectSixdigits':
        return Formatter_getInstance().trainingEffectSixdigits_3r6o6c$(value, formattingOptions);
      case 'TimeOfDayFourdigits':
        return Formatter_getInstance().timeOfDayFourdigits_3r6o6c$(value, formattingOptions);
      case 'TimeOfDayFivedigits':
        return Formatter_getInstance().timeOfDayFivedigits_3r6o6c$(value, formattingOptions);
      case 'TimeOfDaySixdigits':
        return Formatter_getInstance().timeOfDaySixdigits_3r6o6c$(value, formattingOptions);
      case 'TimeOfDayAccurate':
        return Formatter_getInstance().timeOfDayAccurate_3r6o6c$(value, formattingOptions);
      case 'DownhillLapCountFourdigits':
        return Formatter_getInstance().downhillLapCountFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillLapCountThreedigits':
        return Formatter_getInstance().downhillLapCountThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillLapCountFivedigits':
        return Formatter_getInstance().downhillLapCountFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillLapCountSixdigits':
        return Formatter_getInstance().downhillLapCountSixdigits_3r6o6c$(value, formattingOptions);
      case 'PercentageFourdigits':
        return Formatter_getInstance().percentageFourdigits_3r6o6c$(value, formattingOptions);
      case 'PercentageThreedigits':
        return Formatter_getInstance().percentageThreedigits_3r6o6c$(value, formattingOptions);
      case 'PercentageFivedigits':
        return Formatter_getInstance().percentageFivedigits_3r6o6c$(value, formattingOptions);
      case 'PercentageSixdigits':
        return Formatter_getInstance().percentageSixdigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedMountainFourdigits':
        return Formatter_getInstance().verticalSpeedMountainFourdigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedMountainThreedigits':
        return Formatter_getInstance().verticalSpeedMountainThreedigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedMountainFivedigits':
        return Formatter_getInstance().verticalSpeedMountainFivedigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedMountainSixdigits':
        return Formatter_getInstance().verticalSpeedMountainSixdigits_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeedFourdigits':
        return Formatter_getInstance().nauticalSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeedThreedigits':
        return Formatter_getInstance().nauticalSpeedThreedigits_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeedApproximate':
        return Formatter_getInstance().nauticalSpeedApproximate_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeedFivedigits':
        return Formatter_getInstance().nauticalSpeedFivedigits_3r6o6c$(value, formattingOptions);
      case 'NauticalSpeedSixdigits':
        return Formatter_getInstance().nauticalSpeedSixdigits_3r6o6c$(value, formattingOptions);
      case 'SwolfFourdigits':
        return Formatter_getInstance().swolfFourdigits_3r6o6c$(value, formattingOptions);
      case 'SwolfThreedigits':
        return Formatter_getInstance().swolfThreedigits_3r6o6c$(value, formattingOptions);
      case 'SwolfFivedigits':
        return Formatter_getInstance().swolfFivedigits_3r6o6c$(value, formattingOptions);
      case 'SwolfSixdigits':
        return Formatter_getInstance().swolfSixdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGradeFourdigits':
        return Formatter_getInstance().downhillGradeFourdigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGradeTwodigits':
        return Formatter_getInstance().downhillGradeTwodigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGradeThreedigits':
        return Formatter_getInstance().downhillGradeThreedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGradeFivedigits':
        return Formatter_getInstance().downhillGradeFivedigits_3r6o6c$(value, formattingOptions);
      case 'DownhillGradeSixdigits':
        return Formatter_getInstance().downhillGradeSixdigits_3r6o6c$(value, formattingOptions);
      case 'SunsetFourdigits':
        return Formatter_getInstance().sunsetFourdigits_3r6o6c$(value, formattingOptions);
      case 'SunsetFivedigits':
        return Formatter_getInstance().sunsetFivedigits_3r6o6c$(value, formattingOptions);
      case 'SunsetSixdigits':
        return Formatter_getInstance().sunsetSixdigits_3r6o6c$(value, formattingOptions);
      case 'SunsetAccurate':
        return Formatter_getInstance().sunsetAccurate_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETAFourdigits':
        return Formatter_getInstance().navigationRouteETAFourdigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETAFivedigits':
        return Formatter_getInstance().navigationRouteETAFivedigits_3r6o6c$(value, formattingOptions);
      case 'NavigationRouteETASixdigits':
        return Formatter_getInstance().navigationRouteETASixdigits_3r6o6c$(value, formattingOptions);
      case 'SunriseFourdigits':
        return Formatter_getInstance().sunriseFourdigits_3r6o6c$(value, formattingOptions);
      case 'SunriseFivedigits':
        return Formatter_getInstance().sunriseFivedigits_3r6o6c$(value, formattingOptions);
      case 'SunriseSixdigits':
        return Formatter_getInstance().sunriseSixdigits_3r6o6c$(value, formattingOptions);
      case 'SunriseAccurate':
        return Formatter_getInstance().sunriseAccurate_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedFourdigits':
        return Formatter_getInstance().verticalSpeedFourdigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedThreedigits':
        return Formatter_getInstance().verticalSpeedThreedigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedFivedigits':
        return Formatter_getInstance().verticalSpeedFivedigits_3r6o6c$(value, formattingOptions);
      case 'VerticalSpeedSixdigits':
        return Formatter_getInstance().verticalSpeedSixdigits_3r6o6c$(value, formattingOptions);
      case 'AirPressureFourdigits':
        return Formatter_getInstance().airPressureFourdigits_3r6o6c$(value, formattingOptions);
      case 'AirPressureFivedigits':
        return Formatter_getInstance().airPressureFivedigits_3r6o6c$(value, formattingOptions);
      case 'AirPressureSixdigits':
        return Formatter_getInstance().airPressureSixdigits_3r6o6c$(value, formattingOptions);
      case 'PowerFourdigits':
        return Formatter_getInstance().powerFourdigits_3r6o6c$(value, formattingOptions);
      case 'PowerFivedigits':
        return Formatter_getInstance().powerFivedigits_3r6o6c$(value, formattingOptions);
      case 'PowerSixdigits':
        return Formatter_getInstance().powerSixdigits_3r6o6c$(value, formattingOptions);
      case 'PowerAccurate':
        return Formatter_getInstance().powerAccurate_3r6o6c$(value, formattingOptions);
      case 'AltitudeFourdigits':
        return Formatter_getInstance().altitudeFourdigits_3r6o6c$(value, formattingOptions);
      case 'AltitudeFivedigits':
        return Formatter_getInstance().altitudeFivedigits_3r6o6c$(value, formattingOptions);
      case 'AltitudeSixdigits':
        return Formatter_getInstance().altitudeSixdigits_3r6o6c$(value, formattingOptions);
      default:
        return new FormatFailure('Format name [' + formatNameWithStyle + '] not supported');
    }
  }
  function FormatResult() {
  }
  FormatResult.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FormatResult',
    interfaces: []
  };
  function FormatSuccess(icon, value, unit) {
    FormatResult.call(this);
    this.icon = icon;
    this.value = value;
    this.unit = unit;
  }
  FormatSuccess.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FormatSuccess',
    interfaces: [FormatResult]
  };
  FormatSuccess.prototype.component1 = function () {
    return this.icon;
  };
  FormatSuccess.prototype.component2 = function () {
    return this.value;
  };
  FormatSuccess.prototype.component3 = function () {
    return this.unit;
  };
  FormatSuccess.prototype.copy_tjzlyd$ = function (icon, value, unit) {
    return new FormatSuccess(icon === void 0 ? this.icon : icon, value === void 0 ? this.value : value, unit === void 0 ? this.unit : unit);
  };
  FormatSuccess.prototype.toString = function () {
    return 'FormatSuccess(icon=' + Kotlin.toString(this.icon) + (', value=' + Kotlin.toString(this.value)) + (', unit=' + Kotlin.toString(this.unit)) + ')';
  };
  FormatSuccess.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.icon) | 0;
    result = result * 31 + Kotlin.hashCode(this.value) | 0;
    result = result * 31 + Kotlin.hashCode(this.unit) | 0;
    return result;
  };
  FormatSuccess.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.icon, other.icon) && Kotlin.equals(this.value, other.value) && Kotlin.equals(this.unit, other.unit)))));
  };
  function FormatFailure(reason) {
    FormatResult.call(this);
    this.reason = reason;
  }
  FormatFailure.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FormatFailure',
    interfaces: [FormatResult]
  };
  FormatFailure.prototype.component1 = function () {
    return this.reason;
  };
  FormatFailure.prototype.copy_61zpoe$ = function (reason) {
    return new FormatFailure(reason === void 0 ? this.reason : reason);
  };
  FormatFailure.prototype.toString = function () {
    return 'FormatFailure(reason=' + Kotlin.toString(this.reason) + ')';
  };
  FormatFailure.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.reason) | 0;
    return result;
  };
  FormatFailure.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && Kotlin.equals(this.reason, other.reason))));
  };
  function FormattingOptions(measurementSystem, use24HClock) {
    if (measurementSystem === void 0)
      measurementSystem = MeasurementSystem$METRIC_getInstance();
    if (use24HClock === void 0)
      use24HClock = true;
    this.measurementSystem = measurementSystem;
    this.use24HClock = use24HClock;
  }
  FormattingOptions.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FormattingOptions',
    interfaces: []
  };
  FormattingOptions.prototype.component1 = function () {
    return this.measurementSystem;
  };
  FormattingOptions.prototype.component2 = function () {
    return this.use24HClock;
  };
  FormattingOptions.prototype.copy_ellzc3$ = function (measurementSystem, use24HClock) {
    return new FormattingOptions(measurementSystem === void 0 ? this.measurementSystem : measurementSystem, use24HClock === void 0 ? this.use24HClock : use24HClock);
  };
  FormattingOptions.prototype.toString = function () {
    return 'FormattingOptions(measurementSystem=' + Kotlin.toString(this.measurementSystem) + (', use24HClock=' + Kotlin.toString(this.use24HClock)) + ')';
  };
  FormattingOptions.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.measurementSystem) | 0;
    result = result * 31 + Kotlin.hashCode(this.use24HClock) | 0;
    return result;
  };
  FormattingOptions.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.measurementSystem, other.measurementSystem) && Kotlin.equals(this.use24HClock, other.use24HClock)))));
  };
  function Icon(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Icon_initFields() {
    Icon_initFields = function () {
    };
    Icon$SWIM_PACE_instance = new Icon('SWIM_PACE', 0);
    Icon$HEART_RATE_instance = new Icon('HEART_RATE', 1);
    Icon$AIR_PRESSURE_instance = new Icon('AIR_PRESSURE', 2);
    Icon$DOWNHILL_SPEED_instance = new Icon('DOWNHILL_SPEED', 3);
    Icon$CADENCE_instance = new Icon('CADENCE', 4);
    Icon$STROKES_instance = new Icon('STROKES', 5);
    Icon$TRAINING_EFFECT_instance = new Icon('TRAINING_EFFECT', 6);
    Icon$SWIM_DISTANCE_instance = new Icon('SWIM_DISTANCE', 7);
    Icon$COUNT_instance = new Icon('COUNT', 8);
    Icon$DOWNHILL_DURATION_instance = new Icon('DOWNHILL_DURATION', 9);
    Icon$FLIGHT_TIME_instance = new Icon('FLIGHT_TIME', 10);
    Icon$RECOVERY_TIME_instance = new Icon('RECOVERY_TIME', 11);
    Icon$ASCENT_instance = new Icon('ASCENT', 12);
    Icon$PACE_instance = new Icon('PACE', 13);
    Icon$VO2_instance = new Icon('VO2', 14);
    Icon$NAVIGATION_ROUTE_instance = new Icon('NAVIGATION_ROUTE', 15);
    Icon$STIFFNESS_instance = new Icon('STIFFNESS', 16);
    Icon$DOWNHILL_ALTITUDE_instance = new Icon('DOWNHILL_ALTITUDE', 17);
    Icon$COMPASS_HEADING_DEG_instance = new Icon('COMPASS_HEADING_DEG', 18);
    Icon$DISTANCE_instance = new Icon('DISTANCE', 19);
    Icon$DESCENT_instance = new Icon('DESCENT', 20);
    Icon$EPOC_instance = new Icon('EPOC', 21);
    Icon$STEPLENGTH_instance = new Icon('STEPLENGTH', 22);
    Icon$NAVIGATION_ETE_POI_instance = new Icon('NAVIGATION_ETE_POI', 23);
    Icon$NAVIGATION_POI_instance = new Icon('NAVIGATION_POI', 24);
    Icon$DURATION_instance = new Icon('DURATION', 25);
    Icon$DOWNHILL_DESCENT_instance = new Icon('DOWNHILL_DESCENT', 26);
    Icon$REACTIVITY_instance = new Icon('REACTIVITY', 27);
    Icon$CONTACT_TIME_instance = new Icon('CONTACT_TIME', 28);
    Icon$WEIGHT_instance = new Icon('WEIGHT', 29);
    Icon$PERFORMANCE_instance = new Icon('PERFORMANCE', 30);
    Icon$DOWNHILL_DISTANCE_instance = new Icon('DOWNHILL_DISTANCE', 31);
    Icon$TEMPERATURE_instance = new Icon('TEMPERATURE', 32);
    Icon$UNDULATION_instance = new Icon('UNDULATION', 33);
    Icon$NAVIGATION_ETE_ROUTE_instance = new Icon('NAVIGATION_ETE_ROUTE', 34);
    Icon$ROWING_PACE_instance = new Icon('ROWING_PACE', 35);
    Icon$NAUTICAL_DISTANCE_instance = new Icon('NAUTICAL_DISTANCE', 36);
    Icon$CALORIES_instance = new Icon('CALORIES', 37);
    Icon$NAVIGATION_POI_ETA_instance = new Icon('NAVIGATION_POI_ETA', 38);
    Icon$SPEED_instance = new Icon('SPEED', 39);
    Icon$HEART_RATE_PERCENTAGE_instance = new Icon('HEART_RATE_PERCENTAGE', 40);
    Icon$TIME_OF_DAY_instance = new Icon('TIME_OF_DAY', 41);
    Icon$DOWNHILL_LAP_COUNT_instance = new Icon('DOWNHILL_LAP_COUNT', 42);
    Icon$PERCENTAGE_instance = new Icon('PERCENTAGE', 43);
    Icon$VERTICAL_SPEED_instance = new Icon('VERTICAL_SPEED', 44);
    Icon$NAUTICAL_SPEED_instance = new Icon('NAUTICAL_SPEED', 45);
    Icon$SWOLF_instance = new Icon('SWOLF', 46);
    Icon$DOWNHILL_GRADE_instance = new Icon('DOWNHILL_GRADE', 47);
    Icon$SUNSET_instance = new Icon('SUNSET', 48);
    Icon$NAVIGATION_ROUTE_ETA_instance = new Icon('NAVIGATION_ROUTE_ETA', 49);
    Icon$SUNRISE_instance = new Icon('SUNRISE', 50);
    Icon$POWER_instance = new Icon('POWER', 51);
    Icon$ALTITUDE_instance = new Icon('ALTITUDE', 52);
  }
  var Icon$SWIM_PACE_instance;
  function Icon$SWIM_PACE_getInstance() {
    Icon_initFields();
    return Icon$SWIM_PACE_instance;
  }
  var Icon$HEART_RATE_instance;
  function Icon$HEART_RATE_getInstance() {
    Icon_initFields();
    return Icon$HEART_RATE_instance;
  }
  var Icon$AIR_PRESSURE_instance;
  function Icon$AIR_PRESSURE_getInstance() {
    Icon_initFields();
    return Icon$AIR_PRESSURE_instance;
  }
  var Icon$DOWNHILL_SPEED_instance;
  function Icon$DOWNHILL_SPEED_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_SPEED_instance;
  }
  var Icon$CADENCE_instance;
  function Icon$CADENCE_getInstance() {
    Icon_initFields();
    return Icon$CADENCE_instance;
  }
  var Icon$STROKES_instance;
  function Icon$STROKES_getInstance() {
    Icon_initFields();
    return Icon$STROKES_instance;
  }
  var Icon$TRAINING_EFFECT_instance;
  function Icon$TRAINING_EFFECT_getInstance() {
    Icon_initFields();
    return Icon$TRAINING_EFFECT_instance;
  }
  var Icon$SWIM_DISTANCE_instance;
  function Icon$SWIM_DISTANCE_getInstance() {
    Icon_initFields();
    return Icon$SWIM_DISTANCE_instance;
  }
  var Icon$COUNT_instance;
  function Icon$COUNT_getInstance() {
    Icon_initFields();
    return Icon$COUNT_instance;
  }
  var Icon$DOWNHILL_DURATION_instance;
  function Icon$DOWNHILL_DURATION_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_DURATION_instance;
  }
  var Icon$FLIGHT_TIME_instance;
  function Icon$FLIGHT_TIME_getInstance() {
    Icon_initFields();
    return Icon$FLIGHT_TIME_instance;
  }
  var Icon$RECOVERY_TIME_instance;
  function Icon$RECOVERY_TIME_getInstance() {
    Icon_initFields();
    return Icon$RECOVERY_TIME_instance;
  }
  var Icon$ASCENT_instance;
  function Icon$ASCENT_getInstance() {
    Icon_initFields();
    return Icon$ASCENT_instance;
  }
  var Icon$PACE_instance;
  function Icon$PACE_getInstance() {
    Icon_initFields();
    return Icon$PACE_instance;
  }
  var Icon$VO2_instance;
  function Icon$VO2_getInstance() {
    Icon_initFields();
    return Icon$VO2_instance;
  }
  var Icon$NAVIGATION_ROUTE_instance;
  function Icon$NAVIGATION_ROUTE_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_ROUTE_instance;
  }
  var Icon$STIFFNESS_instance;
  function Icon$STIFFNESS_getInstance() {
    Icon_initFields();
    return Icon$STIFFNESS_instance;
  }
  var Icon$DOWNHILL_ALTITUDE_instance;
  function Icon$DOWNHILL_ALTITUDE_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_ALTITUDE_instance;
  }
  var Icon$COMPASS_HEADING_DEG_instance;
  function Icon$COMPASS_HEADING_DEG_getInstance() {
    Icon_initFields();
    return Icon$COMPASS_HEADING_DEG_instance;
  }
  var Icon$DISTANCE_instance;
  function Icon$DISTANCE_getInstance() {
    Icon_initFields();
    return Icon$DISTANCE_instance;
  }
  var Icon$DESCENT_instance;
  function Icon$DESCENT_getInstance() {
    Icon_initFields();
    return Icon$DESCENT_instance;
  }
  var Icon$EPOC_instance;
  function Icon$EPOC_getInstance() {
    Icon_initFields();
    return Icon$EPOC_instance;
  }
  var Icon$STEPLENGTH_instance;
  function Icon$STEPLENGTH_getInstance() {
    Icon_initFields();
    return Icon$STEPLENGTH_instance;
  }
  var Icon$NAVIGATION_ETE_POI_instance;
  function Icon$NAVIGATION_ETE_POI_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_ETE_POI_instance;
  }
  var Icon$NAVIGATION_POI_instance;
  function Icon$NAVIGATION_POI_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_POI_instance;
  }
  var Icon$DURATION_instance;
  function Icon$DURATION_getInstance() {
    Icon_initFields();
    return Icon$DURATION_instance;
  }
  var Icon$DOWNHILL_DESCENT_instance;
  function Icon$DOWNHILL_DESCENT_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_DESCENT_instance;
  }
  var Icon$REACTIVITY_instance;
  function Icon$REACTIVITY_getInstance() {
    Icon_initFields();
    return Icon$REACTIVITY_instance;
  }
  var Icon$CONTACT_TIME_instance;
  function Icon$CONTACT_TIME_getInstance() {
    Icon_initFields();
    return Icon$CONTACT_TIME_instance;
  }
  var Icon$WEIGHT_instance;
  function Icon$WEIGHT_getInstance() {
    Icon_initFields();
    return Icon$WEIGHT_instance;
  }
  var Icon$PERFORMANCE_instance;
  function Icon$PERFORMANCE_getInstance() {
    Icon_initFields();
    return Icon$PERFORMANCE_instance;
  }
  var Icon$DOWNHILL_DISTANCE_instance;
  function Icon$DOWNHILL_DISTANCE_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_DISTANCE_instance;
  }
  var Icon$TEMPERATURE_instance;
  function Icon$TEMPERATURE_getInstance() {
    Icon_initFields();
    return Icon$TEMPERATURE_instance;
  }
  var Icon$UNDULATION_instance;
  function Icon$UNDULATION_getInstance() {
    Icon_initFields();
    return Icon$UNDULATION_instance;
  }
  var Icon$NAVIGATION_ETE_ROUTE_instance;
  function Icon$NAVIGATION_ETE_ROUTE_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_ETE_ROUTE_instance;
  }
  var Icon$ROWING_PACE_instance;
  function Icon$ROWING_PACE_getInstance() {
    Icon_initFields();
    return Icon$ROWING_PACE_instance;
  }
  var Icon$NAUTICAL_DISTANCE_instance;
  function Icon$NAUTICAL_DISTANCE_getInstance() {
    Icon_initFields();
    return Icon$NAUTICAL_DISTANCE_instance;
  }
  var Icon$CALORIES_instance;
  function Icon$CALORIES_getInstance() {
    Icon_initFields();
    return Icon$CALORIES_instance;
  }
  var Icon$NAVIGATION_POI_ETA_instance;
  function Icon$NAVIGATION_POI_ETA_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_POI_ETA_instance;
  }
  var Icon$SPEED_instance;
  function Icon$SPEED_getInstance() {
    Icon_initFields();
    return Icon$SPEED_instance;
  }
  var Icon$HEART_RATE_PERCENTAGE_instance;
  function Icon$HEART_RATE_PERCENTAGE_getInstance() {
    Icon_initFields();
    return Icon$HEART_RATE_PERCENTAGE_instance;
  }
  var Icon$TIME_OF_DAY_instance;
  function Icon$TIME_OF_DAY_getInstance() {
    Icon_initFields();
    return Icon$TIME_OF_DAY_instance;
  }
  var Icon$DOWNHILL_LAP_COUNT_instance;
  function Icon$DOWNHILL_LAP_COUNT_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_LAP_COUNT_instance;
  }
  var Icon$PERCENTAGE_instance;
  function Icon$PERCENTAGE_getInstance() {
    Icon_initFields();
    return Icon$PERCENTAGE_instance;
  }
  var Icon$VERTICAL_SPEED_instance;
  function Icon$VERTICAL_SPEED_getInstance() {
    Icon_initFields();
    return Icon$VERTICAL_SPEED_instance;
  }
  var Icon$NAUTICAL_SPEED_instance;
  function Icon$NAUTICAL_SPEED_getInstance() {
    Icon_initFields();
    return Icon$NAUTICAL_SPEED_instance;
  }
  var Icon$SWOLF_instance;
  function Icon$SWOLF_getInstance() {
    Icon_initFields();
    return Icon$SWOLF_instance;
  }
  var Icon$DOWNHILL_GRADE_instance;
  function Icon$DOWNHILL_GRADE_getInstance() {
    Icon_initFields();
    return Icon$DOWNHILL_GRADE_instance;
  }
  var Icon$SUNSET_instance;
  function Icon$SUNSET_getInstance() {
    Icon_initFields();
    return Icon$SUNSET_instance;
  }
  var Icon$NAVIGATION_ROUTE_ETA_instance;
  function Icon$NAVIGATION_ROUTE_ETA_getInstance() {
    Icon_initFields();
    return Icon$NAVIGATION_ROUTE_ETA_instance;
  }
  var Icon$SUNRISE_instance;
  function Icon$SUNRISE_getInstance() {
    Icon_initFields();
    return Icon$SUNRISE_instance;
  }
  var Icon$POWER_instance;
  function Icon$POWER_getInstance() {
    Icon_initFields();
    return Icon$POWER_instance;
  }
  var Icon$ALTITUDE_instance;
  function Icon$ALTITUDE_getInstance() {
    Icon_initFields();
    return Icon$ALTITUDE_instance;
  }
  Icon.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Icon',
    interfaces: [Enum]
  };
  function Icon$values() {
    return [Icon$SWIM_PACE_getInstance(), Icon$HEART_RATE_getInstance(), Icon$AIR_PRESSURE_getInstance(), Icon$DOWNHILL_SPEED_getInstance(), Icon$CADENCE_getInstance(), Icon$STROKES_getInstance(), Icon$TRAINING_EFFECT_getInstance(), Icon$SWIM_DISTANCE_getInstance(), Icon$COUNT_getInstance(), Icon$DOWNHILL_DURATION_getInstance(), Icon$FLIGHT_TIME_getInstance(), Icon$RECOVERY_TIME_getInstance(), Icon$ASCENT_getInstance(), Icon$PACE_getInstance(), Icon$VO2_getInstance(), Icon$NAVIGATION_ROUTE_getInstance(), Icon$STIFFNESS_getInstance(), Icon$DOWNHILL_ALTITUDE_getInstance(), Icon$COMPASS_HEADING_DEG_getInstance(), Icon$DISTANCE_getInstance(), Icon$DESCENT_getInstance(), Icon$EPOC_getInstance(), Icon$STEPLENGTH_getInstance(), Icon$NAVIGATION_ETE_POI_getInstance(), Icon$NAVIGATION_POI_getInstance(), Icon$DURATION_getInstance(), Icon$DOWNHILL_DESCENT_getInstance(), Icon$REACTIVITY_getInstance(), Icon$CONTACT_TIME_getInstance(), Icon$WEIGHT_getInstance(), Icon$PERFORMANCE_getInstance(), Icon$DOWNHILL_DISTANCE_getInstance(), Icon$TEMPERATURE_getInstance(), Icon$UNDULATION_getInstance(), Icon$NAVIGATION_ETE_ROUTE_getInstance(), Icon$ROWING_PACE_getInstance(), Icon$NAUTICAL_DISTANCE_getInstance(), Icon$CALORIES_getInstance(), Icon$NAVIGATION_POI_ETA_getInstance(), Icon$SPEED_getInstance(), Icon$HEART_RATE_PERCENTAGE_getInstance(), Icon$TIME_OF_DAY_getInstance(), Icon$DOWNHILL_LAP_COUNT_getInstance(), Icon$PERCENTAGE_getInstance(), Icon$VERTICAL_SPEED_getInstance(), Icon$NAUTICAL_SPEED_getInstance(), Icon$SWOLF_getInstance(), Icon$DOWNHILL_GRADE_getInstance(), Icon$SUNSET_getInstance(), Icon$NAVIGATION_ROUTE_ETA_getInstance(), Icon$SUNRISE_getInstance(), Icon$POWER_getInstance(), Icon$ALTITUDE_getInstance()];
  }
  Icon.values = Icon$values;
  function Icon$valueOf(name) {
    switch (name) {
      case 'SWIM_PACE':
        return Icon$SWIM_PACE_getInstance();
      case 'HEART_RATE':
        return Icon$HEART_RATE_getInstance();
      case 'AIR_PRESSURE':
        return Icon$AIR_PRESSURE_getInstance();
      case 'DOWNHILL_SPEED':
        return Icon$DOWNHILL_SPEED_getInstance();
      case 'CADENCE':
        return Icon$CADENCE_getInstance();
      case 'STROKES':
        return Icon$STROKES_getInstance();
      case 'TRAINING_EFFECT':
        return Icon$TRAINING_EFFECT_getInstance();
      case 'SWIM_DISTANCE':
        return Icon$SWIM_DISTANCE_getInstance();
      case 'COUNT':
        return Icon$COUNT_getInstance();
      case 'DOWNHILL_DURATION':
        return Icon$DOWNHILL_DURATION_getInstance();
      case 'FLIGHT_TIME':
        return Icon$FLIGHT_TIME_getInstance();
      case 'RECOVERY_TIME':
        return Icon$RECOVERY_TIME_getInstance();
      case 'ASCENT':
        return Icon$ASCENT_getInstance();
      case 'PACE':
        return Icon$PACE_getInstance();
      case 'VO2':
        return Icon$VO2_getInstance();
      case 'NAVIGATION_ROUTE':
        return Icon$NAVIGATION_ROUTE_getInstance();
      case 'STIFFNESS':
        return Icon$STIFFNESS_getInstance();
      case 'DOWNHILL_ALTITUDE':
        return Icon$DOWNHILL_ALTITUDE_getInstance();
      case 'COMPASS_HEADING_DEG':
        return Icon$COMPASS_HEADING_DEG_getInstance();
      case 'DISTANCE':
        return Icon$DISTANCE_getInstance();
      case 'DESCENT':
        return Icon$DESCENT_getInstance();
      case 'EPOC':
        return Icon$EPOC_getInstance();
      case 'STEPLENGTH':
        return Icon$STEPLENGTH_getInstance();
      case 'NAVIGATION_ETE_POI':
        return Icon$NAVIGATION_ETE_POI_getInstance();
      case 'NAVIGATION_POI':
        return Icon$NAVIGATION_POI_getInstance();
      case 'DURATION':
        return Icon$DURATION_getInstance();
      case 'DOWNHILL_DESCENT':
        return Icon$DOWNHILL_DESCENT_getInstance();
      case 'REACTIVITY':
        return Icon$REACTIVITY_getInstance();
      case 'CONTACT_TIME':
        return Icon$CONTACT_TIME_getInstance();
      case 'WEIGHT':
        return Icon$WEIGHT_getInstance();
      case 'PERFORMANCE':
        return Icon$PERFORMANCE_getInstance();
      case 'DOWNHILL_DISTANCE':
        return Icon$DOWNHILL_DISTANCE_getInstance();
      case 'TEMPERATURE':
        return Icon$TEMPERATURE_getInstance();
      case 'UNDULATION':
        return Icon$UNDULATION_getInstance();
      case 'NAVIGATION_ETE_ROUTE':
        return Icon$NAVIGATION_ETE_ROUTE_getInstance();
      case 'ROWING_PACE':
        return Icon$ROWING_PACE_getInstance();
      case 'NAUTICAL_DISTANCE':
        return Icon$NAUTICAL_DISTANCE_getInstance();
      case 'CALORIES':
        return Icon$CALORIES_getInstance();
      case 'NAVIGATION_POI_ETA':
        return Icon$NAVIGATION_POI_ETA_getInstance();
      case 'SPEED':
        return Icon$SPEED_getInstance();
      case 'HEART_RATE_PERCENTAGE':
        return Icon$HEART_RATE_PERCENTAGE_getInstance();
      case 'TIME_OF_DAY':
        return Icon$TIME_OF_DAY_getInstance();
      case 'DOWNHILL_LAP_COUNT':
        return Icon$DOWNHILL_LAP_COUNT_getInstance();
      case 'PERCENTAGE':
        return Icon$PERCENTAGE_getInstance();
      case 'VERTICAL_SPEED':
        return Icon$VERTICAL_SPEED_getInstance();
      case 'NAUTICAL_SPEED':
        return Icon$NAUTICAL_SPEED_getInstance();
      case 'SWOLF':
        return Icon$SWOLF_getInstance();
      case 'DOWNHILL_GRADE':
        return Icon$DOWNHILL_GRADE_getInstance();
      case 'SUNSET':
        return Icon$SUNSET_getInstance();
      case 'NAVIGATION_ROUTE_ETA':
        return Icon$NAVIGATION_ROUTE_ETA_getInstance();
      case 'SUNRISE':
        return Icon$SUNRISE_getInstance();
      case 'POWER':
        return Icon$POWER_getInstance();
      case 'ALTITUDE':
        return Icon$ALTITUDE_getInstance();
      default:
        throwISE('No enum constant com.suunto.sim.formatting.Icon.' + name);
    }
  }
  Icon.valueOf_61zpoe$ = Icon$valueOf;
  function MeasurementSystem(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function MeasurementSystem_initFields() {
    MeasurementSystem_initFields = function () {
    };
    MeasurementSystem$METRIC_instance = new MeasurementSystem('METRIC', 0);
    MeasurementSystem$IMPERIAL_instance = new MeasurementSystem('IMPERIAL', 1);
  }
  var MeasurementSystem$METRIC_instance;
  function MeasurementSystem$METRIC_getInstance() {
    MeasurementSystem_initFields();
    return MeasurementSystem$METRIC_instance;
  }
  var MeasurementSystem$IMPERIAL_instance;
  function MeasurementSystem$IMPERIAL_getInstance() {
    MeasurementSystem_initFields();
    return MeasurementSystem$IMPERIAL_instance;
  }
  MeasurementSystem.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'MeasurementSystem',
    interfaces: [Enum]
  };
  function MeasurementSystem$values() {
    return [MeasurementSystem$METRIC_getInstance(), MeasurementSystem$IMPERIAL_getInstance()];
  }
  MeasurementSystem.values = MeasurementSystem$values;
  function MeasurementSystem$valueOf(name) {
    switch (name) {
      case 'METRIC':
        return MeasurementSystem$METRIC_getInstance();
      case 'IMPERIAL':
        return MeasurementSystem$IMPERIAL_getInstance();
      default:
        throwISE('No enum constant com.suunto.sim.formatting.MeasurementSystem.' + name);
    }
  }
  MeasurementSystem.valueOf_61zpoe$ = MeasurementSystem$valueOf;
  function Method(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Method_initFields() {
    Method_initFields = function () {
    };
    Method$FLOOR_instance = new Method('FLOOR', 0);
    Method$ROUND_instance = new Method('ROUND', 1);
    Method$TRUNCATE_instance = new Method('TRUNCATE', 2);
  }
  var Method$FLOOR_instance;
  function Method$FLOOR_getInstance() {
    Method_initFields();
    return Method$FLOOR_instance;
  }
  var Method$ROUND_instance;
  function Method$ROUND_getInstance() {
    Method_initFields();
    return Method$ROUND_instance;
  }
  var Method$TRUNCATE_instance;
  function Method$TRUNCATE_getInstance() {
    Method_initFields();
    return Method$TRUNCATE_instance;
  }
  Method.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Method',
    interfaces: [Enum]
  };
  function Method$values() {
    return [Method$FLOOR_getInstance(), Method$ROUND_getInstance(), Method$TRUNCATE_getInstance()];
  }
  Method.values = Method$values;
  function Method$valueOf(name) {
    switch (name) {
      case 'FLOOR':
        return Method$FLOOR_getInstance();
      case 'ROUND':
        return Method$ROUND_getInstance();
      case 'TRUNCATE':
        return Method$TRUNCATE_getInstance();
      default:
        throwISE('No enum constant com.suunto.sim.formatting.Method.' + name);
    }
  }
  Method.valueOf_61zpoe$ = Method$valueOf;
  function Unit(id, dimension, isBase) {
    this.id_prwifw$_0 = id;
    this.dimension_jjtlgp$_0 = dimension;
    this.isBase_yoe02k$_0 = isBase;
  }
  Object.defineProperty(Unit.prototype, 'id', {
    get: function () {
      return this.id_prwifw$_0;
    }
  });
  Object.defineProperty(Unit.prototype, 'dimension', {
    get: function () {
      return this.dimension_jjtlgp$_0;
    }
  });
  Object.defineProperty(Unit.prototype, 'isBase', {
    get: function () {
      return this.isBase_yoe02k$_0;
    }
  });
  Unit.prototype.convert = function (value, toUnit) {
    if (this.dimension !== toUnit.dimension) {
      return new ConversionFailure('Cannot convert from unit ' + this.id + ' with dimension ' + this.dimension + ' to unit ' + toUnit.id + ' with ' + toUnit.dimension + '.');
    } else if (isNaN_0(value)) {
      return new ConversionFailure('Cannot convert an undefined value.');
    } else {
      var valueInBaseUnit = this.convertToBase_14dthe$(value);
      var result = toUnit.convertFromBase_14dthe$(valueInBaseUnit);
      return new ConversionSuccess(result, toUnit);
    }
  };
  Unit.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Unit',
    interfaces: []
  };
  function TemperatureConversionUnit(id, isBase) {
    if (isBase === void 0)
      isBase = false;
    Unit.call(this, id, Dimension$TEMPERATURE_getInstance(), isBase);
    this.id_uaz7rc$_0 = id;
    this.isBase_81zjig$_0 = isBase;
  }
  Object.defineProperty(TemperatureConversionUnit.prototype, 'id', {
    get: function () {
      return this.id_uaz7rc$_0;
    }
  });
  Object.defineProperty(TemperatureConversionUnit.prototype, 'isBase', {
    get: function () {
      return this.isBase_81zjig$_0;
    }
  });
  TemperatureConversionUnit.prototype.convertToBase_14dthe$ = function (value) {
    if (this.isBase) {
      return value;
    } else {
      if (equals(this, Units_getInstance().F))
        return (value - 32.0) * (5.0 / 9.0) + 273.15;
      else if (equals(this, Units_getInstance().C))
        return value + 273.15;
      else if (equals(this, Units_getInstance().K))
        return value;
      else
        throw IllegalArgumentException_init('Trying to convert from base temperature unit ' + this.id + ' ');
    }
  };
  TemperatureConversionUnit.prototype.convertFromBase_14dthe$ = function (value) {
    if (this.isBase) {
      return value;
    } else {
      if (equals(this, Units_getInstance().F))
        return (value - 273.15) * (9.0 / 5.0) + 32;
      else if (equals(this, Units_getInstance().C))
        return value - 273.15;
      else if (equals(this, Units_getInstance().K))
        return value;
      else
        throw IllegalArgumentException_init('Trying to convert to base temperature unit ' + this.id + ' ');
    }
  };
  TemperatureConversionUnit.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'TemperatureConversionUnit',
    interfaces: [Unit]
  };
  TemperatureConversionUnit.prototype.component1 = function () {
    return this.id;
  };
  TemperatureConversionUnit.prototype.component2_8be2vx$ = function () {
    return this.isBase;
  };
  TemperatureConversionUnit.prototype.copy_ivxn3r$ = function (id, isBase) {
    return new TemperatureConversionUnit(id === void 0 ? this.id : id, isBase === void 0 ? this.isBase : isBase);
  };
  TemperatureConversionUnit.prototype.toString = function () {
    return 'TemperatureConversionUnit(id=' + Kotlin.toString(this.id) + (', isBase=' + Kotlin.toString(this.isBase)) + ')';
  };
  TemperatureConversionUnit.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.id) | 0;
    result = result * 31 + Kotlin.hashCode(this.isBase) | 0;
    return result;
  };
  TemperatureConversionUnit.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.id, other.id) && Kotlin.equals(this.isBase, other.isBase)))));
  };
  function FactorialConversionUnit(id, dimension, factor, isBase, isInverted) {
    if (isBase === void 0)
      isBase = false;
    if (isInverted === void 0)
      isInverted = false;
    Unit.call(this, id, dimension, isBase);
    this.id_t3l25l$_0 = id;
    this.dimension_aed5cm$_0 = dimension;
    this.factor = factor;
    this.isBase_xtq6s7$_0 = isBase;
    this.isInverted = isInverted;
  }
  Object.defineProperty(FactorialConversionUnit.prototype, 'id', {
    get: function () {
      return this.id_t3l25l$_0;
    }
  });
  Object.defineProperty(FactorialConversionUnit.prototype, 'dimension', {
    get: function () {
      return this.dimension_aed5cm$_0;
    }
  });
  Object.defineProperty(FactorialConversionUnit.prototype, 'isBase', {
    get: function () {
      return this.isBase_xtq6s7$_0;
    }
  });
  FactorialConversionUnit.prototype.convertToBase_14dthe$ = function (value) {
    if (this.isBase) {
      return value;
    } else {
      var conversionValue = value / this.factor;
      if (this.isInverted) {
        conversionValue = 1.0 / conversionValue;
      }
      return conversionValue;
    }
  };
  FactorialConversionUnit.prototype.convertFromBase_14dthe$ = function (value) {
    var tmp$;
    if (this.isBase) {
      return value;
    } else {
      if (this.isInverted) {
        tmp$ = value / this.factor;
      } else {
        tmp$ = value * this.factor;
      }
      var conversionValue = tmp$;
      if (this.isInverted) {
        conversionValue = 1.0 / conversionValue;
      }
      return conversionValue;
    }
  };
  FactorialConversionUnit.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FactorialConversionUnit',
    interfaces: [Unit]
  };
  FactorialConversionUnit.prototype.component1 = function () {
    return this.id;
  };
  FactorialConversionUnit.prototype.component2_8be2vx$ = function () {
    return this.dimension;
  };
  FactorialConversionUnit.prototype.component3 = function () {
    return this.factor;
  };
  FactorialConversionUnit.prototype.component4_8be2vx$ = function () {
    return this.isBase;
  };
  FactorialConversionUnit.prototype.component5 = function () {
    return this.isInverted;
  };
  FactorialConversionUnit.prototype.copy_o6pphc$ = function (id, dimension, factor, isBase, isInverted) {
    return new FactorialConversionUnit(id === void 0 ? this.id : id, dimension === void 0 ? this.dimension : dimension, factor === void 0 ? this.factor : factor, isBase === void 0 ? this.isBase : isBase, isInverted === void 0 ? this.isInverted : isInverted);
  };
  FactorialConversionUnit.prototype.toString = function () {
    return 'FactorialConversionUnit(id=' + Kotlin.toString(this.id) + (', dimension=' + Kotlin.toString(this.dimension)) + (', factor=' + Kotlin.toString(this.factor)) + (', isBase=' + Kotlin.toString(this.isBase)) + (', isInverted=' + Kotlin.toString(this.isInverted)) + ')';
  };
  FactorialConversionUnit.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.id) | 0;
    result = result * 31 + Kotlin.hashCode(this.dimension) | 0;
    result = result * 31 + Kotlin.hashCode(this.factor) | 0;
    result = result * 31 + Kotlin.hashCode(this.isBase) | 0;
    result = result * 31 + Kotlin.hashCode(this.isInverted) | 0;
    return result;
  };
  FactorialConversionUnit.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.id, other.id) && Kotlin.equals(this.dimension, other.dimension) && Kotlin.equals(this.factor, other.factor) && Kotlin.equals(this.isBase, other.isBase) && Kotlin.equals(this.isInverted, other.isInverted)))));
  };
  function Units() {
    Units_instance = this;
    this.M = new FactorialConversionUnit('M', Dimension$LENGTH_getInstance(), 1.0, true);
    this.KM = new FactorialConversionUnit('KM', Dimension$LENGTH_getInstance(), 0.001);
    this.CM = new FactorialConversionUnit('CM', Dimension$LENGTH_getInstance(), 100.0);
    this.FT = new FactorialConversionUnit('FT', Dimension$LENGTH_getInstance(), 3.280839895013123);
    this.MI = new FactorialConversionUnit('MI', Dimension$LENGTH_getInstance(), 6.213711922373339E-4);
    this.KFT = new FactorialConversionUnit('KFT', Dimension$LENGTH_getInstance(), 0.0032808398950131233);
    this.YD = new FactorialConversionUnit('YD', Dimension$LENGTH_getInstance(), 1.0936132983377078);
    this.NMI = new FactorialConversionUnit('NMI', Dimension$LENGTH_getInstance(), 5.399568034557235E-4);
    this.K = new TemperatureConversionUnit('K', true);
    this.C = new TemperatureConversionUnit('C');
    this.F = new TemperatureConversionUnit('F');
    this.M_PER_S = new FactorialConversionUnit('M_PER_S', Dimension$VELOCITY_getInstance(), 1.0, true);
    this.M_PER_H = new FactorialConversionUnit('M_PER_H', Dimension$VELOCITY_getInstance(), 3600.0);
    this.M_PER_MIN = new FactorialConversionUnit('M_PER_MIN', Dimension$VELOCITY_getInstance(), 60.0);
    this.KM_PER_H = new FactorialConversionUnit('KM_PER_H', Dimension$VELOCITY_getInstance(), 3.6);
    this.FT_PER_MIN = new FactorialConversionUnit('FT_PER_MIN', Dimension$VELOCITY_getInstance(), 196.85039370078738);
    this.FT_PER_H = new FactorialConversionUnit('FT_PER_H', Dimension$VELOCITY_getInstance(), 11811.023622047243);
    this.MI_PER_H = new FactorialConversionUnit('MI_PER_H', Dimension$VELOCITY_getInstance(), 2.2369362920544025);
    this.KN = new FactorialConversionUnit('KN', Dimension$VELOCITY_getInstance(), 1.943844494119952);
    this.S_PER_M = new FactorialConversionUnit('S_PER_M', Dimension$VELOCITY_getInstance(), 1.0, void 0, true);
    this.S_PER_KM = new FactorialConversionUnit('S_PER_KM', Dimension$VELOCITY_getInstance(), 1000.0, void 0, true);
    this.S_PER_100M = new FactorialConversionUnit('S_PER_100M', Dimension$VELOCITY_getInstance(), 100.0, void 0, true);
    this.S_PER_500M = new FactorialConversionUnit('S_PER_500M', Dimension$VELOCITY_getInstance(), 500.0, void 0, true);
    this.S_PER_MI = new FactorialConversionUnit('S_PER_MI', Dimension$VELOCITY_getInstance(), 1609.3440000000103, void 0, true);
    this.S_PER_100YD = new FactorialConversionUnit('S_PER_100YD', Dimension$VELOCITY_getInstance(), 91.44000000000064, void 0, true);
    this.PA = new FactorialConversionUnit('PA', Dimension$PRESSURE_getInstance(), 1.0, true);
    this.KPA = new FactorialConversionUnit('KPA', Dimension$PRESSURE_getInstance(), 0.001);
    this.HPA = new FactorialConversionUnit('HPA', Dimension$PRESSURE_getInstance(), 0.01);
    this.BAR = new FactorialConversionUnit('BAR', Dimension$PRESSURE_getInstance(), 1.0E-5);
    this.PSI = new FactorialConversionUnit('PSI', Dimension$PRESSURE_getInstance(), 1.450377E-4);
    this.INHG = new FactorialConversionUnit('INHG', Dimension$PRESSURE_getInstance(), 2.9529983E-4);
    this.J = new FactorialConversionUnit('J', Dimension$ENERGY_getInstance(), 1.0, true);
    this.KNM = new FactorialConversionUnit('KNM', Dimension$ENERGY_getInstance(), 0.001);
    this.KCAL = new FactorialConversionUnit('KCAL', Dimension$ENERGY_getInstance(), 2.390057361E-4);
    this.W = new FactorialConversionUnit('W', Dimension$POWER_getInstance(), 1.0, true);
    this.HZ = new FactorialConversionUnit('HZ', Dimension$FREQUENCY_getInstance(), 1.0, true);
    this.BPM = new FactorialConversionUnit('BPM', Dimension$FREQUENCY_getInstance(), 60.0);
    this.RPM = new FactorialConversionUnit('RPM', Dimension$FREQUENCY_getInstance(), 60.0);
    this.SPM = new FactorialConversionUnit('SPM', Dimension$FREQUENCY_getInstance(), 60.0);
    this.M3_PER_S = new FactorialConversionUnit('M3_PER_S', Dimension$VOLUMETRIC_FLOW_getInstance(), 1.0, true);
    this.L_PER_MIN = new FactorialConversionUnit('L_PER_MIN', Dimension$VOLUMETRIC_FLOW_getInstance(), 60000.0);
    this.FT3_PER_MIN = new FactorialConversionUnit('FT3_PER_MIN', Dimension$VOLUMETRIC_FLOW_getInstance(), 2118.880003289);
    this.RAD = new FactorialConversionUnit('RAD', Dimension$ANGLE_getInstance(), 1.0, true);
    this.DEG = new FactorialConversionUnit('DEG', Dimension$ANGLE_getInstance(), 57.295779513082);
    this.MIL = new FactorialConversionUnit('MIL', Dimension$ANGLE_getInstance(), 1018.5916357881);
    this.KG = new FactorialConversionUnit('KG', Dimension$WEIGHT_getInstance(), 1.0, true);
    this.LB = new FactorialConversionUnit('LB', Dimension$WEIGHT_getInstance(), 2.20462262185);
    this.SEC = new FactorialConversionUnit('SEC', Dimension$TIME_getInstance(), 1.0, true);
    this.MIN = new FactorialConversionUnit('MIN', Dimension$TIME_getInstance(), 1 / 60.0);
    this.HOUR = new FactorialConversionUnit('HOUR', Dimension$TIME_getInstance(), 1 / 3600.0);
    this.DAY = new FactorialConversionUnit('DAY', Dimension$TIME_getInstance(), 1 / 86400.0);
    this.MS = new FactorialConversionUnit('MS', Dimension$TIME_getInstance(), 1000.0);
    this.AM = new FactorialConversionUnit('AM', Dimension$OTHER_getInstance(), 1.0, true);
    this.PM = new FactorialConversionUnit('PM', Dimension$OTHER_getInstance(), 1.0, true);
    this.NONE = new FactorialConversionUnit('NONE', Dimension$OTHER_getInstance(), 1.0, true);
    this.PER_M = new FactorialConversionUnit('PER_M', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PER_100M = new FactorialConversionUnit('PER_100M', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PER_500M = new FactorialConversionUnit('PER_500M', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PER_KM = new FactorialConversionUnit('PER_KM', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PER_100YD = new FactorialConversionUnit('PER_100YD', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PER_MI = new FactorialConversionUnit('PER_MI', Dimension$OTHER_getInstance(), 1.0, true, true);
    this.PERCENT = new FactorialConversionUnit('PERCENT', Dimension$OTHER_getInstance(), 1.0, true);
    this.SCALAR = new FactorialConversionUnit('SCALAR', Dimension$OTHER_getInstance(), 1.0, true);
  }
  Units.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Units',
    interfaces: []
  };
  var Units_instance = null;
  function Units_getInstance() {
    if (Units_instance === null) {
      new Units();
    }
    return Units_instance;
  }
  function BaseFormatter() {
  }
  function BaseFormatter$ConversionData(transformedValue, unit, range) {
    this.transformedValue = transformedValue;
    this.unit = unit;
    this.range = range;
  }
  BaseFormatter$ConversionData.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ConversionData',
    interfaces: []
  };
  BaseFormatter$ConversionData.prototype.component1 = function () {
    return this.transformedValue;
  };
  BaseFormatter$ConversionData.prototype.component2 = function () {
    return this.unit;
  };
  BaseFormatter$ConversionData.prototype.component3 = function () {
    return this.range;
  };
  BaseFormatter$ConversionData.prototype.copy_9fr5ba$ = function (transformedValue, unit, range) {
    return new BaseFormatter$ConversionData(transformedValue === void 0 ? this.transformedValue : transformedValue, unit === void 0 ? this.unit : unit, range === void 0 ? this.range : range);
  };
  BaseFormatter$ConversionData.prototype.toString = function () {
    return 'ConversionData(transformedValue=' + Kotlin.toString(this.transformedValue) + (', unit=' + Kotlin.toString(this.unit)) + (', range=' + Kotlin.toString(this.range)) + ')';
  };
  BaseFormatter$ConversionData.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.transformedValue) | 0;
    result = result * 31 + Kotlin.hashCode(this.unit) | 0;
    result = result * 31 + Kotlin.hashCode(this.range) | 0;
    return result;
  };
  BaseFormatter$ConversionData.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.transformedValue, other.transformedValue) && Kotlin.equals(this.unit, other.unit) && Kotlin.equals(this.range, other.range)))));
  };
  BaseFormatter.prototype.formatValue_fqspz6$ = function (formattingOptions, icon, value, valueUnit, rangeUnit, ranges) {
    var tmp$;
    try {
      tmp$ = this.applyUnitConversions_0(formattingOptions, value, valueUnit, rangeUnit, ranges);
      if (tmp$ == null) {
        return new FormatFailure('out of formatter range');
      }
    } catch (e) {
      if (Kotlin.isType(e, IllegalArgumentException)) {
        return new FormatFailure('Error while converting units. Reason: ' + toString(e.message));
      } else
        throw e;
    }
    var convertedValue = tmp$;
    var formattedValueAsString = convertedValue.range.format_qfvghl$(convertedValue.transformedValue, convertedValue.unit);
    return new FormatSuccess(icon, formattedValueAsString, convertedValue.unit);
  };
  BaseFormatter.prototype.applyUnitConversions_0 = function (formattingOptions, value, valueUnit, rangeUnit, ranges) {
    var tmp$, tmp$_0, tmp$_1, tmp$_2, tmp$_3;
    var tempValue = value;
    var tempValueUnit = valueUnit;
    if (!equals(tempValueUnit, rangeUnit)) {
      var result = tempValueUnit.convert(value, rangeUnit);
      if (Kotlin.isType(result, ConversionSuccess))
        tmp$ = result.value;
      else if (Kotlin.isType(result, ConversionFailure))
        throw IllegalArgumentException_init(result.reason);
      else
        tmp$ = Kotlin.noWhenBranchMatched();
      tempValue = tmp$;
      tempValueUnit = rangeUnit;
    }
    tmp$_0 = this.findRange_0(tempValue, ranges);
    if (tmp$_0 == null) {
      return null;
    }
    var range = tmp$_0;
    if (!equals(tempValueUnit, range.unit)) {
      var result_0 = tempValueUnit.convert(tempValue, range.unit);
      if (Kotlin.isType(result_0, ConversionSuccess))
        tmp$_1 = result_0.value;
      else if (Kotlin.isType(result_0, ConversionFailure))
        throw IllegalArgumentException_init(result_0.reason);
      else
        tmp$_1 = Kotlin.noWhenBranchMatched();
      tempValue = tmp$_1;
      tempValueUnit = range.unit;
    }
    if (!Kotlin.isType(range, RangeDateTime))
      if (Kotlin.isType(range, RangeDuration)) {
        tmp$_2 = range.unit;
        if (equals(tmp$_2, Units_getInstance().SEC))
          tmp$_3 = Units_getInstance().NONE;
        else if (equals(tmp$_2, Units_getInstance().S_PER_M))
          tmp$_3 = Units_getInstance().PER_M;
        else if (equals(tmp$_2, Units_getInstance().S_PER_100M))
          tmp$_3 = Units_getInstance().PER_100M;
        else if (equals(tmp$_2, Units_getInstance().S_PER_500M))
          tmp$_3 = Units_getInstance().PER_500M;
        else if (equals(tmp$_2, Units_getInstance().S_PER_KM))
          tmp$_3 = Units_getInstance().PER_KM;
        else if (equals(tmp$_2, Units_getInstance().S_PER_MI))
          tmp$_3 = Units_getInstance().PER_MI;
        else if (equals(tmp$_2, Units_getInstance().S_PER_100YD))
          tmp$_3 = Units_getInstance().PER_100YD;
        else
          tmp$_3 = tempValueUnit;
        tempValueUnit = tmp$_3;
      }
    return new BaseFormatter$ConversionData(tempValue, tempValueUnit, range);
  };
  BaseFormatter.prototype.findRange_0 = function (transformedValue, ranges) {
    var rangeValue = JsMath.abs(transformedValue);
    var firstOrNull$result;
    firstOrNull$break: do {
      var tmp$;
      for (tmp$ = 0; tmp$ !== ranges.length; ++tmp$) {
        var element = ranges[tmp$];
        if (rangeValue < element.max || isNaN_0(element.max)) {
          firstOrNull$result = element;
          break firstOrNull$break;
        }
      }
      firstOrNull$result = null;
    }
     while (false);
    return firstOrNull$result;
  };
  BaseFormatter.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BaseFormatter',
    interfaces: []
  };
  function Formatter() {
    Formatter_instance = this;
    BaseFormatter.call(this);
  }
  Formatter.prototype.swimPaceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100M, get_SwimPaceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100YD, get_SwimPaceFourdigitsImperial());
    }
  };
  Formatter.prototype.swimPaceFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100M, get_SwimPaceFixedNoLeadingZeroMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100YD, get_SwimPaceFixedNoLeadingZeroImperial());
    }
  };
  Formatter.prototype.swimPaceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100M, get_SwimPaceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100YD, get_SwimPaceFivedigitsImperial());
    }
  };
  Formatter.prototype.swimPaceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100M, get_SwimPaceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_100YD, get_SwimPaceSixdigitsImperial());
    }
  };
  Formatter.prototype.heartRateBpmFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_getInstance(), value, Units_getInstance().BPM, Units_getInstance().BPM, get_HeartRateBpmFourdigitsGeneric());
  };
  Formatter.prototype.heartRateBpmFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_getInstance(), value, Units_getInstance().BPM, Units_getInstance().BPM, get_HeartRateBpmFivedigitsGeneric());
  };
  Formatter.prototype.diveGasPressureNodecimal_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().KPA, Units_getInstance().BAR, get_DiveGasPressureNodecimalMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().KPA, Units_getInstance().PSI, get_DiveGasPressureNodecimalImperial());
    }
  };
  Formatter.prototype.downhillSpeedFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_DownhillSpeedFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_DownhillSpeedFourdigitsImperial());
    }
  };
  Formatter.prototype.downhillSpeedThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_DownhillSpeedThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_DownhillSpeedThreedigitsImperial());
    }
  };
  Formatter.prototype.downhillSpeedApproximate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_DownhillSpeedApproximateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_DownhillSpeedApproximateImperial());
    }
  };
  Formatter.prototype.downhillSpeedFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_DownhillSpeedFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_DownhillSpeedFivedigitsImperial());
    }
  };
  Formatter.prototype.downhillSpeedSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_DownhillSpeedSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_DownhillSpeedSixdigitsImperial());
    }
  };
  Formatter.prototype.cadenceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().RPM, get_CadenceFourdigitsGeneric());
  };
  Formatter.prototype.cadenceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().RPM, get_CadenceFivedigitsGeneric());
  };
  Formatter.prototype.cadenceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().RPM, get_CadenceSixdigitsGeneric());
  };
  Formatter.prototype.strokesFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_StrokesFourdigitsGeneric());
  };
  Formatter.prototype.strokesThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_StrokesThreedigitsGeneric());
  };
  Formatter.prototype.strokesFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_StrokesFivedigitsGeneric());
  };
  Formatter.prototype.strokesSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_StrokesSixdigitsGeneric());
  };
  Formatter.prototype.peakTrainingEffectFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PeakTrainingEffectFourdigitsGeneric());
  };
  Formatter.prototype.peakTrainingEffectFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PeakTrainingEffectFivedigitsGeneric());
  };
  Formatter.prototype.peakTrainingEffectSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PeakTrainingEffectSixdigitsGeneric());
  };
  Formatter.prototype.swimDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_SwimDistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_SwimDistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.swimDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_SwimDistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_SwimDistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.swimDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_SwimDistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_SwimDistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.countFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_CountFourdigitsGeneric());
  };
  Formatter.prototype.countTwodigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_CountTwodigitsGeneric());
  };
  Formatter.prototype.countThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_CountThreedigitsGeneric());
  };
  Formatter.prototype.countFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_CountFivedigitsGeneric());
  };
  Formatter.prototype.countSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_CountSixdigitsGeneric());
  };
  Formatter.prototype.downhillDurationTraining_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DownhillDurationTrainingGeneric());
  };
  Formatter.prototype.downhillDurationFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DownhillDurationFourdigitsGeneric());
  };
  Formatter.prototype.downhillDurationApproximate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DownhillDurationApproximateGeneric());
  };
  Formatter.prototype.downhillDurationFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DownhillDurationFivedigitsGeneric());
  };
  Formatter.prototype.downhillDurationSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DownhillDurationSixdigitsGeneric());
  };
  Formatter.prototype.flightTimeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$FLIGHT_TIME_getInstance(), value, Units_getInstance().SEC, Units_getInstance().MS, get_FlightTimeFourdigitsGeneric());
  };
  Formatter.prototype.recoveryTimeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$RECOVERY_TIME_getInstance(), value, Units_getInstance().SEC, Units_getInstance().HOUR, get_RecoveryTimeFourdigitsGeneric());
  };
  Formatter.prototype.recoveryTimeFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$RECOVERY_TIME_getInstance(), value, Units_getInstance().SEC, Units_getInstance().HOUR, get_RecoveryTimeFivedigitsGeneric());
  };
  Formatter.prototype.recoveryTimeSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$RECOVERY_TIME_getInstance(), value, Units_getInstance().SEC, Units_getInstance().HOUR, get_RecoveryTimeSixdigitsGeneric());
  };
  Formatter.prototype.ascentFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AscentFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AscentFourdigitsImperial());
    }
  };
  Formatter.prototype.ascentFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AscentFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AscentFivedigitsImperial());
    }
  };
  Formatter.prototype.ascentSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AscentSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ASCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AscentSixdigitsImperial());
    }
  };
  Formatter.prototype.paceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_KM, get_PaceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_PaceFourdigitsImperial());
    }
  };
  Formatter.prototype.paceFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_KM, get_PaceFixedNoLeadingZeroMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_PaceFixedNoLeadingZeroImperial());
    }
  };
  Formatter.prototype.paceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_KM, get_PaceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_PaceFivedigitsImperial());
    }
  };
  Formatter.prototype.paceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_KM, get_PaceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_PaceSixdigitsImperial());
    }
  };
  Formatter.prototype.vO2Fourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$VO2_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_VO2FourdigitsGeneric());
  };
  Formatter.prototype.vO2Fivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$VO2_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_VO2FivedigitsGeneric());
  };
  Formatter.prototype.vO2Sixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$VO2_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_VO2SixdigitsGeneric());
  };
  Formatter.prototype.navigationRouteDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationRouteDistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationRouteDistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.navigationRouteDistanceThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationRouteDistanceThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationRouteDistanceThreedigitsImperial());
    }
  };
  Formatter.prototype.navigationRouteDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationRouteDistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationRouteDistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.navigationRouteDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationRouteDistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationRouteDistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.navigationRouteDistanceAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationRouteDistanceAccurateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationRouteDistanceAccurateImperial());
    }
  };
  Formatter.prototype.stiffnessTwodigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STIFFNESS_getInstance(), value, Units_getInstance().J, Units_getInstance().KNM, get_StiffnessTwodigitsGeneric());
  };
  Formatter.prototype.downhillAltitudeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillAltitudeFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillAltitudeFourdigitsImperial());
    }
  };
  Formatter.prototype.downhillAltitudeFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillAltitudeFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillAltitudeFivedigitsImperial());
    }
  };
  Formatter.prototype.downhillAltitudeSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillAltitudeSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillAltitudeSixdigitsImperial());
    }
  };
  Formatter.prototype.compassHeadingDegFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_CompassHeadingDegFourdigitsGeneric());
  };
  Formatter.prototype.compassHeadingDegFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_CompassHeadingDegFivedigitsGeneric());
  };
  Formatter.prototype.compassHeadingDegSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_CompassHeadingDegSixdigitsGeneric());
  };
  Formatter.prototype.compassHeadingDegAccurate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_CompassHeadingDegAccurateGeneric());
  };
  Formatter.prototype.distanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.distanceAccumulated_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceAccumulatedMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceAccumulatedImperial());
    }
  };
  Formatter.prototype.distanceThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceThreedigitsImperial());
    }
  };
  Formatter.prototype.distanceMapscale_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceMapscaleMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceMapscaleImperial());
    }
  };
  Formatter.prototype.distanceApproximate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceApproximateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceApproximateImperial());
    }
  };
  Formatter.prototype.distanceNodecimal_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceNodecimalMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceNodecimalImperial());
    }
  };
  Formatter.prototype.distanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.distanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.distanceOnedecimal_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceOnedecimalMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceOnedecimalImperial());
    }
  };
  Formatter.prototype.distanceAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DistanceAccurateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DistanceAccurateImperial());
    }
  };
  Formatter.prototype.trackAndFieldDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_TrackAndFieldDistanceFivedigitsGeneric());
  };
  Formatter.prototype.descentFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DescentFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DescentFourdigitsImperial());
    }
  };
  Formatter.prototype.descentFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DescentFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DescentFivedigitsImperial());
    }
  };
  Formatter.prototype.descentSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DescentSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DescentSixdigitsImperial());
    }
  };
  Formatter.prototype.ePOCFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$EPOC_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_EPOCFourdigitsGeneric());
  };
  Formatter.prototype.ePOCFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$EPOC_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_EPOCFivedigitsGeneric());
  };
  Formatter.prototype.ePOCSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$EPOC_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_EPOCSixdigitsGeneric());
  };
  Formatter.prototype.poolSwimDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_PoolSwimDistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_PoolSwimDistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.poolSwimDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_PoolSwimDistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_PoolSwimDistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.poolSwimDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_PoolSwimDistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SWIM_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().YD, get_PoolSwimDistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.stepLengthThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STEPLENGTH_getInstance(), value, Units_getInstance().M, Units_getInstance().CM, get_StepLengthThreedigitsGeneric());
  };
  Formatter.prototype.navigationPoiETEFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEFourdigitsGeneric());
  };
  Formatter.prototype.navigationPoiETEFourdigitsFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEFourdigitsFixedGeneric());
  };
  Formatter.prototype.navigationPoiETEHours_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEHoursGeneric());
  };
  Formatter.prototype.navigationPoiETEFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEFixedNoLeadingZeroGeneric());
  };
  Formatter.prototype.navigationPoiETEHumane_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEHumaneGeneric());
  };
  Formatter.prototype.navigationPoiETEFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEFivedigitsGeneric());
  };
  Formatter.prototype.navigationPoiETESixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETESixdigitsGeneric());
  };
  Formatter.prototype.navigationPoiETEMinutes_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEMinutesGeneric());
  };
  Formatter.prototype.navigationPoiETEFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_POI_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETEFixedGeneric());
  };
  Formatter.prototype.navigationPOIDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationPOIDistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationPOIDistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.navigationPOIDistanceThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationPOIDistanceThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationPOIDistanceThreedigitsImperial());
    }
  };
  Formatter.prototype.navigationPOIDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationPOIDistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationPOIDistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.navigationPOIDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationPOIDistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationPOIDistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.navigationPOIDistanceAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_NavigationPOIDistanceAccurateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_NavigationPOIDistanceAccurateImperial());
    }
  };
  Formatter.prototype.durationFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFourdigitsGeneric());
  };
  Formatter.prototype.durationAccumulated_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationAccumulatedGeneric());
  };
  Formatter.prototype.durationHours_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationHoursGeneric());
  };
  Formatter.prototype.durationFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFixedNoLeadingZeroGeneric());
  };
  Formatter.prototype.durationApproximate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationApproximateGeneric());
  };
  Formatter.prototype.durationMinutes_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationMinutesGeneric());
  };
  Formatter.prototype.durationTraining_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationTrainingGeneric());
  };
  Formatter.prototype.durationApproximateNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationApproximateNoLeadingZeroGeneric());
  };
  Formatter.prototype.durationFourdigitsFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFourdigitsFixedGeneric());
  };
  Formatter.prototype.durationHumane_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationHumaneGeneric());
  };
  Formatter.prototype.durationNodecimal_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationNodecimalGeneric());
  };
  Formatter.prototype.durationFourdigitsFixedRounded_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFourdigitsFixedRoundedGeneric());
  };
  Formatter.prototype.durationFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFivedigitsGeneric());
  };
  Formatter.prototype.durationSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationSixdigitsGeneric());
  };
  Formatter.prototype.durationAccurate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationAccurateGeneric());
  };
  Formatter.prototype.durationFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DurationFixedGeneric());
  };
  Formatter.prototype.compassHeadingMilFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().MIL, get_CompassHeadingMilFourdigitsGeneric());
  };
  Formatter.prototype.compassHeadingMilFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().MIL, get_CompassHeadingMilFivedigitsGeneric());
  };
  Formatter.prototype.compassHeadingMilSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().MIL, get_CompassHeadingMilSixdigitsGeneric());
  };
  Formatter.prototype.downhillDescentFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDescentFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillDescentFourdigitsImperial());
    }
  };
  Formatter.prototype.downhillDescentFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDescentFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillDescentFivedigitsImperial());
    }
  };
  Formatter.prototype.downhillDescentSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDescentSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DESCENT_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_DownhillDescentSixdigitsImperial());
    }
  };
  Formatter.prototype.reactivityOnedigit_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$REACTIVITY_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_ReactivityOnedigitGeneric());
  };
  Formatter.prototype.contactTimeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CONTACT_TIME_getInstance(), value, Units_getInstance().SEC, Units_getInstance().MS, get_ContactTimeFourdigitsGeneric());
  };
  Formatter.prototype.weigthFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().KG, get_WeigthFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().LB, get_WeigthFourdigitsImperial());
    }
  };
  Formatter.prototype.weigthFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().KG, get_WeigthFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().LB, get_WeigthFivedigitsImperial());
    }
  };
  Formatter.prototype.weigthSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().KG, get_WeigthSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$WEIGHT_getInstance(), value, Units_getInstance().KG, Units_getInstance().LB, get_WeigthSixdigitsImperial());
    }
  };
  Formatter.prototype.cadenceSpmFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_CadenceSpmFourdigitsGeneric());
  };
  Formatter.prototype.cadenceSpmFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_CadenceSpmFivedigitsGeneric());
  };
  Formatter.prototype.cadenceSpmSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CADENCE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_CadenceSpmSixdigitsGeneric());
  };
  Formatter.prototype.declinationFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_DeclinationFourdigitsGeneric());
  };
  Formatter.prototype.declinationFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_DeclinationFivedigitsGeneric());
  };
  Formatter.prototype.declinationSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$COMPASS_HEADING_DEG_getInstance(), value, Units_getInstance().RAD, Units_getInstance().DEG, get_DeclinationSixdigitsGeneric());
  };
  Formatter.prototype.performanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERFORMANCE_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PerformanceFourdigitsGeneric());
  };
  Formatter.prototype.performanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERFORMANCE_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PerformanceFivedigitsGeneric());
  };
  Formatter.prototype.performanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERFORMANCE_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_PerformanceSixdigitsGeneric());
  };
  Formatter.prototype.diveGasConsumptionOnedecimal_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().M3_PER_S, Units_getInstance().L_PER_MIN, get_DiveGasConsumptionOnedecimalMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().M3_PER_S, Units_getInstance().FT3_PER_MIN, get_DiveGasConsumptionOnedecimalImperial());
    }
  };
  Formatter.prototype.diveDurationAccurate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_DiveDurationAccurateGeneric());
  };
  Formatter.prototype.downhillDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceFourdigitsImperial());
    }
  };
  Formatter.prototype.downhillDistanceThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceThreedigitsImperial());
    }
  };
  Formatter.prototype.downhillDistanceApproximate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceApproximateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceApproximateImperial());
    }
  };
  Formatter.prototype.downhillDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceFivedigitsImperial());
    }
  };
  Formatter.prototype.downhillDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceSixdigitsImperial());
    }
  };
  Formatter.prototype.downhillDistanceAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DownhillDistanceAccurateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DownhillDistanceAccurateImperial());
    }
  };
  Formatter.prototype.heartRateFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().BPM, get_HeartRateFourdigitsGeneric());
  };
  Formatter.prototype.heartRateFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().BPM, get_HeartRateFivedigitsGeneric());
  };
  Formatter.prototype.heartRateSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_getInstance(), value, Units_getInstance().HZ, Units_getInstance().BPM, get_HeartRateSixdigitsGeneric());
  };
  Formatter.prototype.temperatureFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().C, get_TemperatureFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().F, get_TemperatureFourdigitsImperial());
    }
  };
  Formatter.prototype.temperatureFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().C, get_TemperatureFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().F, get_TemperatureFivedigitsImperial());
    }
  };
  Formatter.prototype.temperatureSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().C, get_TemperatureSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TEMPERATURE_getInstance(), value, Units_getInstance().K, Units_getInstance().F, get_TemperatureSixdigitsImperial());
    }
  };
  Formatter.prototype.undulationThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$UNDULATION_getInstance(), value, Units_getInstance().M, Units_getInstance().CM, get_UndulationThreedigitsGeneric());
  };
  Formatter.prototype.durationMsFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().MS, Units_getInstance().SEC, get_DurationMsFourdigitsGeneric());
  };
  Formatter.prototype.durationMsApproximate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().MS, Units_getInstance().SEC, get_DurationMsApproximateGeneric());
  };
  Formatter.prototype.durationMsFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().MS, Units_getInstance().SEC, get_DurationMsFivedigitsGeneric());
  };
  Formatter.prototype.durationMsSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().MS, Units_getInstance().SEC, get_DurationMsSixdigitsGeneric());
  };
  Formatter.prototype.durationMsAccurate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DURATION_getInstance(), value, Units_getInstance().MS, Units_getInstance().SEC, get_DurationMsAccurateGeneric());
  };
  Formatter.prototype.navigationRouteETEFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEFourdigitsGeneric());
  };
  Formatter.prototype.navigationRouteETEFourdigitsFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEFourdigitsFixedGeneric());
  };
  Formatter.prototype.navigationRouteETEHours_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEHoursGeneric());
  };
  Formatter.prototype.navigationRouteETEFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEFixedNoLeadingZeroGeneric());
  };
  Formatter.prototype.navigationRouteETEHumane_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEHumaneGeneric());
  };
  Formatter.prototype.navigationRouteETEFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEFivedigitsGeneric());
  };
  Formatter.prototype.navigationRouteETESixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETESixdigitsGeneric());
  };
  Formatter.prototype.navigationRouteETEMinutes_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEMinutesGeneric());
  };
  Formatter.prototype.navigationRouteETEFixed_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ETE_ROUTE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETEFixedGeneric());
  };
  Formatter.prototype.rowingPaceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_500M, get_RowingPaceFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_RowingPaceFourdigitsImperial());
    }
  };
  Formatter.prototype.rowingPaceFixedNoLeadingZero_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_500M, get_RowingPaceFixedNoLeadingZeroMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_RowingPaceFixedNoLeadingZeroImperial());
    }
  };
  Formatter.prototype.rowingPaceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_500M, get_RowingPaceFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_RowingPaceFivedigitsImperial());
    }
  };
  Formatter.prototype.rowingPaceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_500M, get_RowingPaceSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ROWING_PACE_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().S_PER_MI, get_RowingPaceSixdigitsImperial());
    }
  };
  Formatter.prototype.diveDistanceAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_DiveDistanceAccurateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().MI, get_DiveDistanceAccurateImperial());
    }
  };
  Formatter.prototype.nauticalDistanceFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().NMI, get_NauticalDistanceFourdigitsGeneric());
  };
  Formatter.prototype.nauticalDistanceFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().NMI, get_NauticalDistanceFivedigitsGeneric());
  };
  Formatter.prototype.nauticalDistanceSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_DISTANCE_getInstance(), value, Units_getInstance().M, Units_getInstance().NMI, get_NauticalDistanceSixdigitsGeneric());
  };
  Formatter.prototype.energyFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CALORIES_getInstance(), value, Units_getInstance().J, Units_getInstance().KCAL, get_EnergyFourdigitsGeneric());
  };
  Formatter.prototype.energyAccumulated_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CALORIES_getInstance(), value, Units_getInstance().J, Units_getInstance().KCAL, get_EnergyAccumulatedGeneric());
  };
  Formatter.prototype.energyFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CALORIES_getInstance(), value, Units_getInstance().J, Units_getInstance().KCAL, get_EnergyFivedigitsGeneric());
  };
  Formatter.prototype.energySixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$CALORIES_getInstance(), value, Units_getInstance().J, Units_getInstance().KCAL, get_EnergySixdigitsGeneric());
  };
  Formatter.prototype.navigationPoiETAFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETAFourdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETAFourdigits12());
    }
  };
  Formatter.prototype.navigationPoiETAFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETAFivedigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETAFivedigits12());
    }
  };
  Formatter.prototype.navigationPoiETASixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETASixdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_POI_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationPoiETASixdigits12());
    }
  };
  Formatter.prototype.strokeRateFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_StrokeRateFourdigitsGeneric());
  };
  Formatter.prototype.strokeRateFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_StrokeRateFivedigitsGeneric());
  };
  Formatter.prototype.strokeRateSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$STROKES_getInstance(), value, Units_getInstance().HZ, Units_getInstance().SPM, get_StrokeRateSixdigitsGeneric());
  };
  Formatter.prototype.speedFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_SpeedFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_SpeedFourdigitsImperial());
    }
  };
  Formatter.prototype.speedThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_SpeedThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_SpeedThreedigitsImperial());
    }
  };
  Formatter.prototype.speedApproximate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_SpeedApproximateMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_SpeedApproximateImperial());
    }
  };
  Formatter.prototype.speedFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_SpeedFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_SpeedFivedigitsImperial());
    }
  };
  Formatter.prototype.speedSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KM_PER_H, get_SpeedSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().MI_PER_H, get_SpeedSixdigitsImperial());
    }
  };
  Formatter.prototype.heartRatePercentageFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_HeartRatePercentageFourdigitsGeneric());
  };
  Formatter.prototype.heartRatePercentageThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_HeartRatePercentageThreedigitsGeneric());
  };
  Formatter.prototype.heartRatePercentageFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_HeartRatePercentageFivedigitsGeneric());
  };
  Formatter.prototype.heartRatePercentageSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$HEART_RATE_PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_HeartRatePercentageSixdigitsGeneric());
  };
  Formatter.prototype.trainingEffectFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_TrainingEffectFourdigitsGeneric());
  };
  Formatter.prototype.trainingEffectFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_TrainingEffectFivedigitsGeneric());
  };
  Formatter.prototype.trainingEffectSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$TRAINING_EFFECT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_TrainingEffectSixdigitsGeneric());
  };
  Formatter.prototype.timeOfDayFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayFourdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayFourdigits12());
    }
  };
  Formatter.prototype.timeOfDayFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayFivedigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayFivedigits12());
    }
  };
  Formatter.prototype.timeOfDaySixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDaySixdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDaySixdigits12());
    }
  };
  Formatter.prototype.timeOfDayAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayAccurate24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$TIME_OF_DAY_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_TimeOfDayAccurate12());
    }
  };
  Formatter.prototype.downhillLapCountFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_LAP_COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_DownhillLapCountFourdigitsGeneric());
  };
  Formatter.prototype.downhillLapCountThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_LAP_COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_DownhillLapCountThreedigitsGeneric());
  };
  Formatter.prototype.downhillLapCountFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_LAP_COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_DownhillLapCountFivedigitsGeneric());
  };
  Formatter.prototype.downhillLapCountSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_LAP_COUNT_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_DownhillLapCountSixdigitsGeneric());
  };
  Formatter.prototype.percentageFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_PercentageFourdigitsGeneric());
  };
  Formatter.prototype.percentageThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_PercentageThreedigitsGeneric());
  };
  Formatter.prototype.percentageFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_PercentageFivedigitsGeneric());
  };
  Formatter.prototype.percentageSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$PERCENTAGE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_PercentageSixdigitsGeneric());
  };
  Formatter.prototype.verticalSpeedMountainFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_H, get_VerticalSpeedMountainFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_H, get_VerticalSpeedMountainFourdigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedMountainThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_H, get_VerticalSpeedMountainThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_H, get_VerticalSpeedMountainThreedigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedMountainFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_H, get_VerticalSpeedMountainFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_H, get_VerticalSpeedMountainFivedigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedMountainSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_H, get_VerticalSpeedMountainSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_H, get_VerticalSpeedMountainSixdigitsImperial());
    }
  };
  Formatter.prototype.nauticalSpeedFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KN, get_NauticalSpeedFourdigitsGeneric());
  };
  Formatter.prototype.nauticalSpeedThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KN, get_NauticalSpeedThreedigitsGeneric());
  };
  Formatter.prototype.nauticalSpeedApproximate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KN, get_NauticalSpeedApproximateGeneric());
  };
  Formatter.prototype.nauticalSpeedFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KN, get_NauticalSpeedFivedigitsGeneric());
  };
  Formatter.prototype.nauticalSpeedSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$NAUTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().KN, get_NauticalSpeedSixdigitsGeneric());
  };
  Formatter.prototype.swolfFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$SWOLF_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_SwolfFourdigitsGeneric());
  };
  Formatter.prototype.swolfThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$SWOLF_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_SwolfThreedigitsGeneric());
  };
  Formatter.prototype.swolfFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$SWOLF_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_SwolfFivedigitsGeneric());
  };
  Formatter.prototype.swolfSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$SWOLF_getInstance(), value, Units_getInstance().SCALAR, Units_getInstance().SCALAR, get_SwolfSixdigitsGeneric());
  };
  Formatter.prototype.downhillGradeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_GRADE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_DownhillGradeFourdigitsGeneric());
  };
  Formatter.prototype.downhillGradeTwodigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_GRADE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_DownhillGradeTwodigitsGeneric());
  };
  Formatter.prototype.downhillGradeThreedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_GRADE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_DownhillGradeThreedigitsGeneric());
  };
  Formatter.prototype.downhillGradeFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_GRADE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_DownhillGradeFivedigitsGeneric());
  };
  Formatter.prototype.downhillGradeSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$DOWNHILL_GRADE_getInstance(), value, Units_getInstance().PERCENT, Units_getInstance().PERCENT, get_DownhillGradeSixdigitsGeneric());
  };
  Formatter.prototype.sunsetFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetFourdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetFourdigits12());
    }
  };
  Formatter.prototype.sunsetFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetFivedigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetFivedigits12());
    }
  };
  Formatter.prototype.sunsetSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetSixdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetSixdigits12());
    }
  };
  Formatter.prototype.sunsetAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetAccurate24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNSET_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunsetAccurate12());
    }
  };
  Formatter.prototype.navigationRouteETAFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETAFourdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETAFourdigits12());
    }
  };
  Formatter.prototype.navigationRouteETAFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETAFivedigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETAFivedigits12());
    }
  };
  Formatter.prototype.navigationRouteETASixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETASixdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$NAVIGATION_ROUTE_ETA_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_NavigationRouteETASixdigits12());
    }
  };
  Formatter.prototype.sunriseFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseFourdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseFourdigits12());
    }
  };
  Formatter.prototype.sunriseFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseFivedigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseFivedigits12());
    }
  };
  Formatter.prototype.sunriseSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseSixdigits24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseSixdigits12());
    }
  };
  Formatter.prototype.sunriseAccurate_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.use24HClock) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseAccurate24());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$SUNRISE_getInstance(), value, Units_getInstance().SEC, Units_getInstance().SEC, get_SunriseAccurate12());
    }
  };
  Formatter.prototype.verticalSpeedFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_MIN, get_VerticalSpeedFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_MIN, get_VerticalSpeedFourdigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedThreedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_MIN, get_VerticalSpeedThreedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_MIN, get_VerticalSpeedThreedigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_MIN, get_VerticalSpeedFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_MIN, get_VerticalSpeedFivedigitsImperial());
    }
  };
  Formatter.prototype.verticalSpeedSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().M_PER_MIN, get_VerticalSpeedSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$VERTICAL_SPEED_getInstance(), value, Units_getInstance().M_PER_S, Units_getInstance().FT_PER_MIN, get_VerticalSpeedSixdigitsImperial());
    }
  };
  Formatter.prototype.airPressureFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().HPA, get_AirPressureFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().INHG, get_AirPressureFourdigitsImperial());
    }
  };
  Formatter.prototype.airPressureFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().HPA, get_AirPressureFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().INHG, get_AirPressureFivedigitsImperial());
    }
  };
  Formatter.prototype.airPressureSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().HPA, get_AirPressureSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$AIR_PRESSURE_getInstance(), value, Units_getInstance().PA, Units_getInstance().INHG, get_AirPressureSixdigitsImperial());
    }
  };
  Formatter.prototype.powerFourdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$POWER_getInstance(), value, Units_getInstance().W, Units_getInstance().W, get_PowerFourdigitsGeneric());
  };
  Formatter.prototype.powerFivedigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$POWER_getInstance(), value, Units_getInstance().W, Units_getInstance().W, get_PowerFivedigitsGeneric());
  };
  Formatter.prototype.powerSixdigits_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$POWER_getInstance(), value, Units_getInstance().W, Units_getInstance().W, get_PowerSixdigitsGeneric());
  };
  Formatter.prototype.powerAccurate_3r6o6c$ = function (value, formattingOptions) {
    return this.formatValue_fqspz6$(formattingOptions, Icon$POWER_getInstance(), value, Units_getInstance().W, Units_getInstance().W, get_PowerAccurateGeneric());
  };
  Formatter.prototype.altitudeFourdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AltitudeFourdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AltitudeFourdigitsImperial());
    }
  };
  Formatter.prototype.altitudeFivedigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AltitudeFivedigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AltitudeFivedigitsImperial());
    }
  };
  Formatter.prototype.altitudeSixdigits_3r6o6c$ = function (value, formattingOptions) {
    if (formattingOptions.measurementSystem === MeasurementSystem$METRIC_getInstance()) {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().M, get_AltitudeSixdigitsMetric());
    } else {
      return this.formatValue_fqspz6$(formattingOptions, Icon$ALTITUDE_getInstance(), value, Units_getInstance().M, Units_getInstance().FT, get_AltitudeSixdigitsImperial());
    }
  };
  Formatter.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Formatter',
    interfaces: [BaseFormatter]
  };
  var Formatter_instance = null;
  function Formatter_getInstance() {
    if (Formatter_instance === null) {
      new Formatter();
    }
    return Formatter_instance;
  }
  function Range(max, unit) {
    if (max === void 0)
      max = kotlin_js_internal_DoubleCompanionObject.MAX_VALUE;
    this.max_6oep0v$_0 = max;
    this.unit_67b02j$_0 = unit;
  }
  Object.defineProperty(Range.prototype, 'max', {
    get: function () {
      return this.max_6oep0v$_0;
    }
  });
  Object.defineProperty(Range.prototype, 'unit', {
    get: function () {
      return this.unit_67b02j$_0;
    }
  });
  Range.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Range',
    interfaces: []
  };
  function RangeSimpleConversion(max, unit, decimalsMin, decimalsMax, method) {
    if (method === void 0)
      method = Method$ROUND_getInstance();
    Range.call(this, max, unit);
    this.max_1owfux$_0 = max;
    this.unit_igk0gt$_0 = unit;
    this.decimalFormatter_0 = new DecimalFormatter(decimalsMin, decimalsMax, method);
  }
  Object.defineProperty(RangeSimpleConversion.prototype, 'max', {
    get: function () {
      return this.max_1owfux$_0;
    }
  });
  Object.defineProperty(RangeSimpleConversion.prototype, 'unit', {
    get: function () {
      return this.unit_igk0gt$_0;
    }
  });
  RangeSimpleConversion.prototype.format_qfvghl$ = function (value, valueUnit) {
    return this.decimalFormatter_0.roundToString_14dthe$(value);
  };
  RangeSimpleConversion.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'RangeSimpleConversion',
    interfaces: [Range]
  };
  function RangeDateTime(max, unit, format) {
    Range.call(this, max, unit);
    this.max_1nsm4q$_0 = max;
    this.unit_jo3dsm$_0 = unit;
    this.format = format;
  }
  Object.defineProperty(RangeDateTime.prototype, 'max', {
    get: function () {
      return this.max_1nsm4q$_0;
    }
  });
  Object.defineProperty(RangeDateTime.prototype, 'unit', {
    get: function () {
      return this.unit_jo3dsm$_0;
    }
  });
  RangeDateTime.prototype.format_qfvghl$ = function (value, valueUnit) {
    throw new NotImplementedError_init('An operation is not implemented: ' + 'Date/time ranges are not supported');
  };
  RangeDateTime.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'RangeDateTime',
    interfaces: [Range]
  };
  function RangeDuration(max, unit, format, method) {
    if (method === void 0)
      method = Method$ROUND_getInstance();
    Range.call(this, max, unit);
    this.max_s4625f$_0 = max;
    this.unit_j38t2p$_0 = unit;
    this.format = format;
    this.method = method;
  }
  Object.defineProperty(RangeDuration.prototype, 'max', {
    get: function () {
      return this.max_s4625f$_0;
    }
  });
  Object.defineProperty(RangeDuration.prototype, 'unit', {
    get: function () {
      return this.unit_j38t2p$_0;
    }
  });
  RangeDuration.prototype.format_qfvghl$ = function (value, valueUnit) {
    var tmp$, tmp$_0;
    switch (valueUnit.dimension.name) {
      case 'OTHER':
        tmp$ = value;
        break;
      case 'TIME':
        var secondsResult = valueUnit.convert(value, Units_getInstance().SEC);
        if (Kotlin.isType(secondsResult, ConversionSuccess)) {
          tmp$ = secondsResult.value;
        } else {
          throw IllegalArgumentException_init('Unable to format ' + value + ' with unit ' + valueUnit);
        }

        break;
      case 'VELOCITY':
        var secondsResult_0 = valueUnit.convert(value, Units_getInstance().S_PER_KM);
        if (Kotlin.isType(secondsResult_0, ConversionSuccess)) {
          tmp$ = secondsResult_0.value;
        } else {
          throw IllegalArgumentException_init('Unable to format ' + value + ' with unit ' + valueUnit);
        }

        break;
      default:
        throw IllegalArgumentException_init('Unable to format ' + value + ' with unit ' + valueUnit);
    }
    var durationInSeconds = tmp$;
    switch (this.method.name) {
      case 'ROUND':
        tmp$_0 = round(durationInSeconds);
        break;
      case 'FLOOR':
        tmp$_0 = JsMath.floor(durationInSeconds);
        break;
      case 'TRUNCATE':
        tmp$_0 = durationInSeconds;
        break;
      default:
        tmp$_0 = Kotlin.noWhenBranchMatched();
        break;
    }
    var durationInSecondsRounded = tmp$_0;
    return formatDuration(durationInSecondsRounded, this.format);
  };
  RangeDuration.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'RangeDuration',
    interfaces: [Range]
  };
  function RangePercentage(max, unit, decimalsMin, decimalsMax) {
    Range.call(this, max, unit);
    this.max_amtsev$_0 = max;
    this.unit_pdaf6z$_0 = unit;
    this.decimalsMax = decimalsMax;
    this.decimalFormatter_0 = new DecimalFormatter(decimalsMin, this.decimalsMax);
  }
  Object.defineProperty(RangePercentage.prototype, 'max', {
    get: function () {
      return this.max_amtsev$_0;
    }
  });
  Object.defineProperty(RangePercentage.prototype, 'unit', {
    get: function () {
      return this.unit_pdaf6z$_0;
    }
  });
  RangePercentage.prototype.format_qfvghl$ = function (value, valueUnit) {
    return this.decimalFormatter_0.roundToString_14dthe$(value * 100.0);
  };
  RangePercentage.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'RangePercentage',
    interfaces: [Range]
  };
  function SwimPaceFourdigitsMetric$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_100M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFourdigitsMetric;
  function get_SwimPaceFourdigitsMetric() {
    return SwimPaceFourdigitsMetric.value;
  }
  function SwimPaceFourdigitsImperial$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_100YD, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFourdigitsImperial;
  function get_SwimPaceFourdigitsImperial() {
    return SwimPaceFourdigitsImperial.value;
  }
  function SwimPaceFixedNoLeadingZeroMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_100M, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFixedNoLeadingZeroMetric;
  function get_SwimPaceFixedNoLeadingZeroMetric() {
    return SwimPaceFixedNoLeadingZeroMetric.value;
  }
  function SwimPaceFixedNoLeadingZeroImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_100YD, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFixedNoLeadingZeroImperial;
  function get_SwimPaceFixedNoLeadingZeroImperial() {
    return SwimPaceFixedNoLeadingZeroImperial.value;
  }
  function SwimPaceFivedigitsMetric$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_100M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFivedigitsMetric;
  function get_SwimPaceFivedigitsMetric() {
    return SwimPaceFivedigitsMetric.value;
  }
  function SwimPaceFivedigitsImperial$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_100YD, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceFivedigitsImperial;
  function get_SwimPaceFivedigitsImperial() {
    return SwimPaceFivedigitsImperial.value;
  }
  function SwimPaceSixdigitsMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_100M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceSixdigitsMetric;
  function get_SwimPaceSixdigitsMetric() {
    return SwimPaceSixdigitsMetric.value;
  }
  function SwimPaceSixdigitsImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_100YD, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SwimPaceSixdigitsImperial;
  function get_SwimPaceSixdigitsImperial() {
    return SwimPaceSixdigitsImperial.value;
  }
  function HeartRateBpmFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().BPM, 0, 0)];
  }
  var HeartRateBpmFourdigitsGeneric;
  function get_HeartRateBpmFourdigitsGeneric() {
    return HeartRateBpmFourdigitsGeneric.value;
  }
  function HeartRateBpmFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().BPM, 0, 0)];
  }
  var HeartRateBpmFivedigitsGeneric;
  function get_HeartRateBpmFivedigitsGeneric() {
    return HeartRateBpmFivedigitsGeneric.value;
  }
  function DiveGasPressureNodecimalMetric$lambda() {
    return [new RangeSimpleConversion(1.0E8, Units_getInstance().BAR, 0, 0)];
  }
  var DiveGasPressureNodecimalMetric;
  function get_DiveGasPressureNodecimalMetric() {
    return DiveGasPressureNodecimalMetric.value;
  }
  function DiveGasPressureNodecimalImperial$lambda() {
    return [new RangeSimpleConversion(1.0E8, Units_getInstance().PSI, 0, 0)];
  }
  var DiveGasPressureNodecimalImperial;
  function get_DiveGasPressureNodecimalImperial() {
    return DiveGasPressureNodecimalImperial.value;
  }
  function DownhillSpeedFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var DownhillSpeedFourdigitsMetric;
  function get_DownhillSpeedFourdigitsMetric() {
    return DownhillSpeedFourdigitsMetric.value;
  }
  function DownhillSpeedFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var DownhillSpeedFourdigitsImperial;
  function get_DownhillSpeedFourdigitsImperial() {
    return DownhillSpeedFourdigitsImperial.value;
  }
  function DownhillSpeedThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().KM_PER_H, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().KM_PER_H, 0, 0)];
  }
  var DownhillSpeedThreedigitsMetric;
  function get_DownhillSpeedThreedigitsMetric() {
    return DownhillSpeedThreedigitsMetric.value;
  }
  function DownhillSpeedThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().MI_PER_H, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI_PER_H, 0, 0)];
  }
  var DownhillSpeedThreedigitsImperial;
  function get_DownhillSpeedThreedigitsImperial() {
    return DownhillSpeedThreedigitsImperial.value;
  }
  function DownhillSpeedApproximateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().KM_PER_H, 0, 0)];
  }
  var DownhillSpeedApproximateMetric;
  function get_DownhillSpeedApproximateMetric() {
    return DownhillSpeedApproximateMetric.value;
  }
  function DownhillSpeedApproximateImperial$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().MI_PER_H, 0, 0)];
  }
  var DownhillSpeedApproximateImperial;
  function get_DownhillSpeedApproximateImperial() {
    return DownhillSpeedApproximateImperial.value;
  }
  function DownhillSpeedFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var DownhillSpeedFivedigitsMetric;
  function get_DownhillSpeedFivedigitsMetric() {
    return DownhillSpeedFivedigitsMetric.value;
  }
  function DownhillSpeedFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var DownhillSpeedFivedigitsImperial;
  function get_DownhillSpeedFivedigitsImperial() {
    return DownhillSpeedFivedigitsImperial.value;
  }
  function DownhillSpeedSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var DownhillSpeedSixdigitsMetric;
  function get_DownhillSpeedSixdigitsMetric() {
    return DownhillSpeedSixdigitsMetric.value;
  }
  function DownhillSpeedSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var DownhillSpeedSixdigitsImperial;
  function get_DownhillSpeedSixdigitsImperial() {
    return DownhillSpeedSixdigitsImperial.value;
  }
  function CadenceFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().RPM, 0, 0)];
  }
  var CadenceFourdigitsGeneric;
  function get_CadenceFourdigitsGeneric() {
    return CadenceFourdigitsGeneric.value;
  }
  function CadenceFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().RPM, 0, 0)];
  }
  var CadenceFivedigitsGeneric;
  function get_CadenceFivedigitsGeneric() {
    return CadenceFivedigitsGeneric.value;
  }
  function CadenceSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().RPM, 0, 0)];
  }
  var CadenceSixdigitsGeneric;
  function get_CadenceSixdigitsGeneric() {
    return CadenceSixdigitsGeneric.value;
  }
  function StrokesFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var StrokesFourdigitsGeneric;
  function get_StrokesFourdigitsGeneric() {
    return StrokesFourdigitsGeneric.value;
  }
  function StrokesThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var StrokesThreedigitsGeneric;
  function get_StrokesThreedigitsGeneric() {
    return StrokesThreedigitsGeneric.value;
  }
  function StrokesFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var StrokesFivedigitsGeneric;
  function get_StrokesFivedigitsGeneric() {
    return StrokesFivedigitsGeneric.value;
  }
  function StrokesSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var StrokesSixdigitsGeneric;
  function get_StrokesSixdigitsGeneric() {
    return StrokesSixdigitsGeneric.value;
  }
  function PeakTrainingEffectFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.05, Units_getInstance().SCALAR, 1, 1)];
  }
  var PeakTrainingEffectFourdigitsGeneric;
  function get_PeakTrainingEffectFourdigitsGeneric() {
    return PeakTrainingEffectFourdigitsGeneric.value;
  }
  function PeakTrainingEffectFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.05, Units_getInstance().SCALAR, 1, 1)];
  }
  var PeakTrainingEffectFivedigitsGeneric;
  function get_PeakTrainingEffectFivedigitsGeneric() {
    return PeakTrainingEffectFivedigitsGeneric.value;
  }
  function PeakTrainingEffectSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.05, Units_getInstance().SCALAR, 1, 1)];
  }
  var PeakTrainingEffectSixdigitsGeneric;
  function get_PeakTrainingEffectSixdigitsGeneric() {
    return PeakTrainingEffectSixdigitsGeneric.value;
  }
  function SwimDistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var SwimDistanceFourdigitsMetric;
  function get_SwimDistanceFourdigitsMetric() {
    return SwimDistanceFourdigitsMetric.value;
  }
  function SwimDistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 0, 0)];
  }
  var SwimDistanceFourdigitsImperial;
  function get_SwimDistanceFourdigitsImperial() {
    return SwimDistanceFourdigitsImperial.value;
  }
  function SwimDistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var SwimDistanceFivedigitsMetric;
  function get_SwimDistanceFivedigitsMetric() {
    return SwimDistanceFivedigitsMetric.value;
  }
  function SwimDistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(999950.0, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 1, 1)];
  }
  var SwimDistanceFivedigitsImperial;
  function get_SwimDistanceFivedigitsImperial() {
    return SwimDistanceFivedigitsImperial.value;
  }
  function SwimDistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var SwimDistanceSixdigitsMetric;
  function get_SwimDistanceSixdigitsMetric() {
    return SwimDistanceSixdigitsMetric.value;
  }
  function SwimDistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 2, 2)];
  }
  var SwimDistanceSixdigitsImperial;
  function get_SwimDistanceSixdigitsImperial() {
    return SwimDistanceSixdigitsImperial.value;
  }
  function CountFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var CountFourdigitsGeneric;
  function get_CountFourdigitsGeneric() {
    return CountFourdigitsGeneric.value;
  }
  function CountTwodigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var CountTwodigitsGeneric;
  function get_CountTwodigitsGeneric() {
    return CountTwodigitsGeneric.value;
  }
  function CountThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var CountThreedigitsGeneric;
  function get_CountThreedigitsGeneric() {
    return CountThreedigitsGeneric.value;
  }
  function CountFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var CountFivedigitsGeneric;
  function get_CountFivedigitsGeneric() {
    return CountFivedigitsGeneric.value;
  }
  function CountSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var CountSixdigitsGeneric;
  function get_CountSixdigitsGeneric() {
    return CountSixdigitsGeneric.value;
  }
  function DownhillDurationTrainingGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DownhillDurationTrainingGeneric;
  function get_DownhillDurationTrainingGeneric() {
    return DownhillDurationTrainingGeneric.value;
  }
  function DownhillDurationFourdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DownhillDurationFourdigitsGeneric;
  function get_DownhillDurationFourdigitsGeneric() {
    return DownhillDurationFourdigitsGeneric.value;
  }
  function DownhillDurationApproximateGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf_0(_ss_getInstance())), new RangeDuration(3600.0, Units_getInstance().MIN, listOf_0(_mm_getInstance())), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DownhillDurationApproximateGeneric;
  function get_DownhillDurationApproximateGeneric() {
    return DownhillDurationApproximateGeneric.value;
  }
  function DownhillDurationFivedigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(36000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DownhillDurationFivedigitsGeneric;
  function get_DownhillDurationFivedigitsGeneric() {
    return DownhillDurationFivedigitsGeneric.value;
  }
  function DownhillDurationSixdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var DownhillDurationSixdigitsGeneric;
  function get_DownhillDurationSixdigitsGeneric() {
    return DownhillDurationSixdigitsGeneric.value;
  }
  function FlightTimeFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(2000.0, Units_getInstance().MS, 0, 0)];
  }
  var FlightTimeFourdigitsGeneric;
  function get_FlightTimeFourdigitsGeneric() {
    return FlightTimeFourdigitsGeneric.value;
  }
  function RecoveryTimeFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(120.1, Units_getInstance().HOUR, 0, 0, Method$FLOOR_getInstance())];
  }
  var RecoveryTimeFourdigitsGeneric;
  function get_RecoveryTimeFourdigitsGeneric() {
    return RecoveryTimeFourdigitsGeneric.value;
  }
  function RecoveryTimeFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(120.1, Units_getInstance().HOUR, 0, 0, Method$FLOOR_getInstance())];
  }
  var RecoveryTimeFivedigitsGeneric;
  function get_RecoveryTimeFivedigitsGeneric() {
    return RecoveryTimeFivedigitsGeneric.value;
  }
  function RecoveryTimeSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(120.1, Units_getInstance().HOUR, 0, 0, Method$FLOOR_getInstance())];
  }
  var RecoveryTimeSixdigitsGeneric;
  function get_RecoveryTimeSixdigitsGeneric() {
    return RecoveryTimeSixdigitsGeneric.value;
  }
  function AscentFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 0, 0)];
  }
  var AscentFourdigitsMetric;
  function get_AscentFourdigitsMetric() {
    return AscentFourdigitsMetric.value;
  }
  function AscentFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 0, 0)];
  }
  var AscentFourdigitsImperial;
  function get_AscentFourdigitsImperial() {
    return AscentFourdigitsImperial.value;
  }
  function AscentFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 0, 0)];
  }
  var AscentFivedigitsMetric;
  function get_AscentFivedigitsMetric() {
    return AscentFivedigitsMetric.value;
  }
  function AscentFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 0, 0)];
  }
  var AscentFivedigitsImperial;
  function get_AscentFivedigitsImperial() {
    return AscentFivedigitsImperial.value;
  }
  function AscentSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KM, 0, 0)];
  }
  var AscentSixdigitsMetric;
  function get_AscentSixdigitsMetric() {
    return AscentSixdigitsMetric.value;
  }
  function AscentSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KFT, 0, 0)];
  }
  var AscentSixdigitsImperial;
  function get_AscentSixdigitsImperial() {
    return AscentSixdigitsImperial.value;
  }
  function PaceFourdigitsMetric$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_KM, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFourdigitsMetric;
  function get_PaceFourdigitsMetric() {
    return PaceFourdigitsMetric.value;
  }
  function PaceFourdigitsImperial$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFourdigitsImperial;
  function get_PaceFourdigitsImperial() {
    return PaceFourdigitsImperial.value;
  }
  function PaceFixedNoLeadingZeroMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_KM, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFixedNoLeadingZeroMetric;
  function get_PaceFixedNoLeadingZeroMetric() {
    return PaceFixedNoLeadingZeroMetric.value;
  }
  function PaceFixedNoLeadingZeroImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_MI, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFixedNoLeadingZeroImperial;
  function get_PaceFixedNoLeadingZeroImperial() {
    return PaceFixedNoLeadingZeroImperial.value;
  }
  function PaceFivedigitsMetric$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_KM, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFivedigitsMetric;
  function get_PaceFivedigitsMetric() {
    return PaceFivedigitsMetric.value;
  }
  function PaceFivedigitsImperial$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceFivedigitsImperial;
  function get_PaceFivedigitsImperial() {
    return PaceFivedigitsImperial.value;
  }
  function PaceSixdigitsMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_KM, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceSixdigitsMetric;
  function get_PaceSixdigitsMetric() {
    return PaceSixdigitsMetric.value;
  }
  function PaceSixdigitsImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var PaceSixdigitsImperial;
  function get_PaceSixdigitsImperial() {
    return PaceSixdigitsImperial.value;
  }
  function VO2FourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 1, 1)];
  }
  var VO2FourdigitsGeneric;
  function get_VO2FourdigitsGeneric() {
    return VO2FourdigitsGeneric.value;
  }
  function VO2FivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 1, 1)];
  }
  var VO2FivedigitsGeneric;
  function get_VO2FivedigitsGeneric() {
    return VO2FivedigitsGeneric.value;
  }
  function VO2SixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 1, 1)];
  }
  var VO2SixdigitsGeneric;
  function get_VO2SixdigitsGeneric() {
    return VO2SixdigitsGeneric.value;
  }
  function NavigationRouteDistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var NavigationRouteDistanceFourdigitsMetric;
  function get_NavigationRouteDistanceFourdigitsMetric() {
    return NavigationRouteDistanceFourdigitsMetric.value;
  }
  function NavigationRouteDistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationRouteDistanceFourdigitsImperial;
  function get_NavigationRouteDistanceFourdigitsImperial() {
    return NavigationRouteDistanceFourdigitsImperial.value;
  }
  function NavigationRouteDistanceThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(99950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(999500.0, Units_getInstance().KM, 0, 0)];
  }
  var NavigationRouteDistanceThreedigitsMetric;
  function get_NavigationRouteDistanceThreedigitsMetric() {
    return NavigationRouteDistanceThreedigitsMetric.value;
  }
  function NavigationRouteDistanceThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(99.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationRouteDistanceThreedigitsImperial;
  function get_NavigationRouteDistanceThreedigitsImperial() {
    return NavigationRouteDistanceThreedigitsImperial.value;
  }
  function NavigationRouteDistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var NavigationRouteDistanceFivedigitsMetric;
  function get_NavigationRouteDistanceFivedigitsMetric() {
    return NavigationRouteDistanceFivedigitsMetric.value;
  }
  function NavigationRouteDistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 1, 1)];
  }
  var NavigationRouteDistanceFivedigitsImperial;
  function get_NavigationRouteDistanceFivedigitsImperial() {
    return NavigationRouteDistanceFivedigitsImperial.value;
  }
  function NavigationRouteDistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var NavigationRouteDistanceSixdigitsMetric;
  function get_NavigationRouteDistanceSixdigitsMetric() {
    return NavigationRouteDistanceSixdigitsMetric.value;
  }
  function NavigationRouteDistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().MI, 2, 2)];
  }
  var NavigationRouteDistanceSixdigitsImperial;
  function get_NavigationRouteDistanceSixdigitsImperial() {
    return NavigationRouteDistanceSixdigitsImperial.value;
  }
  function NavigationRouteDistanceAccurateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var NavigationRouteDistanceAccurateMetric;
  function get_NavigationRouteDistanceAccurateMetric() {
    return NavigationRouteDistanceAccurateMetric.value;
  }
  function NavigationRouteDistanceAccurateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationRouteDistanceAccurateImperial;
  function get_NavigationRouteDistanceAccurateImperial() {
    return NavigationRouteDistanceAccurateImperial.value;
  }
  function StiffnessTwodigitsGeneric$lambda() {
    return [new RangeSimpleConversion(80.0, Units_getInstance().KNM, 1, 1)];
  }
  var StiffnessTwodigitsGeneric;
  function get_StiffnessTwodigitsGeneric() {
    return StiffnessTwodigitsGeneric.value;
  }
  function DownhillAltitudeFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KM, 2, 2)];
  }
  var DownhillAltitudeFourdigitsMetric;
  function get_DownhillAltitudeFourdigitsMetric() {
    return DownhillAltitudeFourdigitsMetric.value;
  }
  function DownhillAltitudeFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KFT, 2, 2)];
  }
  var DownhillAltitudeFourdigitsImperial;
  function get_DownhillAltitudeFourdigitsImperial() {
    return DownhillAltitudeFourdigitsImperial.value;
  }
  function DownhillAltitudeFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0)];
  }
  var DownhillAltitudeFivedigitsMetric;
  function get_DownhillAltitudeFivedigitsMetric() {
    return DownhillAltitudeFivedigitsMetric.value;
  }
  function DownhillAltitudeFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT, 0, 0)];
  }
  var DownhillAltitudeFivedigitsImperial;
  function get_DownhillAltitudeFivedigitsImperial() {
    return DownhillAltitudeFivedigitsImperial.value;
  }
  function DownhillAltitudeSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0)];
  }
  var DownhillAltitudeSixdigitsMetric;
  function get_DownhillAltitudeSixdigitsMetric() {
    return DownhillAltitudeSixdigitsMetric.value;
  }
  function DownhillAltitudeSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT, 0, 0)];
  }
  var DownhillAltitudeSixdigitsImperial;
  function get_DownhillAltitudeSixdigitsImperial() {
    return DownhillAltitudeSixdigitsImperial.value;
  }
  function CompassHeadingDegFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(360.0, Units_getInstance().DEG, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingDegFourdigitsGeneric;
  function get_CompassHeadingDegFourdigitsGeneric() {
    return CompassHeadingDegFourdigitsGeneric.value;
  }
  function CompassHeadingDegFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(360.0, Units_getInstance().DEG, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingDegFivedigitsGeneric;
  function get_CompassHeadingDegFivedigitsGeneric() {
    return CompassHeadingDegFivedigitsGeneric.value;
  }
  function CompassHeadingDegSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(360.0, Units_getInstance().DEG, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingDegSixdigitsGeneric;
  function get_CompassHeadingDegSixdigitsGeneric() {
    return CompassHeadingDegSixdigitsGeneric.value;
  }
  function CompassHeadingDegAccurateGeneric$lambda() {
    return [new RangeSimpleConversion(360.0, Units_getInstance().DEG, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingDegAccurateGeneric;
  function get_CompassHeadingDegAccurateGeneric() {
    return CompassHeadingDegAccurateGeneric.value;
  }
  function DistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var DistanceFourdigitsMetric;
  function get_DistanceFourdigitsMetric() {
    return DistanceFourdigitsMetric.value;
  }
  function DistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 0, 0)];
  }
  var DistanceFourdigitsImperial;
  function get_DistanceFourdigitsImperial() {
    return DistanceFourdigitsImperial.value;
  }
  function DistanceAccumulatedMetric$lambda() {
    return [new RangeSimpleConversion(99950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(kotlin_js_internal_DoubleCompanionObject.NaN, Units_getInstance().KM, 0, 0)];
  }
  var DistanceAccumulatedMetric;
  function get_DistanceAccumulatedMetric() {
    return DistanceAccumulatedMetric.value;
  }
  function DistanceAccumulatedImperial$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(kotlin_js_internal_DoubleCompanionObject.NaN, Units_getInstance().MI, 0, 0)];
  }
  var DistanceAccumulatedImperial;
  function get_DistanceAccumulatedImperial() {
    return DistanceAccumulatedImperial.value;
  }
  function DistanceThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(99950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(999500.0, Units_getInstance().KM, 0, 0)];
  }
  var DistanceThreedigitsMetric;
  function get_DistanceThreedigitsMetric() {
    return DistanceThreedigitsMetric.value;
  }
  function DistanceThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(99.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI, 0, 0)];
  }
  var DistanceThreedigitsImperial;
  function get_DistanceThreedigitsImperial() {
    return DistanceThreedigitsImperial.value;
  }
  function DistanceMapscaleMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DistanceMapscaleMetric;
  function get_DistanceMapscaleMetric() {
    return DistanceMapscaleMetric.value;
  }
  function DistanceMapscaleImperial$lambda() {
    return [new RangeSimpleConversion(0.38, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DistanceMapscaleImperial;
  function get_DistanceMapscaleImperial() {
    return DistanceMapscaleImperial.value;
  }
  function DistanceApproximateMetric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DistanceApproximateMetric;
  function get_DistanceApproximateMetric() {
    return DistanceApproximateMetric.value;
  }
  function DistanceApproximateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DistanceApproximateImperial;
  function get_DistanceApproximateImperial() {
    return DistanceApproximateImperial.value;
  }
  function DistanceNodecimalMetric$lambda() {
    return [new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DistanceNodecimalMetric;
  function get_DistanceNodecimalMetric() {
    return DistanceNodecimalMetric.value;
  }
  function DistanceNodecimalImperial$lambda() {
    return [new RangeSimpleConversion(621370.0, Units_getInstance().MI, 0, 0)];
  }
  var DistanceNodecimalImperial;
  function get_DistanceNodecimalImperial() {
    return DistanceNodecimalImperial.value;
  }
  function DistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var DistanceFivedigitsMetric;
  function get_DistanceFivedigitsMetric() {
    return DistanceFivedigitsMetric.value;
  }
  function DistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1)];
  }
  var DistanceFivedigitsImperial;
  function get_DistanceFivedigitsImperial() {
    return DistanceFivedigitsImperial.value;
  }
  function DistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var DistanceSixdigitsMetric;
  function get_DistanceSixdigitsMetric() {
    return DistanceSixdigitsMetric.value;
  }
  function DistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(99999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(621370.0, Units_getInstance().MI, 0, 0)];
  }
  var DistanceSixdigitsImperial;
  function get_DistanceSixdigitsImperial() {
    return DistanceSixdigitsImperial.value;
  }
  function DistanceOnedecimalMetric$lambda() {
    return [new RangeSimpleConversion(9.999995E7, Units_getInstance().KM, 1, 1)];
  }
  var DistanceOnedecimalMetric;
  function get_DistanceOnedecimalMetric() {
    return DistanceOnedecimalMetric.value;
  }
  function DistanceOnedecimalImperial$lambda() {
    return [new RangeSimpleConversion(621370.0, Units_getInstance().MI, 1, 1)];
  }
  var DistanceOnedecimalImperial;
  function get_DistanceOnedecimalImperial() {
    return DistanceOnedecimalImperial.value;
  }
  function DistanceAccurateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DistanceAccurateMetric;
  function get_DistanceAccurateMetric() {
    return DistanceAccurateMetric.value;
  }
  function DistanceAccurateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DistanceAccurateImperial;
  function get_DistanceAccurateImperial() {
    return DistanceAccurateImperial.value;
  }
  function TrackAndFieldDistanceFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var TrackAndFieldDistanceFivedigitsGeneric;
  function get_TrackAndFieldDistanceFivedigitsGeneric() {
    return TrackAndFieldDistanceFivedigitsGeneric.value;
  }
  function DescentFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 0, 0)];
  }
  var DescentFourdigitsMetric;
  function get_DescentFourdigitsMetric() {
    return DescentFourdigitsMetric.value;
  }
  function DescentFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 0, 0)];
  }
  var DescentFourdigitsImperial;
  function get_DescentFourdigitsImperial() {
    return DescentFourdigitsImperial.value;
  }
  function DescentFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 0, 0)];
  }
  var DescentFivedigitsMetric;
  function get_DescentFivedigitsMetric() {
    return DescentFivedigitsMetric.value;
  }
  function DescentFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 0, 0)];
  }
  var DescentFivedigitsImperial;
  function get_DescentFivedigitsImperial() {
    return DescentFivedigitsImperial.value;
  }
  function DescentSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KM, 0, 0)];
  }
  var DescentSixdigitsMetric;
  function get_DescentSixdigitsMetric() {
    return DescentSixdigitsMetric.value;
  }
  function DescentSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KFT, 0, 0)];
  }
  var DescentSixdigitsImperial;
  function get_DescentSixdigitsImperial() {
    return DescentSixdigitsImperial.value;
  }
  function EPOCFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var EPOCFourdigitsGeneric;
  function get_EPOCFourdigitsGeneric() {
    return EPOCFourdigitsGeneric.value;
  }
  function EPOCFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var EPOCFivedigitsGeneric;
  function get_EPOCFivedigitsGeneric() {
    return EPOCFivedigitsGeneric.value;
  }
  function EPOCSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var EPOCSixdigitsGeneric;
  function get_EPOCSixdigitsGeneric() {
    return EPOCSixdigitsGeneric.value;
  }
  function PoolSwimDistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var PoolSwimDistanceFourdigitsMetric;
  function get_PoolSwimDistanceFourdigitsMetric() {
    return PoolSwimDistanceFourdigitsMetric.value;
  }
  function PoolSwimDistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 0, 0)];
  }
  var PoolSwimDistanceFourdigitsImperial;
  function get_PoolSwimDistanceFourdigitsImperial() {
    return PoolSwimDistanceFourdigitsImperial.value;
  }
  function PoolSwimDistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var PoolSwimDistanceFivedigitsMetric;
  function get_PoolSwimDistanceFivedigitsMetric() {
    return PoolSwimDistanceFivedigitsMetric.value;
  }
  function PoolSwimDistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(999950.0, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 1, 1)];
  }
  var PoolSwimDistanceFivedigitsImperial;
  function get_PoolSwimDistanceFivedigitsImperial() {
    return PoolSwimDistanceFivedigitsImperial.value;
  }
  function PoolSwimDistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var PoolSwimDistanceSixdigitsMetric;
  function get_PoolSwimDistanceSixdigitsMetric() {
    return PoolSwimDistanceSixdigitsMetric.value;
  }
  function PoolSwimDistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().YD, 0, 0), new RangeSimpleConversion(9999500.0, Units_getInstance().MI, 2, 2)];
  }
  var PoolSwimDistanceSixdigitsImperial;
  function get_PoolSwimDistanceSixdigitsImperial() {
    return PoolSwimDistanceSixdigitsImperial.value;
  }
  function StepLengthThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(200.0, Units_getInstance().CM, 0, 0)];
  }
  var StepLengthThreedigitsGeneric;
  function get_StepLengthThreedigitsGeneric() {
    return StepLengthThreedigitsGeneric.value;
  }
  function NavigationPoiETEFourdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf_0(_h_getInstance()))];
  }
  var NavigationPoiETEFourdigitsGeneric;
  function get_NavigationPoiETEFourdigitsGeneric() {
    return NavigationPoiETEFourdigitsGeneric.value;
  }
  function NavigationPoiETEFourdigitsFixedGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETEFourdigitsFixedGeneric;
  function get_NavigationPoiETEFourdigitsFixedGeneric() {
    return NavigationPoiETEFourdigitsFixedGeneric.value;
  }
  function NavigationPoiETEHoursGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf_0(_h_getInstance()))];
  }
  var NavigationPoiETEHoursGeneric;
  function get_NavigationPoiETEHoursGeneric() {
    return NavigationPoiETEHoursGeneric.value;
  }
  function NavigationPoiETEFixedNoLeadingZeroGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().HOUR, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETEFixedNoLeadingZeroGeneric;
  function get_NavigationPoiETEFixedNoLeadingZeroGeneric() {
    return NavigationPoiETEFixedNoLeadingZeroGeneric.value;
  }
  function NavigationPoiETEHumaneGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([new Delimiter("'"), _s_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETEHumaneGeneric;
  function get_NavigationPoiETEHumaneGeneric() {
    return NavigationPoiETEHumaneGeneric.value;
  }
  function NavigationPoiETEFivedigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(36000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETEFivedigitsGeneric;
  function get_NavigationPoiETEFivedigitsGeneric() {
    return NavigationPoiETEFivedigitsGeneric.value;
  }
  function NavigationPoiETESixdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var NavigationPoiETESixdigitsGeneric;
  function get_NavigationPoiETESixdigitsGeneric() {
    return NavigationPoiETESixdigitsGeneric.value;
  }
  function NavigationPoiETEMinutesGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().SEC, listOf_0(_m_getInstance()))];
  }
  var NavigationPoiETEMinutesGeneric;
  function get_NavigationPoiETEMinutesGeneric() {
    return NavigationPoiETEMinutesGeneric.value;
  }
  function NavigationPoiETEFixedGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETEFixedGeneric;
  function get_NavigationPoiETEFixedGeneric() {
    return NavigationPoiETEFixedGeneric.value;
  }
  function NavigationPOIDistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var NavigationPOIDistanceFourdigitsMetric;
  function get_NavigationPOIDistanceFourdigitsMetric() {
    return NavigationPOIDistanceFourdigitsMetric.value;
  }
  function NavigationPOIDistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationPOIDistanceFourdigitsImperial;
  function get_NavigationPOIDistanceFourdigitsImperial() {
    return NavigationPOIDistanceFourdigitsImperial.value;
  }
  function NavigationPOIDistanceThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(99950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(999500.0, Units_getInstance().KM, 0, 0)];
  }
  var NavigationPOIDistanceThreedigitsMetric;
  function get_NavigationPOIDistanceThreedigitsMetric() {
    return NavigationPOIDistanceThreedigitsMetric.value;
  }
  function NavigationPOIDistanceThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(99.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationPOIDistanceThreedigitsImperial;
  function get_NavigationPOIDistanceThreedigitsImperial() {
    return NavigationPOIDistanceThreedigitsImperial.value;
  }
  function NavigationPOIDistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var NavigationPOIDistanceFivedigitsMetric;
  function get_NavigationPOIDistanceFivedigitsMetric() {
    return NavigationPOIDistanceFivedigitsMetric.value;
  }
  function NavigationPOIDistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 1, 1)];
  }
  var NavigationPOIDistanceFivedigitsImperial;
  function get_NavigationPOIDistanceFivedigitsImperial() {
    return NavigationPOIDistanceFivedigitsImperial.value;
  }
  function NavigationPOIDistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var NavigationPOIDistanceSixdigitsMetric;
  function get_NavigationPOIDistanceSixdigitsMetric() {
    return NavigationPOIDistanceSixdigitsMetric.value;
  }
  function NavigationPOIDistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().MI, 2, 2)];
  }
  var NavigationPOIDistanceSixdigitsImperial;
  function get_NavigationPOIDistanceSixdigitsImperial() {
    return NavigationPOIDistanceSixdigitsImperial.value;
  }
  function NavigationPOIDistanceAccurateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var NavigationPOIDistanceAccurateMetric;
  function get_NavigationPOIDistanceAccurateMetric() {
    return NavigationPOIDistanceAccurateMetric.value;
  }
  function NavigationPOIDistanceAccurateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var NavigationPOIDistanceAccurateImperial;
  function get_NavigationPOIDistanceAccurateImperial() {
    return NavigationPOIDistanceAccurateImperial.value;
  }
  function DurationFourdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DurationFourdigitsGeneric;
  function get_DurationFourdigitsGeneric() {
    return DurationFourdigitsGeneric.value;
  }
  function DurationAccumulatedGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3.6E9, Units_getInstance().SEC, listOf_0(_h_getInstance()))];
  }
  var DurationAccumulatedGeneric;
  function get_DurationAccumulatedGeneric() {
    return DurationAccumulatedGeneric.value;
  }
  function DurationHoursGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DurationHoursGeneric;
  function get_DurationHoursGeneric() {
    return DurationHoursGeneric.value;
  }
  function DurationFixedNoLeadingZeroGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationFixedNoLeadingZeroGeneric;
  function get_DurationFixedNoLeadingZeroGeneric() {
    return DurationFixedNoLeadingZeroGeneric.value;
  }
  function DurationApproximateGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf_0(_ss_getInstance())), new RangeDuration(3600.0, Units_getInstance().MIN, listOf_0(_mm_getInstance())), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DurationApproximateGeneric;
  function get_DurationApproximateGeneric() {
    return DurationApproximateGeneric.value;
  }
  function DurationMinutesGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().MIN, listOf_0(_m_getInstance()))];
  }
  var DurationMinutesGeneric;
  function get_DurationMinutesGeneric() {
    return DurationMinutesGeneric.value;
  }
  function DurationTrainingGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationTrainingGeneric;
  function get_DurationTrainingGeneric() {
    return DurationTrainingGeneric.value;
  }
  function DurationApproximateNoLeadingZeroGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().MIN, listOf_0(_m_getInstance())), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().HOUR, listOf_0(_h_getInstance()))];
  }
  var DurationApproximateNoLeadingZeroGeneric;
  function get_DurationApproximateNoLeadingZeroGeneric() {
    return DurationApproximateNoLeadingZeroGeneric.value;
  }
  function DurationFourdigitsFixedGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationFourdigitsFixedGeneric;
  function get_DurationFourdigitsFixedGeneric() {
    return DurationFourdigitsFixedGeneric.value;
  }
  function DurationHumaneGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([new Delimiter("'"), _s_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationHumaneGeneric;
  function get_DurationHumaneGeneric() {
    return DurationHumaneGeneric.value;
  }
  function DurationNodecimalGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var DurationNodecimalGeneric;
  function get_DurationNodecimalGeneric() {
    return DurationNodecimalGeneric.value;
  }
  function DurationFourdigitsFixedRoundedGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]), Method$ROUND_getInstance()), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]), Method$ROUND_getInstance())];
  }
  var DurationFourdigitsFixedRoundedGeneric;
  function get_DurationFourdigitsFixedRoundedGeneric() {
    return DurationFourdigitsFixedRoundedGeneric.value;
  }
  function DurationFivedigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(36000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationFivedigitsGeneric;
  function get_DurationFivedigitsGeneric() {
    return DurationFivedigitsGeneric.value;
  }
  function DurationSixdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var DurationSixdigitsGeneric;
  function get_DurationSixdigitsGeneric() {
    return DurationSixdigitsGeneric.value;
  }
  function DurationAccurateGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationAccurateGeneric;
  function get_DurationAccurateGeneric() {
    return DurationAccurateGeneric.value;
  }
  function DurationFixedGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationFixedGeneric;
  function get_DurationFixedGeneric() {
    return DurationFixedGeneric.value;
  }
  function CompassHeadingMilFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(6400.0, Units_getInstance().MIL, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingMilFourdigitsGeneric;
  function get_CompassHeadingMilFourdigitsGeneric() {
    return CompassHeadingMilFourdigitsGeneric.value;
  }
  function CompassHeadingMilFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(6400.0, Units_getInstance().MIL, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingMilFivedigitsGeneric;
  function get_CompassHeadingMilFivedigitsGeneric() {
    return CompassHeadingMilFivedigitsGeneric.value;
  }
  function CompassHeadingMilSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(6400.0, Units_getInstance().MIL, 0, 0, Method$FLOOR_getInstance())];
  }
  var CompassHeadingMilSixdigitsGeneric;
  function get_CompassHeadingMilSixdigitsGeneric() {
    return CompassHeadingMilSixdigitsGeneric.value;
  }
  function DownhillDescentFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDescentFourdigitsMetric;
  function get_DownhillDescentFourdigitsMetric() {
    return DownhillDescentFourdigitsMetric.value;
  }
  function DownhillDescentFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 0, 0)];
  }
  var DownhillDescentFourdigitsImperial;
  function get_DownhillDescentFourdigitsImperial() {
    return DownhillDescentFourdigitsImperial.value;
  }
  function DownhillDescentFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDescentFivedigitsMetric;
  function get_DownhillDescentFivedigitsMetric() {
    return DownhillDescentFivedigitsMetric.value;
  }
  function DownhillDescentFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 0, 0)];
  }
  var DownhillDescentFivedigitsImperial;
  function get_DownhillDescentFivedigitsImperial() {
    return DownhillDescentFivedigitsImperial.value;
  }
  function DownhillDescentSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDescentSixdigitsMetric;
  function get_DownhillDescentSixdigitsMetric() {
    return DownhillDescentSixdigitsMetric.value;
  }
  function DownhillDescentSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9999999.5, Units_getInstance().KFT, 2, 2), new RangeSimpleConversion(9.99999995E7, Units_getInstance().KFT, 1, 1), new RangeSimpleConversion(9.999999995E8, Units_getInstance().KFT, 0, 0)];
  }
  var DownhillDescentSixdigitsImperial;
  function get_DownhillDescentSixdigitsImperial() {
    return DownhillDescentSixdigitsImperial.value;
  }
  function ReactivityOnedigitGeneric$lambda() {
    return [new RangeSimpleConversion(2.0, Units_getInstance().SCALAR, 2, 2)];
  }
  var ReactivityOnedigitGeneric;
  function get_ReactivityOnedigitGeneric() {
    return ReactivityOnedigitGeneric.value;
  }
  function ContactTimeFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(2000.0, Units_getInstance().MS, 0, 0)];
  }
  var ContactTimeFourdigitsGeneric;
  function get_ContactTimeFourdigitsGeneric() {
    return ContactTimeFourdigitsGeneric.value;
  }
  function WeigthFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KG, 1, 1)];
  }
  var WeigthFourdigitsMetric;
  function get_WeigthFourdigitsMetric() {
    return WeigthFourdigitsMetric.value;
  }
  function WeigthFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().LB, 1, 1)];
  }
  var WeigthFourdigitsImperial;
  function get_WeigthFourdigitsImperial() {
    return WeigthFourdigitsImperial.value;
  }
  function WeigthFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KG, 1, 1)];
  }
  var WeigthFivedigitsMetric;
  function get_WeigthFivedigitsMetric() {
    return WeigthFivedigitsMetric.value;
  }
  function WeigthFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().LB, 1, 1)];
  }
  var WeigthFivedigitsImperial;
  function get_WeigthFivedigitsImperial() {
    return WeigthFivedigitsImperial.value;
  }
  function WeigthSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KG, 1, 1)];
  }
  var WeigthSixdigitsMetric;
  function get_WeigthSixdigitsMetric() {
    return WeigthSixdigitsMetric.value;
  }
  function WeigthSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().LB, 1, 1)];
  }
  var WeigthSixdigitsImperial;
  function get_WeigthSixdigitsImperial() {
    return WeigthSixdigitsImperial.value;
  }
  function CadenceSpmFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SPM, 0, 0)];
  }
  var CadenceSpmFourdigitsGeneric;
  function get_CadenceSpmFourdigitsGeneric() {
    return CadenceSpmFourdigitsGeneric.value;
  }
  function CadenceSpmFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SPM, 0, 0)];
  }
  var CadenceSpmFivedigitsGeneric;
  function get_CadenceSpmFivedigitsGeneric() {
    return CadenceSpmFivedigitsGeneric.value;
  }
  function CadenceSpmSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SPM, 0, 0)];
  }
  var CadenceSpmSixdigitsGeneric;
  function get_CadenceSpmSixdigitsGeneric() {
    return CadenceSpmSixdigitsGeneric.value;
  }
  function DeclinationFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(90.05, Units_getInstance().DEG, 1, 1)];
  }
  var DeclinationFourdigitsGeneric;
  function get_DeclinationFourdigitsGeneric() {
    return DeclinationFourdigitsGeneric.value;
  }
  function DeclinationFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(90.05, Units_getInstance().DEG, 1, 1)];
  }
  var DeclinationFivedigitsGeneric;
  function get_DeclinationFivedigitsGeneric() {
    return DeclinationFivedigitsGeneric.value;
  }
  function DeclinationSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(90.05, Units_getInstance().DEG, 1, 1)];
  }
  var DeclinationSixdigitsGeneric;
  function get_DeclinationSixdigitsGeneric() {
    return DeclinationSixdigitsGeneric.value;
  }
  function PerformanceFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var PerformanceFourdigitsGeneric;
  function get_PerformanceFourdigitsGeneric() {
    return PerformanceFourdigitsGeneric.value;
  }
  function PerformanceFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var PerformanceFivedigitsGeneric;
  function get_PerformanceFivedigitsGeneric() {
    return PerformanceFivedigitsGeneric.value;
  }
  function PerformanceSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var PerformanceSixdigitsGeneric;
  function get_PerformanceSixdigitsGeneric() {
    return PerformanceSixdigitsGeneric.value;
  }
  function DiveGasConsumptionOnedecimalMetric$lambda() {
    return [new RangeSimpleConversion(6.0E7, Units_getInstance().L_PER_MIN, 0, 1)];
  }
  var DiveGasConsumptionOnedecimalMetric;
  function get_DiveGasConsumptionOnedecimalMetric() {
    return DiveGasConsumptionOnedecimalMetric.value;
  }
  function DiveGasConsumptionOnedecimalImperial$lambda() {
    return [new RangeSimpleConversion(3000000.0, Units_getInstance().FT3_PER_MIN, 0, 1)];
  }
  var DiveGasConsumptionOnedecimalImperial;
  function get_DiveGasConsumptionOnedecimalImperial() {
    return DiveGasConsumptionOnedecimalImperial.value;
  }
  function DiveDurationAccurateGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(86400.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3.6E8, Units_getInstance().DAY, listOf_0(_d_getInstance()))];
  }
  var DiveDurationAccurateGeneric;
  function get_DiveDurationAccurateGeneric() {
    return DiveDurationAccurateGeneric.value;
  }
  function DownhillDistanceFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDistanceFourdigitsMetric;
  function get_DownhillDistanceFourdigitsMetric() {
    return DownhillDistanceFourdigitsMetric.value;
  }
  function DownhillDistanceFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 0, 0)];
  }
  var DownhillDistanceFourdigitsImperial;
  function get_DownhillDistanceFourdigitsImperial() {
    return DownhillDistanceFourdigitsImperial.value;
  }
  function DownhillDistanceThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(99950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(999500.0, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDistanceThreedigitsMetric;
  function get_DownhillDistanceThreedigitsMetric() {
    return DownhillDistanceThreedigitsMetric.value;
  }
  function DownhillDistanceThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(99.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI, 0, 0)];
  }
  var DownhillDistanceThreedigitsImperial;
  function get_DownhillDistanceThreedigitsImperial() {
    return DownhillDistanceThreedigitsImperial.value;
  }
  function DownhillDistanceApproximateMetric$lambda() {
    return [new RangeSimpleConversion(99.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(9999.5, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDistanceApproximateMetric;
  function get_DownhillDistanceApproximateMetric() {
    return DownhillDistanceApproximateMetric.value;
  }
  function DownhillDistanceApproximateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(9.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DownhillDistanceApproximateImperial;
  function get_DownhillDistanceApproximateImperial() {
    return DownhillDistanceApproximateImperial.value;
  }
  function DownhillDistanceFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999950.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 1, 1)];
  }
  var DownhillDistanceFivedigitsMetric;
  function get_DownhillDistanceFivedigitsMetric() {
    return DownhillDistanceFivedigitsMetric.value;
  }
  function DownhillDistanceFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.5, Units_getInstance().MI, 1, 1)];
  }
  var DownhillDistanceFivedigitsImperial;
  function get_DownhillDistanceFivedigitsImperial() {
    return DownhillDistanceFivedigitsImperial.value;
  }
  function DownhillDistanceSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999500.0, Units_getInstance().KM, 2, 2)];
  }
  var DownhillDistanceSixdigitsMetric;
  function get_DownhillDistanceSixdigitsMetric() {
    return DownhillDistanceSixdigitsMetric.value;
  }
  function DownhillDistanceSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().MI, 2, 2)];
  }
  var DownhillDistanceSixdigitsImperial;
  function get_DownhillDistanceSixdigitsImperial() {
    return DownhillDistanceSixdigitsImperial.value;
  }
  function DownhillDistanceAccurateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DownhillDistanceAccurateMetric;
  function get_DownhillDistanceAccurateMetric() {
    return DownhillDistanceAccurateMetric.value;
  }
  function DownhillDistanceAccurateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DownhillDistanceAccurateImperial;
  function get_DownhillDistanceAccurateImperial() {
    return DownhillDistanceAccurateImperial.value;
  }
  function HeartRateFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().BPM, 0, 0)];
  }
  var HeartRateFourdigitsGeneric;
  function get_HeartRateFourdigitsGeneric() {
    return HeartRateFourdigitsGeneric.value;
  }
  function HeartRateFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().BPM, 0, 0)];
  }
  var HeartRateFivedigitsGeneric;
  function get_HeartRateFivedigitsGeneric() {
    return HeartRateFivedigitsGeneric.value;
  }
  function HeartRateSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().BPM, 0, 0)];
  }
  var HeartRateSixdigitsGeneric;
  function get_HeartRateSixdigitsGeneric() {
    return HeartRateSixdigitsGeneric.value;
  }
  function TemperatureFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().C, 0, 0)];
  }
  var TemperatureFourdigitsMetric;
  function get_TemperatureFourdigitsMetric() {
    return TemperatureFourdigitsMetric.value;
  }
  function TemperatureFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().F, 0, 0)];
  }
  var TemperatureFourdigitsImperial;
  function get_TemperatureFourdigitsImperial() {
    return TemperatureFourdigitsImperial.value;
  }
  function TemperatureFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().C, 0, 0)];
  }
  var TemperatureFivedigitsMetric;
  function get_TemperatureFivedigitsMetric() {
    return TemperatureFivedigitsMetric.value;
  }
  function TemperatureFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().F, 0, 0)];
  }
  var TemperatureFivedigitsImperial;
  function get_TemperatureFivedigitsImperial() {
    return TemperatureFivedigitsImperial.value;
  }
  function TemperatureSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().C, 0, 0)];
  }
  var TemperatureSixdigitsMetric;
  function get_TemperatureSixdigitsMetric() {
    return TemperatureSixdigitsMetric.value;
  }
  function TemperatureSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().F, 0, 0)];
  }
  var TemperatureSixdigitsImperial;
  function get_TemperatureSixdigitsImperial() {
    return TemperatureSixdigitsImperial.value;
  }
  function UndulationThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(100.0, Units_getInstance().CM, 1, 1)];
  }
  var UndulationThreedigitsGeneric;
  function get_UndulationThreedigitsGeneric() {
    return UndulationThreedigitsGeneric.value;
  }
  function DurationMsFourdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationMsFourdigitsGeneric;
  function get_DurationMsFourdigitsGeneric() {
    return DurationMsFourdigitsGeneric.value;
  }
  function DurationMsApproximateGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf_0(_ss_getInstance())), new RangeDuration(3600.0, Units_getInstance().MIN, listOf_0(_mm_getInstance())), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationMsApproximateGeneric;
  function get_DurationMsApproximateGeneric() {
    return DurationMsApproximateGeneric.value;
  }
  function DurationMsFivedigitsGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(36000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var DurationMsFivedigitsGeneric;
  function get_DurationMsFivedigitsGeneric() {
    return DurationMsFivedigitsGeneric.value;
  }
  function DurationMsSixdigitsGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var DurationMsSixdigitsGeneric;
  function get_DurationMsSixdigitsGeneric() {
    return DurationMsSixdigitsGeneric.value;
  }
  function DurationMsAccurateGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var DurationMsAccurateGeneric;
  function get_DurationMsAccurateGeneric() {
    return DurationMsAccurateGeneric.value;
  }
  function NavigationRouteETEFourdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf_0(_h_getInstance()))];
  }
  var NavigationRouteETEFourdigitsGeneric;
  function get_NavigationRouteETEFourdigitsGeneric() {
    return NavigationRouteETEFourdigitsGeneric.value;
  }
  function NavigationRouteETEFourdigitsFixedGeneric$lambda() {
    return [new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(360000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETEFourdigitsFixedGeneric;
  function get_NavigationRouteETEFourdigitsFixedGeneric() {
    return NavigationRouteETEFourdigitsFixedGeneric.value;
  }
  function NavigationRouteETEHoursGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf_0(_h_getInstance()))];
  }
  var NavigationRouteETEHoursGeneric;
  function get_NavigationRouteETEHoursGeneric() {
    return NavigationRouteETEHoursGeneric.value;
  }
  function NavigationRouteETEFixedNoLeadingZeroGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().HOUR, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETEFixedNoLeadingZeroGeneric;
  function get_NavigationRouteETEFixedNoLeadingZeroGeneric() {
    return NavigationRouteETEFixedNoLeadingZeroGeneric.value;
  }
  function NavigationRouteETEHumaneGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([new Delimiter("'"), _s_getInstance()])), new RangeDuration(3600.0, Units_getInstance().SEC, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETEHumaneGeneric;
  function get_NavigationRouteETEHumaneGeneric() {
    return NavigationRouteETEHumaneGeneric.value;
  }
  function NavigationRouteETEFivedigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(36000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETEFivedigitsGeneric;
  function get_NavigationRouteETEFivedigitsGeneric() {
    return NavigationRouteETEFivedigitsGeneric.value;
  }
  function NavigationRouteETESixdigitsGeneric$lambda() {
    return [new RangeDuration(60.0, Units_getInstance().SEC, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance(), new Delimiter('.'), _f_getInstance()])), new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var NavigationRouteETESixdigitsGeneric;
  function get_NavigationRouteETESixdigitsGeneric() {
    return NavigationRouteETESixdigitsGeneric.value;
  }
  function NavigationRouteETEMinutesGeneric$lambda() {
    return [new RangeDuration(360000.0, Units_getInstance().SEC, listOf_0(_m_getInstance()))];
  }
  var NavigationRouteETEMinutesGeneric;
  function get_NavigationRouteETEMinutesGeneric() {
    return NavigationRouteETEMinutesGeneric.value;
  }
  function NavigationRouteETEFixedGeneric$lambda() {
    return [new RangeDuration(3600000.0, Units_getInstance().SEC, listOf([_hh_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETEFixedGeneric;
  function get_NavigationRouteETEFixedGeneric() {
    return NavigationRouteETEFixedGeneric.value;
  }
  function RowingPaceFourdigitsMetric$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_500M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFourdigitsMetric;
  function get_RowingPaceFourdigitsMetric() {
    return RowingPaceFourdigitsMetric.value;
  }
  function RowingPaceFourdigitsImperial$lambda() {
    return [new RangeDuration(5999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFourdigitsImperial;
  function get_RowingPaceFourdigitsImperial() {
    return RowingPaceFourdigitsImperial.value;
  }
  function RowingPaceFixedNoLeadingZeroMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_500M, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFixedNoLeadingZeroMetric;
  function get_RowingPaceFixedNoLeadingZeroMetric() {
    return RowingPaceFixedNoLeadingZeroMetric.value;
  }
  function RowingPaceFixedNoLeadingZeroImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_MI, listOf([_m_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFixedNoLeadingZeroImperial;
  function get_RowingPaceFixedNoLeadingZeroImperial() {
    return RowingPaceFixedNoLeadingZeroImperial.value;
  }
  function RowingPaceFivedigitsMetric$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_500M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFivedigitsMetric;
  function get_RowingPaceFivedigitsMetric() {
    return RowingPaceFivedigitsMetric.value;
  }
  function RowingPaceFivedigitsImperial$lambda() {
    return [new RangeDuration(59999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceFivedigitsImperial;
  function get_RowingPaceFivedigitsImperial() {
    return RowingPaceFivedigitsImperial.value;
  }
  function RowingPaceSixdigitsMetric$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_500M, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceSixdigitsMetric;
  function get_RowingPaceSixdigitsMetric() {
    return RowingPaceSixdigitsMetric.value;
  }
  function RowingPaceSixdigitsImperial$lambda() {
    return [new RangeDuration(599999.5, Units_getInstance().S_PER_MI, listOf([_mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var RowingPaceSixdigitsImperial;
  function get_RowingPaceSixdigitsImperial() {
    return RowingPaceSixdigitsImperial.value;
  }
  function DiveDistanceAccurateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M, 1, 1), new RangeSimpleConversion(99995.0, Units_getInstance().KM, 2, 2), new RangeSimpleConversion(9999950.0, Units_getInstance().KM, 1, 1), new RangeSimpleConversion(9.99995E7, Units_getInstance().KM, 0, 0)];
  }
  var DiveDistanceAccurateMetric;
  function get_DiveDistanceAccurateMetric() {
    return DiveDistanceAccurateMetric.value;
  }
  function DiveDistanceAccurateImperial$lambda() {
    return [new RangeSimpleConversion(0.1, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99.995, Units_getInstance().MI, 2, 2), new RangeSimpleConversion(9999.95, Units_getInstance().MI, 1, 1), new RangeSimpleConversion(99999.5, Units_getInstance().MI, 0, 0)];
  }
  var DiveDistanceAccurateImperial;
  function get_DiveDistanceAccurateImperial() {
    return DiveDistanceAccurateImperial.value;
  }
  function NauticalDistanceFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9.995, Units_getInstance().NMI, 2, 2), new RangeSimpleConversion(999.95, Units_getInstance().NMI, 1, 1), new RangeSimpleConversion(9999.5, Units_getInstance().NMI, 0, 0)];
  }
  var NauticalDistanceFourdigitsGeneric;
  function get_NauticalDistanceFourdigitsGeneric() {
    return NauticalDistanceFourdigitsGeneric.value;
  }
  function NauticalDistanceFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().NMI, 2, 2), new RangeSimpleConversion(9999.5, Units_getInstance().NMI, 1, 1)];
  }
  var NauticalDistanceFivedigitsGeneric;
  function get_NauticalDistanceFivedigitsGeneric() {
    return NauticalDistanceFivedigitsGeneric.value;
  }
  function NauticalDistanceSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().NMI, 2, 2)];
  }
  var NauticalDistanceSixdigitsGeneric;
  function get_NauticalDistanceSixdigitsGeneric() {
    return NauticalDistanceSixdigitsGeneric.value;
  }
  function EnergyFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().KCAL, 0, 0)];
  }
  var EnergyFourdigitsGeneric;
  function get_EnergyFourdigitsGeneric() {
    return EnergyFourdigitsGeneric.value;
  }
  function EnergyAccumulatedGeneric$lambda() {
    return [new RangeSimpleConversion(9.99999995E7, Units_getInstance().KCAL, 0, 0)];
  }
  var EnergyAccumulatedGeneric;
  function get_EnergyAccumulatedGeneric() {
    return EnergyAccumulatedGeneric.value;
  }
  function EnergyFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().KCAL, 0, 0)];
  }
  var EnergyFivedigitsGeneric;
  function get_EnergyFivedigitsGeneric() {
    return EnergyFivedigitsGeneric.value;
  }
  function EnergySixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().KCAL, 0, 0)];
  }
  var EnergySixdigitsGeneric;
  function get_EnergySixdigitsGeneric() {
    return EnergySixdigitsGeneric.value;
  }
  function NavigationPoiETAFourdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETAFourdigits24;
  function get_NavigationPoiETAFourdigits24() {
    return NavigationPoiETAFourdigits24.value;
  }
  function NavigationPoiETAFourdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETAFourdigits12;
  function get_NavigationPoiETAFourdigits12() {
    return NavigationPoiETAFourdigits12.value;
  }
  function NavigationPoiETAFivedigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETAFivedigits24;
  function get_NavigationPoiETAFivedigits24() {
    return NavigationPoiETAFivedigits24.value;
  }
  function NavigationPoiETAFivedigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETAFivedigits12;
  function get_NavigationPoiETAFivedigits12() {
    return NavigationPoiETAFivedigits12.value;
  }
  function NavigationPoiETASixdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETASixdigits24;
  function get_NavigationPoiETASixdigits24() {
    return NavigationPoiETASixdigits24.value;
  }
  function NavigationPoiETASixdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationPoiETASixdigits12;
  function get_NavigationPoiETASixdigits12() {
    return NavigationPoiETASixdigits12.value;
  }
  function StrokeRateFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().SPM, 0, 0)];
  }
  var StrokeRateFourdigitsGeneric;
  function get_StrokeRateFourdigitsGeneric() {
    return StrokeRateFourdigitsGeneric.value;
  }
  function StrokeRateFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().SPM, 0, 0)];
  }
  var StrokeRateFivedigitsGeneric;
  function get_StrokeRateFivedigitsGeneric() {
    return StrokeRateFivedigitsGeneric.value;
  }
  function StrokeRateSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().SPM, 0, 0)];
  }
  var StrokeRateSixdigitsGeneric;
  function get_StrokeRateSixdigitsGeneric() {
    return StrokeRateSixdigitsGeneric.value;
  }
  function SpeedFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var SpeedFourdigitsMetric;
  function get_SpeedFourdigitsMetric() {
    return SpeedFourdigitsMetric.value;
  }
  function SpeedFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var SpeedFourdigitsImperial;
  function get_SpeedFourdigitsImperial() {
    return SpeedFourdigitsImperial.value;
  }
  function SpeedThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().KM_PER_H, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().KM_PER_H, 0, 0)];
  }
  var SpeedThreedigitsMetric;
  function get_SpeedThreedigitsMetric() {
    return SpeedThreedigitsMetric.value;
  }
  function SpeedThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().MI_PER_H, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().MI_PER_H, 0, 0)];
  }
  var SpeedThreedigitsImperial;
  function get_SpeedThreedigitsImperial() {
    return SpeedThreedigitsImperial.value;
  }
  function SpeedApproximateMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().KM_PER_H, 0, 0)];
  }
  var SpeedApproximateMetric;
  function get_SpeedApproximateMetric() {
    return SpeedApproximateMetric.value;
  }
  function SpeedApproximateImperial$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().MI_PER_H, 0, 0)];
  }
  var SpeedApproximateImperial;
  function get_SpeedApproximateImperial() {
    return SpeedApproximateImperial.value;
  }
  function SpeedFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var SpeedFivedigitsMetric;
  function get_SpeedFivedigitsMetric() {
    return SpeedFivedigitsMetric.value;
  }
  function SpeedFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var SpeedFivedigitsImperial;
  function get_SpeedFivedigitsImperial() {
    return SpeedFivedigitsImperial.value;
  }
  function SpeedSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.95, Units_getInstance().KM_PER_H, 1, 1)];
  }
  var SpeedSixdigitsMetric;
  function get_SpeedSixdigitsMetric() {
    return SpeedSixdigitsMetric.value;
  }
  function SpeedSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.95, Units_getInstance().MI_PER_H, 1, 1)];
  }
  var SpeedSixdigitsImperial;
  function get_SpeedSixdigitsImperial() {
    return SpeedSixdigitsImperial.value;
  }
  function HeartRatePercentageFourdigitsGeneric$lambda() {
    return [new RangePercentage(999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var HeartRatePercentageFourdigitsGeneric;
  function get_HeartRatePercentageFourdigitsGeneric() {
    return HeartRatePercentageFourdigitsGeneric.value;
  }
  function HeartRatePercentageThreedigitsGeneric$lambda() {
    return [new RangePercentage(999.5, Units_getInstance().PERCENT, 0, 0)];
  }
  var HeartRatePercentageThreedigitsGeneric;
  function get_HeartRatePercentageThreedigitsGeneric() {
    return HeartRatePercentageThreedigitsGeneric.value;
  }
  function HeartRatePercentageFivedigitsGeneric$lambda() {
    return [new RangePercentage(9999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var HeartRatePercentageFivedigitsGeneric;
  function get_HeartRatePercentageFivedigitsGeneric() {
    return HeartRatePercentageFivedigitsGeneric.value;
  }
  function HeartRatePercentageSixdigitsGeneric$lambda() {
    return [new RangePercentage(99999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var HeartRatePercentageSixdigitsGeneric;
  function get_HeartRatePercentageSixdigitsGeneric() {
    return HeartRatePercentageSixdigitsGeneric.value;
  }
  function TrainingEffectFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.0, Units_getInstance().SCALAR, 1, 1)];
  }
  var TrainingEffectFourdigitsGeneric;
  function get_TrainingEffectFourdigitsGeneric() {
    return TrainingEffectFourdigitsGeneric.value;
  }
  function TrainingEffectFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.0, Units_getInstance().SCALAR, 1, 1)];
  }
  var TrainingEffectFivedigitsGeneric;
  function get_TrainingEffectFivedigitsGeneric() {
    return TrainingEffectFivedigitsGeneric.value;
  }
  function TrainingEffectSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(5.0, Units_getInstance().SCALAR, 1, 1)];
  }
  var TrainingEffectSixdigitsGeneric;
  function get_TrainingEffectSixdigitsGeneric() {
    return TrainingEffectSixdigitsGeneric.value;
  }
  function TimeOfDayFourdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDayFourdigits24;
  function get_TimeOfDayFourdigits24() {
    return TimeOfDayFourdigits24.value;
  }
  function TimeOfDayFourdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDayFourdigits12;
  function get_TimeOfDayFourdigits12() {
    return TimeOfDayFourdigits12.value;
  }
  function TimeOfDayFivedigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDayFivedigits24;
  function get_TimeOfDayFivedigits24() {
    return TimeOfDayFivedigits24.value;
  }
  function TimeOfDayFivedigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDayFivedigits12;
  function get_TimeOfDayFivedigits12() {
    return TimeOfDayFivedigits12.value;
  }
  function TimeOfDaySixdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDaySixdigits24;
  function get_TimeOfDaySixdigits24() {
    return TimeOfDaySixdigits24.value;
  }
  function TimeOfDaySixdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var TimeOfDaySixdigits12;
  function get_TimeOfDaySixdigits12() {
    return TimeOfDaySixdigits12.value;
  }
  function TimeOfDayAccurate24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var TimeOfDayAccurate24;
  function get_TimeOfDayAccurate24() {
    return TimeOfDayAccurate24.value;
  }
  function TimeOfDayAccurate12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var TimeOfDayAccurate12;
  function get_TimeOfDayAccurate12() {
    return TimeOfDayAccurate12.value;
  }
  function DownhillLapCountFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var DownhillLapCountFourdigitsGeneric;
  function get_DownhillLapCountFourdigitsGeneric() {
    return DownhillLapCountFourdigitsGeneric.value;
  }
  function DownhillLapCountThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var DownhillLapCountThreedigitsGeneric;
  function get_DownhillLapCountThreedigitsGeneric() {
    return DownhillLapCountThreedigitsGeneric.value;
  }
  function DownhillLapCountFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var DownhillLapCountFivedigitsGeneric;
  function get_DownhillLapCountFivedigitsGeneric() {
    return DownhillLapCountFivedigitsGeneric.value;
  }
  function DownhillLapCountSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var DownhillLapCountSixdigitsGeneric;
  function get_DownhillLapCountSixdigitsGeneric() {
    return DownhillLapCountSixdigitsGeneric.value;
  }
  function PercentageFourdigitsGeneric$lambda() {
    return [new RangePercentage(999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var PercentageFourdigitsGeneric;
  function get_PercentageFourdigitsGeneric() {
    return PercentageFourdigitsGeneric.value;
  }
  function PercentageThreedigitsGeneric$lambda() {
    return [new RangePercentage(999.5, Units_getInstance().PERCENT, 0, 0)];
  }
  var PercentageThreedigitsGeneric;
  function get_PercentageThreedigitsGeneric() {
    return PercentageThreedigitsGeneric.value;
  }
  function PercentageFivedigitsGeneric$lambda() {
    return [new RangePercentage(9999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var PercentageFivedigitsGeneric;
  function get_PercentageFivedigitsGeneric() {
    return PercentageFivedigitsGeneric.value;
  }
  function PercentageSixdigitsGeneric$lambda() {
    return [new RangePercentage(99999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var PercentageSixdigitsGeneric;
  function get_PercentageSixdigitsGeneric() {
    return PercentageSixdigitsGeneric.value;
  }
  function VerticalSpeedMountainFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainFourdigitsMetric;
  function get_VerticalSpeedMountainFourdigitsMetric() {
    return VerticalSpeedMountainFourdigitsMetric.value;
  }
  function VerticalSpeedMountainFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainFourdigitsImperial;
  function get_VerticalSpeedMountainFourdigitsImperial() {
    return VerticalSpeedMountainFourdigitsImperial.value;
  }
  function VerticalSpeedMountainThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainThreedigitsMetric;
  function get_VerticalSpeedMountainThreedigitsMetric() {
    return VerticalSpeedMountainThreedigitsMetric.value;
  }
  function VerticalSpeedMountainThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().FT_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainThreedigitsImperial;
  function get_VerticalSpeedMountainThreedigitsImperial() {
    return VerticalSpeedMountainThreedigitsImperial.value;
  }
  function VerticalSpeedMountainFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainFivedigitsMetric;
  function get_VerticalSpeedMountainFivedigitsMetric() {
    return VerticalSpeedMountainFivedigitsMetric.value;
  }
  function VerticalSpeedMountainFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainFivedigitsImperial;
  function get_VerticalSpeedMountainFivedigitsImperial() {
    return VerticalSpeedMountainFivedigitsImperial.value;
  }
  function VerticalSpeedMountainSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainSixdigitsMetric;
  function get_VerticalSpeedMountainSixdigitsMetric() {
    return VerticalSpeedMountainSixdigitsMetric.value;
  }
  function VerticalSpeedMountainSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT_PER_H, 0, 0)];
  }
  var VerticalSpeedMountainSixdigitsImperial;
  function get_VerticalSpeedMountainSixdigitsImperial() {
    return VerticalSpeedMountainSixdigitsImperial.value;
  }
  function NauticalSpeedFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().KN, 1, 1)];
  }
  var NauticalSpeedFourdigitsGeneric;
  function get_NauticalSpeedFourdigitsGeneric() {
    return NauticalSpeedFourdigitsGeneric.value;
  }
  function NauticalSpeedThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().KN, 1, 1), new RangeSimpleConversion(999.5, Units_getInstance().KN, 0, 0)];
  }
  var NauticalSpeedThreedigitsGeneric;
  function get_NauticalSpeedThreedigitsGeneric() {
    return NauticalSpeedThreedigitsGeneric.value;
  }
  function NauticalSpeedApproximateGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().KN, 0, 0)];
  }
  var NauticalSpeedApproximateGeneric;
  function get_NauticalSpeedApproximateGeneric() {
    return NauticalSpeedApproximateGeneric.value;
  }
  function NauticalSpeedFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().KN, 1, 1)];
  }
  var NauticalSpeedFivedigitsGeneric;
  function get_NauticalSpeedFivedigitsGeneric() {
    return NauticalSpeedFivedigitsGeneric.value;
  }
  function NauticalSpeedSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.95, Units_getInstance().KN, 1, 1)];
  }
  var NauticalSpeedSixdigitsGeneric;
  function get_NauticalSpeedSixdigitsGeneric() {
    return NauticalSpeedSixdigitsGeneric.value;
  }
  function SwolfFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var SwolfFourdigitsGeneric;
  function get_SwolfFourdigitsGeneric() {
    return SwolfFourdigitsGeneric.value;
  }
  function SwolfThreedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var SwolfThreedigitsGeneric;
  function get_SwolfThreedigitsGeneric() {
    return SwolfThreedigitsGeneric.value;
  }
  function SwolfFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var SwolfFivedigitsGeneric;
  function get_SwolfFivedigitsGeneric() {
    return SwolfFivedigitsGeneric.value;
  }
  function SwolfSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().SCALAR, 0, 0)];
  }
  var SwolfSixdigitsGeneric;
  function get_SwolfSixdigitsGeneric() {
    return SwolfSixdigitsGeneric.value;
  }
  function DownhillGradeFourdigitsGeneric$lambda() {
    return [new RangePercentage(999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var DownhillGradeFourdigitsGeneric;
  function get_DownhillGradeFourdigitsGeneric() {
    return DownhillGradeFourdigitsGeneric.value;
  }
  function DownhillGradeTwodigitsGeneric$lambda() {
    return [new RangePercentage(99.5, Units_getInstance().PERCENT, 0, 0)];
  }
  var DownhillGradeTwodigitsGeneric;
  function get_DownhillGradeTwodigitsGeneric() {
    return DownhillGradeTwodigitsGeneric.value;
  }
  function DownhillGradeThreedigitsGeneric$lambda() {
    return [new RangePercentage(999.5, Units_getInstance().PERCENT, 0, 0)];
  }
  var DownhillGradeThreedigitsGeneric;
  function get_DownhillGradeThreedigitsGeneric() {
    return DownhillGradeThreedigitsGeneric.value;
  }
  function DownhillGradeFivedigitsGeneric$lambda() {
    return [new RangePercentage(9999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var DownhillGradeFivedigitsGeneric;
  function get_DownhillGradeFivedigitsGeneric() {
    return DownhillGradeFivedigitsGeneric.value;
  }
  function DownhillGradeSixdigitsGeneric$lambda() {
    return [new RangePercentage(99999.95, Units_getInstance().PERCENT, 1, 1)];
  }
  var DownhillGradeSixdigitsGeneric;
  function get_DownhillGradeSixdigitsGeneric() {
    return DownhillGradeSixdigitsGeneric.value;
  }
  function SunsetFourdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetFourdigits24;
  function get_SunsetFourdigits24() {
    return SunsetFourdigits24.value;
  }
  function SunsetFourdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetFourdigits12;
  function get_SunsetFourdigits12() {
    return SunsetFourdigits12.value;
  }
  function SunsetFivedigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetFivedigits24;
  function get_SunsetFivedigits24() {
    return SunsetFivedigits24.value;
  }
  function SunsetFivedigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetFivedigits12;
  function get_SunsetFivedigits12() {
    return SunsetFivedigits12.value;
  }
  function SunsetSixdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetSixdigits24;
  function get_SunsetSixdigits24() {
    return SunsetSixdigits24.value;
  }
  function SunsetSixdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunsetSixdigits12;
  function get_SunsetSixdigits12() {
    return SunsetSixdigits12.value;
  }
  function SunsetAccurate24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SunsetAccurate24;
  function get_SunsetAccurate24() {
    return SunsetAccurate24.value;
  }
  function SunsetAccurate12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SunsetAccurate12;
  function get_SunsetAccurate12() {
    return SunsetAccurate12.value;
  }
  function NavigationRouteETAFourdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETAFourdigits24;
  function get_NavigationRouteETAFourdigits24() {
    return NavigationRouteETAFourdigits24.value;
  }
  function NavigationRouteETAFourdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETAFourdigits12;
  function get_NavigationRouteETAFourdigits12() {
    return NavigationRouteETAFourdigits12.value;
  }
  function NavigationRouteETAFivedigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETAFivedigits24;
  function get_NavigationRouteETAFivedigits24() {
    return NavigationRouteETAFivedigits24.value;
  }
  function NavigationRouteETAFivedigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETAFivedigits12;
  function get_NavigationRouteETAFivedigits12() {
    return NavigationRouteETAFivedigits12.value;
  }
  function NavigationRouteETASixdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETASixdigits24;
  function get_NavigationRouteETASixdigits24() {
    return NavigationRouteETASixdigits24.value;
  }
  function NavigationRouteETASixdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var NavigationRouteETASixdigits12;
  function get_NavigationRouteETASixdigits12() {
    return NavigationRouteETASixdigits12.value;
  }
  function SunriseFourdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseFourdigits24;
  function get_SunriseFourdigits24() {
    return SunriseFourdigits24.value;
  }
  function SunriseFourdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseFourdigits12;
  function get_SunriseFourdigits12() {
    return SunriseFourdigits12.value;
  }
  function SunriseFivedigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseFivedigits24;
  function get_SunriseFivedigits24() {
    return SunriseFivedigits24.value;
  }
  function SunriseFivedigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseFivedigits12;
  function get_SunriseFivedigits12() {
    return SunriseFivedigits12.value;
  }
  function SunriseSixdigits24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseSixdigits24;
  function get_SunriseSixdigits24() {
    return SunriseSixdigits24.value;
  }
  function SunriseSixdigits12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance()]))];
  }
  var SunriseSixdigits12;
  function get_SunriseSixdigits12() {
    return SunriseSixdigits12.value;
  }
  function SunriseAccurate24$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([H_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SunriseAccurate24;
  function get_SunriseAccurate24() {
    return SunriseAccurate24.value;
  }
  function SunriseAccurate12$lambda() {
    return [new RangeDateTime(1.0E12, Units_getInstance().SEC, listOf([_h_getInstance(), new Delimiter(':'), _mm_getInstance(), new Delimiter("'"), _ss_getInstance()]))];
  }
  var SunriseAccurate12;
  function get_SunriseAccurate12() {
    return SunriseAccurate12.value;
  }
  function VerticalSpeedFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M_PER_MIN, 0, 0)];
  }
  var VerticalSpeedFourdigitsMetric;
  function get_VerticalSpeedFourdigitsMetric() {
    return VerticalSpeedFourdigitsMetric.value;
  }
  function VerticalSpeedFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT_PER_MIN, 0, 0)];
  }
  var VerticalSpeedFourdigitsImperial;
  function get_VerticalSpeedFourdigitsImperial() {
    return VerticalSpeedFourdigitsImperial.value;
  }
  function VerticalSpeedThreedigitsMetric$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().M_PER_MIN, 0, 0)];
  }
  var VerticalSpeedThreedigitsMetric;
  function get_VerticalSpeedThreedigitsMetric() {
    return VerticalSpeedThreedigitsMetric.value;
  }
  function VerticalSpeedThreedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.5, Units_getInstance().FT_PER_MIN, 0, 0)];
  }
  var VerticalSpeedThreedigitsImperial;
  function get_VerticalSpeedThreedigitsImperial() {
    return VerticalSpeedThreedigitsImperial.value;
  }
  function VerticalSpeedFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M_PER_MIN, 0, 0)];
  }
  var VerticalSpeedFivedigitsMetric;
  function get_VerticalSpeedFivedigitsMetric() {
    return VerticalSpeedFivedigitsMetric.value;
  }
  function VerticalSpeedFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT_PER_MIN, 0, 0)];
  }
  var VerticalSpeedFivedigitsImperial;
  function get_VerticalSpeedFivedigitsImperial() {
    return VerticalSpeedFivedigitsImperial.value;
  }
  function VerticalSpeedSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M_PER_MIN, 0, 0)];
  }
  var VerticalSpeedSixdigitsMetric;
  function get_VerticalSpeedSixdigitsMetric() {
    return VerticalSpeedSixdigitsMetric.value;
  }
  function VerticalSpeedSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT_PER_MIN, 0, 0)];
  }
  var VerticalSpeedSixdigitsImperial;
  function get_VerticalSpeedSixdigitsImperial() {
    return VerticalSpeedSixdigitsImperial.value;
  }
  function AirPressureFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().HPA, 0, 0)];
  }
  var AirPressureFourdigitsMetric;
  function get_AirPressureFourdigitsMetric() {
    return AirPressureFourdigitsMetric.value;
  }
  function AirPressureFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(99.95, Units_getInstance().INHG, 2, 2)];
  }
  var AirPressureFourdigitsImperial;
  function get_AirPressureFourdigitsImperial() {
    return AirPressureFourdigitsImperial.value;
  }
  function AirPressureFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().HPA, 0, 0)];
  }
  var AirPressureFivedigitsMetric;
  function get_AirPressureFivedigitsMetric() {
    return AirPressureFivedigitsMetric.value;
  }
  function AirPressureFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(999.95, Units_getInstance().INHG, 2, 2)];
  }
  var AirPressureFivedigitsImperial;
  function get_AirPressureFivedigitsImperial() {
    return AirPressureFivedigitsImperial.value;
  }
  function AirPressureSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().HPA, 0, 0)];
  }
  var AirPressureSixdigitsMetric;
  function get_AirPressureSixdigitsMetric() {
    return AirPressureSixdigitsMetric.value;
  }
  function AirPressureSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.95, Units_getInstance().INHG, 2, 2)];
  }
  var AirPressureSixdigitsImperial;
  function get_AirPressureSixdigitsImperial() {
    return AirPressureSixdigitsImperial.value;
  }
  function PowerFourdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().W, 0, 0)];
  }
  var PowerFourdigitsGeneric;
  function get_PowerFourdigitsGeneric() {
    return PowerFourdigitsGeneric.value;
  }
  function PowerFivedigitsGeneric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().W, 0, 0)];
  }
  var PowerFivedigitsGeneric;
  function get_PowerFivedigitsGeneric() {
    return PowerFivedigitsGeneric.value;
  }
  function PowerSixdigitsGeneric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().W, 0, 0)];
  }
  var PowerSixdigitsGeneric;
  function get_PowerSixdigitsGeneric() {
    return PowerSixdigitsGeneric.value;
  }
  function PowerAccurateGeneric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().W, 0, 0)];
  }
  var PowerAccurateGeneric;
  function get_PowerAccurateGeneric() {
    return PowerAccurateGeneric.value;
  }
  function AltitudeFourdigitsMetric$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().M, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KM, 2, 2)];
  }
  var AltitudeFourdigitsMetric;
  function get_AltitudeFourdigitsMetric() {
    return AltitudeFourdigitsMetric.value;
  }
  function AltitudeFourdigitsImperial$lambda() {
    return [new RangeSimpleConversion(9999.5, Units_getInstance().FT, 0, 0), new RangeSimpleConversion(99999.5, Units_getInstance().KFT, 2, 2)];
  }
  var AltitudeFourdigitsImperial;
  function get_AltitudeFourdigitsImperial() {
    return AltitudeFourdigitsImperial.value;
  }
  function AltitudeFivedigitsMetric$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().M, 0, 0)];
  }
  var AltitudeFivedigitsMetric;
  function get_AltitudeFivedigitsMetric() {
    return AltitudeFivedigitsMetric.value;
  }
  function AltitudeFivedigitsImperial$lambda() {
    return [new RangeSimpleConversion(99999.5, Units_getInstance().FT, 0, 0)];
  }
  var AltitudeFivedigitsImperial;
  function get_AltitudeFivedigitsImperial() {
    return AltitudeFivedigitsImperial.value;
  }
  function AltitudeSixdigitsMetric$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().M, 0, 0)];
  }
  var AltitudeSixdigitsMetric;
  function get_AltitudeSixdigitsMetric() {
    return AltitudeSixdigitsMetric.value;
  }
  function AltitudeSixdigitsImperial$lambda() {
    return [new RangeSimpleConversion(999999.5, Units_getInstance().FT, 0, 0)];
  }
  var AltitudeSixdigitsImperial;
  function get_AltitudeSixdigitsImperial() {
    return AltitudeSixdigitsImperial.value;
  }
  function ensureTwoDigits($receiver) {
    return $receiver.length < 2 ? '0' + $receiver : $receiver;
  }
  function ensureThreeDigits($receiver) {
    return $receiver.length === 2 ? '0' + $receiver : $receiver.length === 1 ? '00' + $receiver : $receiver;
  }
  function ensureTwoDigits_0($receiver) {
    return ensureTwoDigits($receiver.toString());
  }
  function ensureThreeDigits_0($receiver) {
    return ensureThreeDigits($receiver.toString());
  }
  function ensureTwoDigits_1($receiver) {
    return ensureTwoDigits($receiver.toString());
  }
  function formatDuration(durationInSeconds, format) {
    var duration = toDuration(durationInSeconds, DurationUnit.SECONDS);
    var tmp$ = first(format).inWholeDuration_cgako$(duration);
    var wholeDuration = tmp$.component1()
    , remainingDuration = tmp$.component2();
    var result = new StringBuilder(wholeDuration);
    remainingDuration.inWholeDays;
    var hours = remainingDuration.hoursComponent;
    var minutes = remainingDuration.minutesComponent;
    var seconds = remainingDuration.secondsComponent;
    var milliseconds = ensureThreeDigits_0(remainingDuration.nanosecondsComponent / 1000000 | 0);
    var tmp$_0;
    tmp$_0 = drop(format, 1).iterator();
    while (tmp$_0.hasNext()) {
      var element = tmp$_0.next();
      var tmp$_1;
      if (Kotlin.isType(element, Delimiter))
        tmp$_1 = element.delimiter;
      else if (equals(element, _a_getInstance())) {
        throw new NotImplementedError_init();
      } else if (equals(element, _d_getInstance()))
        throw IllegalArgumentException_init('Days should only be used as first time format');
      else if (equals(element, _hh_getInstance()) || equals(element, HH_getInstance()))
        tmp$_1 = ensureTwoDigits_0(hours);
      else if (equals(element, _h_getInstance()) || equals(element, H_getInstance()))
        tmp$_1 = hours.toString();
      else if (equals(element, _mm_getInstance()))
        tmp$_1 = ensureTwoDigits_0(minutes);
      else if (equals(element, _m_getInstance()))
        tmp$_1 = minutes.toString();
      else if (equals(element, _ss_getInstance()))
        tmp$_1 = ensureTwoDigits_0(seconds);
      else if (equals(element, _s_getInstance()))
        tmp$_1 = seconds.toString();
      else if (equals(element, _f_getInstance()))
        tmp$_1 = take(milliseconds, 1);
      else if (equals(element, _ff_getInstance()))
        tmp$_1 = take(milliseconds, 2);
      else if (equals(element, _fff_getInstance()))
        tmp$_1 = take(milliseconds, 3);
      else
        tmp$_1 = Kotlin.noWhenBranchMatched();
      var value = tmp$_1;
      result.append_pdl1vj$(value);
    }
    return result.toString();
  }
  function TimeFormatToken() {
  }
  TimeFormatToken.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'TimeFormatToken',
    interfaces: []
  };
  function Delimiter(delimiter) {
    TimeFormatToken.call(this);
    this.delimiter = delimiter;
    this.formatSpecifierToken_c3de3u$_0 = this.delimiter;
  }
  Object.defineProperty(Delimiter.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_c3de3u$_0;
    }
  });
  Delimiter.prototype.inWholeDuration_cgako$ = function (duration) {
    throw IllegalArgumentException_init('Delimiter does not support whole duration');
  };
  Delimiter.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Delimiter',
    interfaces: [TimeFormatToken]
  };
  function _a() {
    _a_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_n5jeml$_0 = 'a';
  }
  Object.defineProperty(_a.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_n5jeml$_0;
    }
  });
  _a.prototype.inWholeDuration_cgako$ = function (duration) {
    throw new NotImplementedError_init('An operation is not implemented: ' + 'Not yet implemented');
  };
  _a.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_a',
    interfaces: [TimeFormatToken]
  };
  var _a_instance = null;
  function _a_getInstance() {
    if (_a_instance === null) {
      new _a();
    }
    return _a_instance;
  }
  function _d() {
    _d_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_wfclca$_0 = 'd';
  }
  Object.defineProperty(_d.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_wfclca$_0;
    }
  });
  _d.prototype.inWholeDuration_cgako$ = function (duration) {
    var inWholeDays = duration.inWholeDays;
    return to(inWholeDays.toString(), duration.minus_cgako$(toDuration_0(inWholeDays, DurationUnit.DAYS)));
  };
  _d.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_d',
    interfaces: [TimeFormatToken]
  };
  var _d_instance = null;
  function _d_getInstance() {
    if (_d_instance === null) {
      new _d();
    }
    return _d_instance;
  }
  function _hh() {
    _hh_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_yi3w9u$_0 = 'hh';
  }
  Object.defineProperty(_hh.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_yi3w9u$_0;
    }
  });
  _hh.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeHours = duration.inWholeHours;
    return to(ensureTwoDigits_1(wholeHours), duration.minus_cgako$(toDuration_0(wholeHours, DurationUnit.HOURS)));
  };
  _hh.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_hh',
    interfaces: [TimeFormatToken]
  };
  var _hh_instance = null;
  function _hh_getInstance() {
    if (_hh_instance === null) {
      new _hh();
    }
    return _hh_instance;
  }
  function _h() {
    _h_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_l425mu$_0 = 'h';
  }
  Object.defineProperty(_h.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_l425mu$_0;
    }
  });
  _h.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeHours = duration.inWholeHours;
    return to(wholeHours.toString(), duration.minus_cgako$(toDuration_0(wholeHours, DurationUnit.HOURS)));
  };
  _h.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_h',
    interfaces: [TimeFormatToken]
  };
  var _h_instance = null;
  function _h_getInstance() {
    if (_h_instance === null) {
      new _h();
    }
    return _h_instance;
  }
  function HH() {
    HH_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_f3pa9b$_0 = 'HH';
  }
  Object.defineProperty(HH.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_f3pa9b$_0;
    }
  });
  HH.prototype.inWholeDuration_cgako$ = function (duration) {
    return _hh_getInstance().inWholeDuration_cgako$(duration);
  };
  HH.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'HH',
    interfaces: [TimeFormatToken]
  };
  var HH_instance = null;
  function HH_getInstance() {
    if (HH_instance === null) {
      new HH();
    }
    return HH_instance;
  }
  function H() {
    H_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_jsk59x$_0 = 'H';
  }
  Object.defineProperty(H.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_jsk59x$_0;
    }
  });
  H.prototype.inWholeDuration_cgako$ = function (duration) {
    return _h_getInstance().inWholeDuration_cgako$(duration);
  };
  H.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'H',
    interfaces: [TimeFormatToken]
  };
  var H_instance = null;
  function H_getInstance() {
    if (H_instance === null) {
      new H();
    }
    return H_instance;
  }
  function _mm() {
    _mm_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_a631a6$_0 = 'mm';
  }
  Object.defineProperty(_mm.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_a631a6$_0;
    }
  });
  _mm.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeMinutes = duration.inWholeMinutes;
    return to(ensureTwoDigits_1(wholeMinutes), duration.minus_cgako$(toDuration_0(wholeMinutes, DurationUnit.MINUTES)));
  };
  _mm.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_mm',
    interfaces: [TimeFormatToken]
  };
  var _mm_instance = null;
  function _mm_getInstance() {
    if (_mm_instance === null) {
      new _mm();
    }
    return _mm_instance;
  }
  function _m() {
    _m_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_asbwhr$_0 = 'm';
  }
  Object.defineProperty(_m.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_asbwhr$_0;
    }
  });
  _m.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeMinutes = duration.inWholeMinutes;
    return to(wholeMinutes.toString(), duration.minus_cgako$(toDuration_0(wholeMinutes, DurationUnit.MINUTES)));
  };
  _m.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_m',
    interfaces: [TimeFormatToken]
  };
  var _m_instance = null;
  function _m_getInstance() {
    if (_m_instance === null) {
      new _m();
    }
    return _m_instance;
  }
  function _ss() {
    _ss_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_zd2j5q$_0 = 'ss';
  }
  Object.defineProperty(_ss.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_zd2j5q$_0;
    }
  });
  _ss.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeSeconds = duration.inWholeSeconds;
    return to(ensureTwoDigits_1(wholeSeconds), duration.minus_cgako$(toDuration_0(wholeSeconds, DurationUnit.SECONDS)));
  };
  _ss.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_ss',
    interfaces: [TimeFormatToken]
  };
  var _ss_instance = null;
  function _ss_getInstance() {
    if (_ss_instance === null) {
      new _ss();
    }
    return _ss_instance;
  }
  function _s() {
    _s_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_7ragxn$_0 = 's';
  }
  Object.defineProperty(_s.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_7ragxn$_0;
    }
  });
  _s.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeSeconds = duration.inWholeSeconds;
    return to(wholeSeconds.toString(), duration.minus_cgako$(toDuration_0(wholeSeconds, DurationUnit.SECONDS)));
  };
  _s.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_s',
    interfaces: [TimeFormatToken]
  };
  var _s_instance = null;
  function _s_getInstance() {
    if (_s_instance === null) {
      new _s();
    }
    return _s_instance;
  }
  function _f() {
    _f_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_8quni0$_0 = 'S';
  }
  Object.defineProperty(_f.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_8quni0$_0;
    }
  });
  _f.prototype.inWholeDuration_cgako$ = function (duration) {
    var wholeMilliseconds = duration.inWholeMilliseconds;
    return to(wholeMilliseconds.toString(), duration.minus_cgako$(toDuration_0(wholeMilliseconds, DurationUnit.MILLISECONDS)));
  };
  _f.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_f',
    interfaces: [TimeFormatToken]
  };
  var _f_instance = null;
  function _f_getInstance() {
    if (_f_instance === null) {
      new _f();
    }
    return _f_instance;
  }
  function _ff() {
    _ff_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_4gaz32$_0 = 'S';
  }
  Object.defineProperty(_ff.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_4gaz32$_0;
    }
  });
  _ff.prototype.inWholeDuration_cgako$ = function (duration) {
    return _f_getInstance().inWholeDuration_cgako$(duration);
  };
  _ff.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_ff',
    interfaces: [TimeFormatToken]
  };
  var _ff_instance = null;
  function _ff_getInstance() {
    if (_ff_instance === null) {
      new _ff();
    }
    return _ff_instance;
  }
  function _fff() {
    _fff_instance = this;
    TimeFormatToken.call(this);
    this.formatSpecifierToken_ckiwmw$_0 = 'S';
  }
  Object.defineProperty(_fff.prototype, 'formatSpecifierToken', {
    configurable: true,
    get: function () {
      return this.formatSpecifierToken_ckiwmw$_0;
    }
  });
  _fff.prototype.inWholeDuration_cgako$ = function (duration) {
    return _f_getInstance().inWholeDuration_cgako$(duration);
  };
  _fff.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: '_fff',
    interfaces: [TimeFormatToken]
  };
  var _fff_instance = null;
  function _fff_getInstance() {
    if (_fff_instance === null) {
      new _fff();
    }
    return _fff_instance;
  }
  function Dimension(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Dimension_initFields() {
    Dimension_initFields = function () {
    };
    Dimension$OTHER_instance = new Dimension('OTHER', 0);
    Dimension$LENGTH_instance = new Dimension('LENGTH', 1);
    Dimension$TEMPERATURE_instance = new Dimension('TEMPERATURE', 2);
    Dimension$VELOCITY_instance = new Dimension('VELOCITY', 3);
    Dimension$PRESSURE_instance = new Dimension('PRESSURE', 4);
    Dimension$ENERGY_instance = new Dimension('ENERGY', 5);
    Dimension$FREQUENCY_instance = new Dimension('FREQUENCY', 6);
    Dimension$VOLUMETRIC_FLOW_instance = new Dimension('VOLUMETRIC_FLOW', 7);
    Dimension$ANGLE_instance = new Dimension('ANGLE', 8);
    Dimension$WEIGHT_instance = new Dimension('WEIGHT', 9);
    Dimension$TIME_instance = new Dimension('TIME', 10);
    Dimension$POWER_instance = new Dimension('POWER', 11);
  }
  var Dimension$OTHER_instance;
  function Dimension$OTHER_getInstance() {
    Dimension_initFields();
    return Dimension$OTHER_instance;
  }
  var Dimension$LENGTH_instance;
  function Dimension$LENGTH_getInstance() {
    Dimension_initFields();
    return Dimension$LENGTH_instance;
  }
  var Dimension$TEMPERATURE_instance;
  function Dimension$TEMPERATURE_getInstance() {
    Dimension_initFields();
    return Dimension$TEMPERATURE_instance;
  }
  var Dimension$VELOCITY_instance;
  function Dimension$VELOCITY_getInstance() {
    Dimension_initFields();
    return Dimension$VELOCITY_instance;
  }
  var Dimension$PRESSURE_instance;
  function Dimension$PRESSURE_getInstance() {
    Dimension_initFields();
    return Dimension$PRESSURE_instance;
  }
  var Dimension$ENERGY_instance;
  function Dimension$ENERGY_getInstance() {
    Dimension_initFields();
    return Dimension$ENERGY_instance;
  }
  var Dimension$FREQUENCY_instance;
  function Dimension$FREQUENCY_getInstance() {
    Dimension_initFields();
    return Dimension$FREQUENCY_instance;
  }
  var Dimension$VOLUMETRIC_FLOW_instance;
  function Dimension$VOLUMETRIC_FLOW_getInstance() {
    Dimension_initFields();
    return Dimension$VOLUMETRIC_FLOW_instance;
  }
  var Dimension$ANGLE_instance;
  function Dimension$ANGLE_getInstance() {
    Dimension_initFields();
    return Dimension$ANGLE_instance;
  }
  var Dimension$WEIGHT_instance;
  function Dimension$WEIGHT_getInstance() {
    Dimension_initFields();
    return Dimension$WEIGHT_instance;
  }
  var Dimension$TIME_instance;
  function Dimension$TIME_getInstance() {
    Dimension_initFields();
    return Dimension$TIME_instance;
  }
  var Dimension$POWER_instance;
  function Dimension$POWER_getInstance() {
    Dimension_initFields();
    return Dimension$POWER_instance;
  }
  Dimension.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Dimension',
    interfaces: [Enum]
  };
  function Dimension$values() {
    return [Dimension$OTHER_getInstance(), Dimension$LENGTH_getInstance(), Dimension$TEMPERATURE_getInstance(), Dimension$VELOCITY_getInstance(), Dimension$PRESSURE_getInstance(), Dimension$ENERGY_getInstance(), Dimension$FREQUENCY_getInstance(), Dimension$VOLUMETRIC_FLOW_getInstance(), Dimension$ANGLE_getInstance(), Dimension$WEIGHT_getInstance(), Dimension$TIME_getInstance(), Dimension$POWER_getInstance()];
  }
  Dimension.values = Dimension$values;
  function Dimension$valueOf(name) {
    switch (name) {
      case 'OTHER':
        return Dimension$OTHER_getInstance();
      case 'LENGTH':
        return Dimension$LENGTH_getInstance();
      case 'TEMPERATURE':
        return Dimension$TEMPERATURE_getInstance();
      case 'VELOCITY':
        return Dimension$VELOCITY_getInstance();
      case 'PRESSURE':
        return Dimension$PRESSURE_getInstance();
      case 'ENERGY':
        return Dimension$ENERGY_getInstance();
      case 'FREQUENCY':
        return Dimension$FREQUENCY_getInstance();
      case 'VOLUMETRIC_FLOW':
        return Dimension$VOLUMETRIC_FLOW_getInstance();
      case 'ANGLE':
        return Dimension$ANGLE_getInstance();
      case 'WEIGHT':
        return Dimension$WEIGHT_getInstance();
      case 'TIME':
        return Dimension$TIME_getInstance();
      case 'POWER':
        return Dimension$POWER_getInstance();
      default:
        throwISE('No enum constant com.suunto.sim.formatting.internal.unitconversion.Dimension.' + name);
    }
  }
  Dimension.valueOf_61zpoe$ = Dimension$valueOf;
  function regex$lambda() {
    return Regex_init('^-(0(\\.0*)?)$');
  }
  var regex;
  function get_regex() {
    return regex.value;
  }
  function LocaleOptions(minimumFractionDigits, maximumFractionDigits, useGrouping) {
    this.minimumFractionDigits = minimumFractionDigits;
    this.maximumFractionDigits = maximumFractionDigits;
    this.useGrouping = useGrouping;
  }
  LocaleOptions.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'LocaleOptions',
    interfaces: []
  };
  LocaleOptions.prototype.component1 = function () {
    return this.minimumFractionDigits;
  };
  LocaleOptions.prototype.component2 = function () {
    return this.maximumFractionDigits;
  };
  LocaleOptions.prototype.component3 = function () {
    return this.useGrouping;
  };
  LocaleOptions.prototype.copy_ydzd23$ = function (minimumFractionDigits, maximumFractionDigits, useGrouping) {
    return new LocaleOptions(minimumFractionDigits === void 0 ? this.minimumFractionDigits : minimumFractionDigits, maximumFractionDigits === void 0 ? this.maximumFractionDigits : maximumFractionDigits, useGrouping === void 0 ? this.useGrouping : useGrouping);
  };
  LocaleOptions.prototype.toString = function () {
    return 'LocaleOptions(minimumFractionDigits=' + Kotlin.toString(this.minimumFractionDigits) + (', maximumFractionDigits=' + Kotlin.toString(this.maximumFractionDigits)) + (', useGrouping=' + Kotlin.toString(this.useGrouping)) + ')';
  };
  LocaleOptions.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.minimumFractionDigits) | 0;
    result = result * 31 + Kotlin.hashCode(this.maximumFractionDigits) | 0;
    result = result * 31 + Kotlin.hashCode(this.useGrouping) | 0;
    return result;
  };
  LocaleOptions.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.minimumFractionDigits, other.minimumFractionDigits) && Kotlin.equals(this.maximumFractionDigits, other.maximumFractionDigits) && Kotlin.equals(this.useGrouping, other.useGrouping)))));
  };
  var locale;
  function DecimalFormatter(decimalsMin, decimalsMax, method) {
    if (method === void 0)
      method = Method$ROUND_getInstance();
    this.method_0 = method;
    if (!(decimalsMin >= 0)) {
      var message = 'decimalsMin must be positive or 0';
      throw IllegalArgumentException_init(message.toString());
    }
    if (!(decimalsMax >= 0)) {
      var message_0 = 'decimalsMax must be positive or 0';
      throw IllegalArgumentException_init(message_0.toString());
    }
    this.localeOptions_0 = new LocaleOptions(decimalsMin, decimalsMax, false);
    this.multiplier_0 = JsMath.pow(10.0, decimalsMax);
  }
  DecimalFormatter.prototype.roundToString_14dthe$ = function (value) {
    var $receiver = this.format_1zw1ma$(this.applyRoundMethod_1zw1ma$(value));
    return get_regex().replace_x2uqeu$($receiver, '$1');
  };
  DecimalFormatter.prototype.applyRoundMethod_1zw1ma$ = function ($receiver) {
    var $receiver_0 = $receiver * this.multiplier_0;
    var tmp$;
    switch (this.method_0.name) {
      case 'FLOOR':
        tmp$ = JsMath.floor($receiver_0);
        break;
      case 'ROUND':
        tmp$ = roundToLong(JsMath.abs($receiver_0)).toNumber() * nativeSign($receiver_0);
        break;
      case 'TRUNCATE':
        tmp$ = nativeTrunc($receiver_0);
        break;
      default:
        tmp$ = Kotlin.noWhenBranchMatched();
        break;
    }
    return tmp$ / this.multiplier_0;
  };
  DecimalFormatter.prototype.format_1zw1ma$ = function ($receiver) {
    var tmp$;
    return typeof (tmp$ = $receiver.toLocaleString(locale, this.localeOptions_0)) === 'string' ? tmp$ : throwCCE();
  };
  DecimalFormatter.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'DecimalFormatter',
    interfaces: []
  };
  var package$com = _.com || (_.com = {});
  var package$suunto = package$com.suunto || (package$com.suunto = {});
  var package$sim = package$suunto.sim || (package$suunto.sim = {});
  var package$formatting = package$sim.formatting || (package$sim.formatting = {});
  package$formatting.ConversionResult = ConversionResult;
  package$formatting.ConversionSuccess = ConversionSuccess;
  package$formatting.ConversionFailure = ConversionFailure;
  package$formatting.format = format;
  package$formatting.formatWithStyle = formatWithStyle;
  package$formatting.FormatResult = FormatResult;
  package$formatting.FormatSuccess = FormatSuccess;
  package$formatting.FormatFailure = FormatFailure;
  package$formatting.FormattingOptions = FormattingOptions;
  Object.defineProperty(Icon, 'SWIM_PACE', {
    get: Icon$SWIM_PACE_getInstance
  });
  Object.defineProperty(Icon, 'HEART_RATE', {
    get: Icon$HEART_RATE_getInstance
  });
  Object.defineProperty(Icon, 'AIR_PRESSURE', {
    get: Icon$AIR_PRESSURE_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_SPEED', {
    get: Icon$DOWNHILL_SPEED_getInstance
  });
  Object.defineProperty(Icon, 'CADENCE', {
    get: Icon$CADENCE_getInstance
  });
  Object.defineProperty(Icon, 'STROKES', {
    get: Icon$STROKES_getInstance
  });
  Object.defineProperty(Icon, 'TRAINING_EFFECT', {
    get: Icon$TRAINING_EFFECT_getInstance
  });
  Object.defineProperty(Icon, 'SWIM_DISTANCE', {
    get: Icon$SWIM_DISTANCE_getInstance
  });
  Object.defineProperty(Icon, 'COUNT', {
    get: Icon$COUNT_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_DURATION', {
    get: Icon$DOWNHILL_DURATION_getInstance
  });
  Object.defineProperty(Icon, 'FLIGHT_TIME', {
    get: Icon$FLIGHT_TIME_getInstance
  });
  Object.defineProperty(Icon, 'RECOVERY_TIME', {
    get: Icon$RECOVERY_TIME_getInstance
  });
  Object.defineProperty(Icon, 'ASCENT', {
    get: Icon$ASCENT_getInstance
  });
  Object.defineProperty(Icon, 'PACE', {
    get: Icon$PACE_getInstance
  });
  Object.defineProperty(Icon, 'VO2', {
    get: Icon$VO2_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_ROUTE', {
    get: Icon$NAVIGATION_ROUTE_getInstance
  });
  Object.defineProperty(Icon, 'STIFFNESS', {
    get: Icon$STIFFNESS_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_ALTITUDE', {
    get: Icon$DOWNHILL_ALTITUDE_getInstance
  });
  Object.defineProperty(Icon, 'COMPASS_HEADING_DEG', {
    get: Icon$COMPASS_HEADING_DEG_getInstance
  });
  Object.defineProperty(Icon, 'DISTANCE', {
    get: Icon$DISTANCE_getInstance
  });
  Object.defineProperty(Icon, 'DESCENT', {
    get: Icon$DESCENT_getInstance
  });
  Object.defineProperty(Icon, 'EPOC', {
    get: Icon$EPOC_getInstance
  });
  Object.defineProperty(Icon, 'STEPLENGTH', {
    get: Icon$STEPLENGTH_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_ETE_POI', {
    get: Icon$NAVIGATION_ETE_POI_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_POI', {
    get: Icon$NAVIGATION_POI_getInstance
  });
  Object.defineProperty(Icon, 'DURATION', {
    get: Icon$DURATION_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_DESCENT', {
    get: Icon$DOWNHILL_DESCENT_getInstance
  });
  Object.defineProperty(Icon, 'REACTIVITY', {
    get: Icon$REACTIVITY_getInstance
  });
  Object.defineProperty(Icon, 'CONTACT_TIME', {
    get: Icon$CONTACT_TIME_getInstance
  });
  Object.defineProperty(Icon, 'WEIGHT', {
    get: Icon$WEIGHT_getInstance
  });
  Object.defineProperty(Icon, 'PERFORMANCE', {
    get: Icon$PERFORMANCE_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_DISTANCE', {
    get: Icon$DOWNHILL_DISTANCE_getInstance
  });
  Object.defineProperty(Icon, 'TEMPERATURE', {
    get: Icon$TEMPERATURE_getInstance
  });
  Object.defineProperty(Icon, 'UNDULATION', {
    get: Icon$UNDULATION_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_ETE_ROUTE', {
    get: Icon$NAVIGATION_ETE_ROUTE_getInstance
  });
  Object.defineProperty(Icon, 'ROWING_PACE', {
    get: Icon$ROWING_PACE_getInstance
  });
  Object.defineProperty(Icon, 'NAUTICAL_DISTANCE', {
    get: Icon$NAUTICAL_DISTANCE_getInstance
  });
  Object.defineProperty(Icon, 'CALORIES', {
    get: Icon$CALORIES_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_POI_ETA', {
    get: Icon$NAVIGATION_POI_ETA_getInstance
  });
  Object.defineProperty(Icon, 'SPEED', {
    get: Icon$SPEED_getInstance
  });
  Object.defineProperty(Icon, 'HEART_RATE_PERCENTAGE', {
    get: Icon$HEART_RATE_PERCENTAGE_getInstance
  });
  Object.defineProperty(Icon, 'TIME_OF_DAY', {
    get: Icon$TIME_OF_DAY_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_LAP_COUNT', {
    get: Icon$DOWNHILL_LAP_COUNT_getInstance
  });
  Object.defineProperty(Icon, 'PERCENTAGE', {
    get: Icon$PERCENTAGE_getInstance
  });
  Object.defineProperty(Icon, 'VERTICAL_SPEED', {
    get: Icon$VERTICAL_SPEED_getInstance
  });
  Object.defineProperty(Icon, 'NAUTICAL_SPEED', {
    get: Icon$NAUTICAL_SPEED_getInstance
  });
  Object.defineProperty(Icon, 'SWOLF', {
    get: Icon$SWOLF_getInstance
  });
  Object.defineProperty(Icon, 'DOWNHILL_GRADE', {
    get: Icon$DOWNHILL_GRADE_getInstance
  });
  Object.defineProperty(Icon, 'SUNSET', {
    get: Icon$SUNSET_getInstance
  });
  Object.defineProperty(Icon, 'NAVIGATION_ROUTE_ETA', {
    get: Icon$NAVIGATION_ROUTE_ETA_getInstance
  });
  Object.defineProperty(Icon, 'SUNRISE', {
    get: Icon$SUNRISE_getInstance
  });
  Object.defineProperty(Icon, 'POWER', {
    get: Icon$POWER_getInstance
  });
  Object.defineProperty(Icon, 'ALTITUDE', {
    get: Icon$ALTITUDE_getInstance
  });
  package$formatting.Icon = Icon;
  Object.defineProperty(MeasurementSystem, 'METRIC', {
    get: MeasurementSystem$METRIC_getInstance
  });
  Object.defineProperty(MeasurementSystem, 'IMPERIAL', {
    get: MeasurementSystem$IMPERIAL_getInstance
  });
  package$formatting.MeasurementSystem = MeasurementSystem;
  Object.defineProperty(Method, 'FLOOR', {
    get: Method$FLOOR_getInstance
  });
  Object.defineProperty(Method, 'ROUND', {
    get: Method$ROUND_getInstance
  });
  Object.defineProperty(Method, 'TRUNCATE', {
    get: Method$TRUNCATE_getInstance
  });
  package$formatting.Method = Method;
  package$formatting.Unit = Unit;
  package$formatting.TemperatureConversionUnit = TemperatureConversionUnit;
  package$formatting.FactorialConversionUnit = FactorialConversionUnit;
  Object.defineProperty(package$formatting, 'Units', {
    get: Units_getInstance
  });
  BaseFormatter.ConversionData = BaseFormatter$ConversionData;
  var package$internal = package$formatting.internal || (package$formatting.internal = {});
  package$internal.BaseFormatter = BaseFormatter;
  Object.defineProperty(package$internal, 'Formatter', {
    get: Formatter_getInstance
  });
  package$internal.Range = Range;
  package$internal.RangeSimpleConversion = RangeSimpleConversion;
  package$internal.RangeDateTime = RangeDateTime;
  package$internal.RangeDuration = RangeDuration;
  package$internal.RangePercentage = RangePercentage;
  Object.defineProperty(package$internal, 'SwimPaceFourdigitsMetric_8be2vx$', {
    get: get_SwimPaceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimPaceFourdigitsImperial_8be2vx$', {
    get: get_SwimPaceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'SwimPaceFixedNoLeadingZeroMetric_8be2vx$', {
    get: get_SwimPaceFixedNoLeadingZeroMetric
  });
  Object.defineProperty(package$internal, 'SwimPaceFixedNoLeadingZeroImperial_8be2vx$', {
    get: get_SwimPaceFixedNoLeadingZeroImperial
  });
  Object.defineProperty(package$internal, 'SwimPaceFivedigitsMetric_8be2vx$', {
    get: get_SwimPaceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimPaceFivedigitsImperial_8be2vx$', {
    get: get_SwimPaceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'SwimPaceSixdigitsMetric_8be2vx$', {
    get: get_SwimPaceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimPaceSixdigitsImperial_8be2vx$', {
    get: get_SwimPaceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'HeartRateBpmFourdigitsGeneric_8be2vx$', {
    get: get_HeartRateBpmFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRateBpmFivedigitsGeneric_8be2vx$', {
    get: get_HeartRateBpmFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DiveGasPressureNodecimalMetric_8be2vx$', {
    get: get_DiveGasPressureNodecimalMetric
  });
  Object.defineProperty(package$internal, 'DiveGasPressureNodecimalImperial_8be2vx$', {
    get: get_DiveGasPressureNodecimalImperial
  });
  Object.defineProperty(package$internal, 'DownhillSpeedFourdigitsMetric_8be2vx$', {
    get: get_DownhillSpeedFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillSpeedFourdigitsImperial_8be2vx$', {
    get: get_DownhillSpeedFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillSpeedThreedigitsMetric_8be2vx$', {
    get: get_DownhillSpeedThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillSpeedThreedigitsImperial_8be2vx$', {
    get: get_DownhillSpeedThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillSpeedApproximateMetric_8be2vx$', {
    get: get_DownhillSpeedApproximateMetric
  });
  Object.defineProperty(package$internal, 'DownhillSpeedApproximateImperial_8be2vx$', {
    get: get_DownhillSpeedApproximateImperial
  });
  Object.defineProperty(package$internal, 'DownhillSpeedFivedigitsMetric_8be2vx$', {
    get: get_DownhillSpeedFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillSpeedFivedigitsImperial_8be2vx$', {
    get: get_DownhillSpeedFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillSpeedSixdigitsMetric_8be2vx$', {
    get: get_DownhillSpeedSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillSpeedSixdigitsImperial_8be2vx$', {
    get: get_DownhillSpeedSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'CadenceFourdigitsGeneric_8be2vx$', {
    get: get_CadenceFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CadenceFivedigitsGeneric_8be2vx$', {
    get: get_CadenceFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CadenceSixdigitsGeneric_8be2vx$', {
    get: get_CadenceSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokesFourdigitsGeneric_8be2vx$', {
    get: get_StrokesFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokesThreedigitsGeneric_8be2vx$', {
    get: get_StrokesThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokesFivedigitsGeneric_8be2vx$', {
    get: get_StrokesFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokesSixdigitsGeneric_8be2vx$', {
    get: get_StrokesSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PeakTrainingEffectFourdigitsGeneric_8be2vx$', {
    get: get_PeakTrainingEffectFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PeakTrainingEffectFivedigitsGeneric_8be2vx$', {
    get: get_PeakTrainingEffectFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'PeakTrainingEffectSixdigitsGeneric_8be2vx$', {
    get: get_PeakTrainingEffectSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'SwimDistanceFourdigitsMetric_8be2vx$', {
    get: get_SwimDistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimDistanceFourdigitsImperial_8be2vx$', {
    get: get_SwimDistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'SwimDistanceFivedigitsMetric_8be2vx$', {
    get: get_SwimDistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimDistanceFivedigitsImperial_8be2vx$', {
    get: get_SwimDistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'SwimDistanceSixdigitsMetric_8be2vx$', {
    get: get_SwimDistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'SwimDistanceSixdigitsImperial_8be2vx$', {
    get: get_SwimDistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'CountFourdigitsGeneric_8be2vx$', {
    get: get_CountFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CountTwodigitsGeneric_8be2vx$', {
    get: get_CountTwodigitsGeneric
  });
  Object.defineProperty(package$internal, 'CountThreedigitsGeneric_8be2vx$', {
    get: get_CountThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CountFivedigitsGeneric_8be2vx$', {
    get: get_CountFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CountSixdigitsGeneric_8be2vx$', {
    get: get_CountSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDurationTrainingGeneric_8be2vx$', {
    get: get_DownhillDurationTrainingGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDurationFourdigitsGeneric_8be2vx$', {
    get: get_DownhillDurationFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDurationApproximateGeneric_8be2vx$', {
    get: get_DownhillDurationApproximateGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDurationFivedigitsGeneric_8be2vx$', {
    get: get_DownhillDurationFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDurationSixdigitsGeneric_8be2vx$', {
    get: get_DownhillDurationSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'FlightTimeFourdigitsGeneric_8be2vx$', {
    get: get_FlightTimeFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'RecoveryTimeFourdigitsGeneric_8be2vx$', {
    get: get_RecoveryTimeFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'RecoveryTimeFivedigitsGeneric_8be2vx$', {
    get: get_RecoveryTimeFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'RecoveryTimeSixdigitsGeneric_8be2vx$', {
    get: get_RecoveryTimeSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'AscentFourdigitsMetric_8be2vx$', {
    get: get_AscentFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'AscentFourdigitsImperial_8be2vx$', {
    get: get_AscentFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'AscentFivedigitsMetric_8be2vx$', {
    get: get_AscentFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'AscentFivedigitsImperial_8be2vx$', {
    get: get_AscentFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'AscentSixdigitsMetric_8be2vx$', {
    get: get_AscentSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'AscentSixdigitsImperial_8be2vx$', {
    get: get_AscentSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'PaceFourdigitsMetric_8be2vx$', {
    get: get_PaceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'PaceFourdigitsImperial_8be2vx$', {
    get: get_PaceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'PaceFixedNoLeadingZeroMetric_8be2vx$', {
    get: get_PaceFixedNoLeadingZeroMetric
  });
  Object.defineProperty(package$internal, 'PaceFixedNoLeadingZeroImperial_8be2vx$', {
    get: get_PaceFixedNoLeadingZeroImperial
  });
  Object.defineProperty(package$internal, 'PaceFivedigitsMetric_8be2vx$', {
    get: get_PaceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'PaceFivedigitsImperial_8be2vx$', {
    get: get_PaceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'PaceSixdigitsMetric_8be2vx$', {
    get: get_PaceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'PaceSixdigitsImperial_8be2vx$', {
    get: get_PaceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'VO2FourdigitsGeneric_8be2vx$', {
    get: get_VO2FourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'VO2FivedigitsGeneric_8be2vx$', {
    get: get_VO2FivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'VO2SixdigitsGeneric_8be2vx$', {
    get: get_VO2SixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceFourdigitsMetric_8be2vx$', {
    get: get_NavigationRouteDistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceFourdigitsImperial_8be2vx$', {
    get: get_NavigationRouteDistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceThreedigitsMetric_8be2vx$', {
    get: get_NavigationRouteDistanceThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceThreedigitsImperial_8be2vx$', {
    get: get_NavigationRouteDistanceThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceFivedigitsMetric_8be2vx$', {
    get: get_NavigationRouteDistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceFivedigitsImperial_8be2vx$', {
    get: get_NavigationRouteDistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceSixdigitsMetric_8be2vx$', {
    get: get_NavigationRouteDistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceSixdigitsImperial_8be2vx$', {
    get: get_NavigationRouteDistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceAccurateMetric_8be2vx$', {
    get: get_NavigationRouteDistanceAccurateMetric
  });
  Object.defineProperty(package$internal, 'NavigationRouteDistanceAccurateImperial_8be2vx$', {
    get: get_NavigationRouteDistanceAccurateImperial
  });
  Object.defineProperty(package$internal, 'StiffnessTwodigitsGeneric_8be2vx$', {
    get: get_StiffnessTwodigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeFourdigitsMetric_8be2vx$', {
    get: get_DownhillAltitudeFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeFourdigitsImperial_8be2vx$', {
    get: get_DownhillAltitudeFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeFivedigitsMetric_8be2vx$', {
    get: get_DownhillAltitudeFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeFivedigitsImperial_8be2vx$', {
    get: get_DownhillAltitudeFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeSixdigitsMetric_8be2vx$', {
    get: get_DownhillAltitudeSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillAltitudeSixdigitsImperial_8be2vx$', {
    get: get_DownhillAltitudeSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'CompassHeadingDegFourdigitsGeneric_8be2vx$', {
    get: get_CompassHeadingDegFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingDegFivedigitsGeneric_8be2vx$', {
    get: get_CompassHeadingDegFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingDegSixdigitsGeneric_8be2vx$', {
    get: get_CompassHeadingDegSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingDegAccurateGeneric_8be2vx$', {
    get: get_CompassHeadingDegAccurateGeneric
  });
  Object.defineProperty(package$internal, 'DistanceFourdigitsMetric_8be2vx$', {
    get: get_DistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DistanceFourdigitsImperial_8be2vx$', {
    get: get_DistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DistanceAccumulatedMetric_8be2vx$', {
    get: get_DistanceAccumulatedMetric
  });
  Object.defineProperty(package$internal, 'DistanceAccumulatedImperial_8be2vx$', {
    get: get_DistanceAccumulatedImperial
  });
  Object.defineProperty(package$internal, 'DistanceThreedigitsMetric_8be2vx$', {
    get: get_DistanceThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'DistanceThreedigitsImperial_8be2vx$', {
    get: get_DistanceThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'DistanceMapscaleMetric_8be2vx$', {
    get: get_DistanceMapscaleMetric
  });
  Object.defineProperty(package$internal, 'DistanceMapscaleImperial_8be2vx$', {
    get: get_DistanceMapscaleImperial
  });
  Object.defineProperty(package$internal, 'DistanceApproximateMetric_8be2vx$', {
    get: get_DistanceApproximateMetric
  });
  Object.defineProperty(package$internal, 'DistanceApproximateImperial_8be2vx$', {
    get: get_DistanceApproximateImperial
  });
  Object.defineProperty(package$internal, 'DistanceNodecimalMetric_8be2vx$', {
    get: get_DistanceNodecimalMetric
  });
  Object.defineProperty(package$internal, 'DistanceNodecimalImperial_8be2vx$', {
    get: get_DistanceNodecimalImperial
  });
  Object.defineProperty(package$internal, 'DistanceFivedigitsMetric_8be2vx$', {
    get: get_DistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DistanceFivedigitsImperial_8be2vx$', {
    get: get_DistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DistanceSixdigitsMetric_8be2vx$', {
    get: get_DistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DistanceSixdigitsImperial_8be2vx$', {
    get: get_DistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'DistanceOnedecimalMetric_8be2vx$', {
    get: get_DistanceOnedecimalMetric
  });
  Object.defineProperty(package$internal, 'DistanceOnedecimalImperial_8be2vx$', {
    get: get_DistanceOnedecimalImperial
  });
  Object.defineProperty(package$internal, 'DistanceAccurateMetric_8be2vx$', {
    get: get_DistanceAccurateMetric
  });
  Object.defineProperty(package$internal, 'DistanceAccurateImperial_8be2vx$', {
    get: get_DistanceAccurateImperial
  });
  Object.defineProperty(package$internal, 'TrackAndFieldDistanceFivedigitsGeneric_8be2vx$', {
    get: get_TrackAndFieldDistanceFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DescentFourdigitsMetric_8be2vx$', {
    get: get_DescentFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DescentFourdigitsImperial_8be2vx$', {
    get: get_DescentFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DescentFivedigitsMetric_8be2vx$', {
    get: get_DescentFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DescentFivedigitsImperial_8be2vx$', {
    get: get_DescentFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DescentSixdigitsMetric_8be2vx$', {
    get: get_DescentSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DescentSixdigitsImperial_8be2vx$', {
    get: get_DescentSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'EPOCFourdigitsGeneric_8be2vx$', {
    get: get_EPOCFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'EPOCFivedigitsGeneric_8be2vx$', {
    get: get_EPOCFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'EPOCSixdigitsGeneric_8be2vx$', {
    get: get_EPOCSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceFourdigitsMetric_8be2vx$', {
    get: get_PoolSwimDistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceFourdigitsImperial_8be2vx$', {
    get: get_PoolSwimDistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceFivedigitsMetric_8be2vx$', {
    get: get_PoolSwimDistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceFivedigitsImperial_8be2vx$', {
    get: get_PoolSwimDistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceSixdigitsMetric_8be2vx$', {
    get: get_PoolSwimDistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'PoolSwimDistanceSixdigitsImperial_8be2vx$', {
    get: get_PoolSwimDistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'StepLengthThreedigitsGeneric_8be2vx$', {
    get: get_StepLengthThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEFourdigitsGeneric_8be2vx$', {
    get: get_NavigationPoiETEFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEFourdigitsFixedGeneric_8be2vx$', {
    get: get_NavigationPoiETEFourdigitsFixedGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEHoursGeneric_8be2vx$', {
    get: get_NavigationPoiETEHoursGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEFixedNoLeadingZeroGeneric_8be2vx$', {
    get: get_NavigationPoiETEFixedNoLeadingZeroGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEHumaneGeneric_8be2vx$', {
    get: get_NavigationPoiETEHumaneGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEFivedigitsGeneric_8be2vx$', {
    get: get_NavigationPoiETEFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETESixdigitsGeneric_8be2vx$', {
    get: get_NavigationPoiETESixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEMinutesGeneric_8be2vx$', {
    get: get_NavigationPoiETEMinutesGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETEFixedGeneric_8be2vx$', {
    get: get_NavigationPoiETEFixedGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceFourdigitsMetric_8be2vx$', {
    get: get_NavigationPOIDistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceFourdigitsImperial_8be2vx$', {
    get: get_NavigationPOIDistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceThreedigitsMetric_8be2vx$', {
    get: get_NavigationPOIDistanceThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceThreedigitsImperial_8be2vx$', {
    get: get_NavigationPOIDistanceThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceFivedigitsMetric_8be2vx$', {
    get: get_NavigationPOIDistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceFivedigitsImperial_8be2vx$', {
    get: get_NavigationPOIDistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceSixdigitsMetric_8be2vx$', {
    get: get_NavigationPOIDistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceSixdigitsImperial_8be2vx$', {
    get: get_NavigationPOIDistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceAccurateMetric_8be2vx$', {
    get: get_NavigationPOIDistanceAccurateMetric
  });
  Object.defineProperty(package$internal, 'NavigationPOIDistanceAccurateImperial_8be2vx$', {
    get: get_NavigationPOIDistanceAccurateImperial
  });
  Object.defineProperty(package$internal, 'DurationFourdigitsGeneric_8be2vx$', {
    get: get_DurationFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationAccumulatedGeneric_8be2vx$', {
    get: get_DurationAccumulatedGeneric
  });
  Object.defineProperty(package$internal, 'DurationHoursGeneric_8be2vx$', {
    get: get_DurationHoursGeneric
  });
  Object.defineProperty(package$internal, 'DurationFixedNoLeadingZeroGeneric_8be2vx$', {
    get: get_DurationFixedNoLeadingZeroGeneric
  });
  Object.defineProperty(package$internal, 'DurationApproximateGeneric_8be2vx$', {
    get: get_DurationApproximateGeneric
  });
  Object.defineProperty(package$internal, 'DurationMinutesGeneric_8be2vx$', {
    get: get_DurationMinutesGeneric
  });
  Object.defineProperty(package$internal, 'DurationTrainingGeneric_8be2vx$', {
    get: get_DurationTrainingGeneric
  });
  Object.defineProperty(package$internal, 'DurationApproximateNoLeadingZeroGeneric_8be2vx$', {
    get: get_DurationApproximateNoLeadingZeroGeneric
  });
  Object.defineProperty(package$internal, 'DurationFourdigitsFixedGeneric_8be2vx$', {
    get: get_DurationFourdigitsFixedGeneric
  });
  Object.defineProperty(package$internal, 'DurationHumaneGeneric_8be2vx$', {
    get: get_DurationHumaneGeneric
  });
  Object.defineProperty(package$internal, 'DurationNodecimalGeneric_8be2vx$', {
    get: get_DurationNodecimalGeneric
  });
  Object.defineProperty(package$internal, 'DurationFourdigitsFixedRoundedGeneric_8be2vx$', {
    get: get_DurationFourdigitsFixedRoundedGeneric
  });
  Object.defineProperty(package$internal, 'DurationFivedigitsGeneric_8be2vx$', {
    get: get_DurationFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationSixdigitsGeneric_8be2vx$', {
    get: get_DurationSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationAccurateGeneric_8be2vx$', {
    get: get_DurationAccurateGeneric
  });
  Object.defineProperty(package$internal, 'DurationFixedGeneric_8be2vx$', {
    get: get_DurationFixedGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingMilFourdigitsGeneric_8be2vx$', {
    get: get_CompassHeadingMilFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingMilFivedigitsGeneric_8be2vx$', {
    get: get_CompassHeadingMilFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CompassHeadingMilSixdigitsGeneric_8be2vx$', {
    get: get_CompassHeadingMilSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDescentFourdigitsMetric_8be2vx$', {
    get: get_DownhillDescentFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDescentFourdigitsImperial_8be2vx$', {
    get: get_DownhillDescentFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDescentFivedigitsMetric_8be2vx$', {
    get: get_DownhillDescentFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDescentFivedigitsImperial_8be2vx$', {
    get: get_DownhillDescentFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDescentSixdigitsMetric_8be2vx$', {
    get: get_DownhillDescentSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDescentSixdigitsImperial_8be2vx$', {
    get: get_DownhillDescentSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'ReactivityOnedigitGeneric_8be2vx$', {
    get: get_ReactivityOnedigitGeneric
  });
  Object.defineProperty(package$internal, 'ContactTimeFourdigitsGeneric_8be2vx$', {
    get: get_ContactTimeFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'WeigthFourdigitsMetric_8be2vx$', {
    get: get_WeigthFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'WeigthFourdigitsImperial_8be2vx$', {
    get: get_WeigthFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'WeigthFivedigitsMetric_8be2vx$', {
    get: get_WeigthFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'WeigthFivedigitsImperial_8be2vx$', {
    get: get_WeigthFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'WeigthSixdigitsMetric_8be2vx$', {
    get: get_WeigthSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'WeigthSixdigitsImperial_8be2vx$', {
    get: get_WeigthSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'CadenceSpmFourdigitsGeneric_8be2vx$', {
    get: get_CadenceSpmFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'CadenceSpmFivedigitsGeneric_8be2vx$', {
    get: get_CadenceSpmFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'CadenceSpmSixdigitsGeneric_8be2vx$', {
    get: get_CadenceSpmSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DeclinationFourdigitsGeneric_8be2vx$', {
    get: get_DeclinationFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DeclinationFivedigitsGeneric_8be2vx$', {
    get: get_DeclinationFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DeclinationSixdigitsGeneric_8be2vx$', {
    get: get_DeclinationSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PerformanceFourdigitsGeneric_8be2vx$', {
    get: get_PerformanceFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PerformanceFivedigitsGeneric_8be2vx$', {
    get: get_PerformanceFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'PerformanceSixdigitsGeneric_8be2vx$', {
    get: get_PerformanceSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DiveGasConsumptionOnedecimalMetric_8be2vx$', {
    get: get_DiveGasConsumptionOnedecimalMetric
  });
  Object.defineProperty(package$internal, 'DiveGasConsumptionOnedecimalImperial_8be2vx$', {
    get: get_DiveGasConsumptionOnedecimalImperial
  });
  Object.defineProperty(package$internal, 'DiveDurationAccurateGeneric_8be2vx$', {
    get: get_DiveDurationAccurateGeneric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceFourdigitsMetric_8be2vx$', {
    get: get_DownhillDistanceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceFourdigitsImperial_8be2vx$', {
    get: get_DownhillDistanceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDistanceThreedigitsMetric_8be2vx$', {
    get: get_DownhillDistanceThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceThreedigitsImperial_8be2vx$', {
    get: get_DownhillDistanceThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDistanceApproximateMetric_8be2vx$', {
    get: get_DownhillDistanceApproximateMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceApproximateImperial_8be2vx$', {
    get: get_DownhillDistanceApproximateImperial
  });
  Object.defineProperty(package$internal, 'DownhillDistanceFivedigitsMetric_8be2vx$', {
    get: get_DownhillDistanceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceFivedigitsImperial_8be2vx$', {
    get: get_DownhillDistanceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDistanceSixdigitsMetric_8be2vx$', {
    get: get_DownhillDistanceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceSixdigitsImperial_8be2vx$', {
    get: get_DownhillDistanceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'DownhillDistanceAccurateMetric_8be2vx$', {
    get: get_DownhillDistanceAccurateMetric
  });
  Object.defineProperty(package$internal, 'DownhillDistanceAccurateImperial_8be2vx$', {
    get: get_DownhillDistanceAccurateImperial
  });
  Object.defineProperty(package$internal, 'HeartRateFourdigitsGeneric_8be2vx$', {
    get: get_HeartRateFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRateFivedigitsGeneric_8be2vx$', {
    get: get_HeartRateFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRateSixdigitsGeneric_8be2vx$', {
    get: get_HeartRateSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'TemperatureFourdigitsMetric_8be2vx$', {
    get: get_TemperatureFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'TemperatureFourdigitsImperial_8be2vx$', {
    get: get_TemperatureFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'TemperatureFivedigitsMetric_8be2vx$', {
    get: get_TemperatureFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'TemperatureFivedigitsImperial_8be2vx$', {
    get: get_TemperatureFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'TemperatureSixdigitsMetric_8be2vx$', {
    get: get_TemperatureSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'TemperatureSixdigitsImperial_8be2vx$', {
    get: get_TemperatureSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'UndulationThreedigitsGeneric_8be2vx$', {
    get: get_UndulationThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationMsFourdigitsGeneric_8be2vx$', {
    get: get_DurationMsFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationMsApproximateGeneric_8be2vx$', {
    get: get_DurationMsApproximateGeneric
  });
  Object.defineProperty(package$internal, 'DurationMsFivedigitsGeneric_8be2vx$', {
    get: get_DurationMsFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationMsSixdigitsGeneric_8be2vx$', {
    get: get_DurationMsSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DurationMsAccurateGeneric_8be2vx$', {
    get: get_DurationMsAccurateGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEFourdigitsGeneric_8be2vx$', {
    get: get_NavigationRouteETEFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEFourdigitsFixedGeneric_8be2vx$', {
    get: get_NavigationRouteETEFourdigitsFixedGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEHoursGeneric_8be2vx$', {
    get: get_NavigationRouteETEHoursGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEFixedNoLeadingZeroGeneric_8be2vx$', {
    get: get_NavigationRouteETEFixedNoLeadingZeroGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEHumaneGeneric_8be2vx$', {
    get: get_NavigationRouteETEHumaneGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEFivedigitsGeneric_8be2vx$', {
    get: get_NavigationRouteETEFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETESixdigitsGeneric_8be2vx$', {
    get: get_NavigationRouteETESixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEMinutesGeneric_8be2vx$', {
    get: get_NavigationRouteETEMinutesGeneric
  });
  Object.defineProperty(package$internal, 'NavigationRouteETEFixedGeneric_8be2vx$', {
    get: get_NavigationRouteETEFixedGeneric
  });
  Object.defineProperty(package$internal, 'RowingPaceFourdigitsMetric_8be2vx$', {
    get: get_RowingPaceFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'RowingPaceFourdigitsImperial_8be2vx$', {
    get: get_RowingPaceFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'RowingPaceFixedNoLeadingZeroMetric_8be2vx$', {
    get: get_RowingPaceFixedNoLeadingZeroMetric
  });
  Object.defineProperty(package$internal, 'RowingPaceFixedNoLeadingZeroImperial_8be2vx$', {
    get: get_RowingPaceFixedNoLeadingZeroImperial
  });
  Object.defineProperty(package$internal, 'RowingPaceFivedigitsMetric_8be2vx$', {
    get: get_RowingPaceFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'RowingPaceFivedigitsImperial_8be2vx$', {
    get: get_RowingPaceFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'RowingPaceSixdigitsMetric_8be2vx$', {
    get: get_RowingPaceSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'RowingPaceSixdigitsImperial_8be2vx$', {
    get: get_RowingPaceSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'DiveDistanceAccurateMetric_8be2vx$', {
    get: get_DiveDistanceAccurateMetric
  });
  Object.defineProperty(package$internal, 'DiveDistanceAccurateImperial_8be2vx$', {
    get: get_DiveDistanceAccurateImperial
  });
  Object.defineProperty(package$internal, 'NauticalDistanceFourdigitsGeneric_8be2vx$', {
    get: get_NauticalDistanceFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NauticalDistanceFivedigitsGeneric_8be2vx$', {
    get: get_NauticalDistanceFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NauticalDistanceSixdigitsGeneric_8be2vx$', {
    get: get_NauticalDistanceSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'EnergyFourdigitsGeneric_8be2vx$', {
    get: get_EnergyFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'EnergyAccumulatedGeneric_8be2vx$', {
    get: get_EnergyAccumulatedGeneric
  });
  Object.defineProperty(package$internal, 'EnergyFivedigitsGeneric_8be2vx$', {
    get: get_EnergyFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'EnergySixdigitsGeneric_8be2vx$', {
    get: get_EnergySixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NavigationPoiETAFourdigits24_8be2vx$', {
    get: get_NavigationPoiETAFourdigits24
  });
  Object.defineProperty(package$internal, 'NavigationPoiETAFourdigits12_8be2vx$', {
    get: get_NavigationPoiETAFourdigits12
  });
  Object.defineProperty(package$internal, 'NavigationPoiETAFivedigits24_8be2vx$', {
    get: get_NavigationPoiETAFivedigits24
  });
  Object.defineProperty(package$internal, 'NavigationPoiETAFivedigits12_8be2vx$', {
    get: get_NavigationPoiETAFivedigits12
  });
  Object.defineProperty(package$internal, 'NavigationPoiETASixdigits24_8be2vx$', {
    get: get_NavigationPoiETASixdigits24
  });
  Object.defineProperty(package$internal, 'NavigationPoiETASixdigits12_8be2vx$', {
    get: get_NavigationPoiETASixdigits12
  });
  Object.defineProperty(package$internal, 'StrokeRateFourdigitsGeneric_8be2vx$', {
    get: get_StrokeRateFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokeRateFivedigitsGeneric_8be2vx$', {
    get: get_StrokeRateFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'StrokeRateSixdigitsGeneric_8be2vx$', {
    get: get_StrokeRateSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'SpeedFourdigitsMetric_8be2vx$', {
    get: get_SpeedFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'SpeedFourdigitsImperial_8be2vx$', {
    get: get_SpeedFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'SpeedThreedigitsMetric_8be2vx$', {
    get: get_SpeedThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'SpeedThreedigitsImperial_8be2vx$', {
    get: get_SpeedThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'SpeedApproximateMetric_8be2vx$', {
    get: get_SpeedApproximateMetric
  });
  Object.defineProperty(package$internal, 'SpeedApproximateImperial_8be2vx$', {
    get: get_SpeedApproximateImperial
  });
  Object.defineProperty(package$internal, 'SpeedFivedigitsMetric_8be2vx$', {
    get: get_SpeedFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'SpeedFivedigitsImperial_8be2vx$', {
    get: get_SpeedFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'SpeedSixdigitsMetric_8be2vx$', {
    get: get_SpeedSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'SpeedSixdigitsImperial_8be2vx$', {
    get: get_SpeedSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'HeartRatePercentageFourdigitsGeneric_8be2vx$', {
    get: get_HeartRatePercentageFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRatePercentageThreedigitsGeneric_8be2vx$', {
    get: get_HeartRatePercentageThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRatePercentageFivedigitsGeneric_8be2vx$', {
    get: get_HeartRatePercentageFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'HeartRatePercentageSixdigitsGeneric_8be2vx$', {
    get: get_HeartRatePercentageSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'TrainingEffectFourdigitsGeneric_8be2vx$', {
    get: get_TrainingEffectFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'TrainingEffectFivedigitsGeneric_8be2vx$', {
    get: get_TrainingEffectFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'TrainingEffectSixdigitsGeneric_8be2vx$', {
    get: get_TrainingEffectSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'TimeOfDayFourdigits24_8be2vx$', {
    get: get_TimeOfDayFourdigits24
  });
  Object.defineProperty(package$internal, 'TimeOfDayFourdigits12_8be2vx$', {
    get: get_TimeOfDayFourdigits12
  });
  Object.defineProperty(package$internal, 'TimeOfDayFivedigits24_8be2vx$', {
    get: get_TimeOfDayFivedigits24
  });
  Object.defineProperty(package$internal, 'TimeOfDayFivedigits12_8be2vx$', {
    get: get_TimeOfDayFivedigits12
  });
  Object.defineProperty(package$internal, 'TimeOfDaySixdigits24_8be2vx$', {
    get: get_TimeOfDaySixdigits24
  });
  Object.defineProperty(package$internal, 'TimeOfDaySixdigits12_8be2vx$', {
    get: get_TimeOfDaySixdigits12
  });
  Object.defineProperty(package$internal, 'TimeOfDayAccurate24_8be2vx$', {
    get: get_TimeOfDayAccurate24
  });
  Object.defineProperty(package$internal, 'TimeOfDayAccurate12_8be2vx$', {
    get: get_TimeOfDayAccurate12
  });
  Object.defineProperty(package$internal, 'DownhillLapCountFourdigitsGeneric_8be2vx$', {
    get: get_DownhillLapCountFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillLapCountThreedigitsGeneric_8be2vx$', {
    get: get_DownhillLapCountThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillLapCountFivedigitsGeneric_8be2vx$', {
    get: get_DownhillLapCountFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillLapCountSixdigitsGeneric_8be2vx$', {
    get: get_DownhillLapCountSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PercentageFourdigitsGeneric_8be2vx$', {
    get: get_PercentageFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PercentageThreedigitsGeneric_8be2vx$', {
    get: get_PercentageThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'PercentageFivedigitsGeneric_8be2vx$', {
    get: get_PercentageFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'PercentageSixdigitsGeneric_8be2vx$', {
    get: get_PercentageSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainFourdigitsMetric_8be2vx$', {
    get: get_VerticalSpeedMountainFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainFourdigitsImperial_8be2vx$', {
    get: get_VerticalSpeedMountainFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainThreedigitsMetric_8be2vx$', {
    get: get_VerticalSpeedMountainThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainThreedigitsImperial_8be2vx$', {
    get: get_VerticalSpeedMountainThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainFivedigitsMetric_8be2vx$', {
    get: get_VerticalSpeedMountainFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainFivedigitsImperial_8be2vx$', {
    get: get_VerticalSpeedMountainFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainSixdigitsMetric_8be2vx$', {
    get: get_VerticalSpeedMountainSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedMountainSixdigitsImperial_8be2vx$', {
    get: get_VerticalSpeedMountainSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'NauticalSpeedFourdigitsGeneric_8be2vx$', {
    get: get_NauticalSpeedFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'NauticalSpeedThreedigitsGeneric_8be2vx$', {
    get: get_NauticalSpeedThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NauticalSpeedApproximateGeneric_8be2vx$', {
    get: get_NauticalSpeedApproximateGeneric
  });
  Object.defineProperty(package$internal, 'NauticalSpeedFivedigitsGeneric_8be2vx$', {
    get: get_NauticalSpeedFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'NauticalSpeedSixdigitsGeneric_8be2vx$', {
    get: get_NauticalSpeedSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'SwolfFourdigitsGeneric_8be2vx$', {
    get: get_SwolfFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'SwolfThreedigitsGeneric_8be2vx$', {
    get: get_SwolfThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'SwolfFivedigitsGeneric_8be2vx$', {
    get: get_SwolfFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'SwolfSixdigitsGeneric_8be2vx$', {
    get: get_SwolfSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillGradeFourdigitsGeneric_8be2vx$', {
    get: get_DownhillGradeFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillGradeTwodigitsGeneric_8be2vx$', {
    get: get_DownhillGradeTwodigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillGradeThreedigitsGeneric_8be2vx$', {
    get: get_DownhillGradeThreedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillGradeFivedigitsGeneric_8be2vx$', {
    get: get_DownhillGradeFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'DownhillGradeSixdigitsGeneric_8be2vx$', {
    get: get_DownhillGradeSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'SunsetFourdigits24_8be2vx$', {
    get: get_SunsetFourdigits24
  });
  Object.defineProperty(package$internal, 'SunsetFourdigits12_8be2vx$', {
    get: get_SunsetFourdigits12
  });
  Object.defineProperty(package$internal, 'SunsetFivedigits24_8be2vx$', {
    get: get_SunsetFivedigits24
  });
  Object.defineProperty(package$internal, 'SunsetFivedigits12_8be2vx$', {
    get: get_SunsetFivedigits12
  });
  Object.defineProperty(package$internal, 'SunsetSixdigits24_8be2vx$', {
    get: get_SunsetSixdigits24
  });
  Object.defineProperty(package$internal, 'SunsetSixdigits12_8be2vx$', {
    get: get_SunsetSixdigits12
  });
  Object.defineProperty(package$internal, 'SunsetAccurate24_8be2vx$', {
    get: get_SunsetAccurate24
  });
  Object.defineProperty(package$internal, 'SunsetAccurate12_8be2vx$', {
    get: get_SunsetAccurate12
  });
  Object.defineProperty(package$internal, 'NavigationRouteETAFourdigits24_8be2vx$', {
    get: get_NavigationRouteETAFourdigits24
  });
  Object.defineProperty(package$internal, 'NavigationRouteETAFourdigits12_8be2vx$', {
    get: get_NavigationRouteETAFourdigits12
  });
  Object.defineProperty(package$internal, 'NavigationRouteETAFivedigits24_8be2vx$', {
    get: get_NavigationRouteETAFivedigits24
  });
  Object.defineProperty(package$internal, 'NavigationRouteETAFivedigits12_8be2vx$', {
    get: get_NavigationRouteETAFivedigits12
  });
  Object.defineProperty(package$internal, 'NavigationRouteETASixdigits24_8be2vx$', {
    get: get_NavigationRouteETASixdigits24
  });
  Object.defineProperty(package$internal, 'NavigationRouteETASixdigits12_8be2vx$', {
    get: get_NavigationRouteETASixdigits12
  });
  Object.defineProperty(package$internal, 'SunriseFourdigits24_8be2vx$', {
    get: get_SunriseFourdigits24
  });
  Object.defineProperty(package$internal, 'SunriseFourdigits12_8be2vx$', {
    get: get_SunriseFourdigits12
  });
  Object.defineProperty(package$internal, 'SunriseFivedigits24_8be2vx$', {
    get: get_SunriseFivedigits24
  });
  Object.defineProperty(package$internal, 'SunriseFivedigits12_8be2vx$', {
    get: get_SunriseFivedigits12
  });
  Object.defineProperty(package$internal, 'SunriseSixdigits24_8be2vx$', {
    get: get_SunriseSixdigits24
  });
  Object.defineProperty(package$internal, 'SunriseSixdigits12_8be2vx$', {
    get: get_SunriseSixdigits12
  });
  Object.defineProperty(package$internal, 'SunriseAccurate24_8be2vx$', {
    get: get_SunriseAccurate24
  });
  Object.defineProperty(package$internal, 'SunriseAccurate12_8be2vx$', {
    get: get_SunriseAccurate12
  });
  Object.defineProperty(package$internal, 'VerticalSpeedFourdigitsMetric_8be2vx$', {
    get: get_VerticalSpeedFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedFourdigitsImperial_8be2vx$', {
    get: get_VerticalSpeedFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedThreedigitsMetric_8be2vx$', {
    get: get_VerticalSpeedThreedigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedThreedigitsImperial_8be2vx$', {
    get: get_VerticalSpeedThreedigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedFivedigitsMetric_8be2vx$', {
    get: get_VerticalSpeedFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedFivedigitsImperial_8be2vx$', {
    get: get_VerticalSpeedFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'VerticalSpeedSixdigitsMetric_8be2vx$', {
    get: get_VerticalSpeedSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'VerticalSpeedSixdigitsImperial_8be2vx$', {
    get: get_VerticalSpeedSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'AirPressureFourdigitsMetric_8be2vx$', {
    get: get_AirPressureFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'AirPressureFourdigitsImperial_8be2vx$', {
    get: get_AirPressureFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'AirPressureFivedigitsMetric_8be2vx$', {
    get: get_AirPressureFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'AirPressureFivedigitsImperial_8be2vx$', {
    get: get_AirPressureFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'AirPressureSixdigitsMetric_8be2vx$', {
    get: get_AirPressureSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'AirPressureSixdigitsImperial_8be2vx$', {
    get: get_AirPressureSixdigitsImperial
  });
  Object.defineProperty(package$internal, 'PowerFourdigitsGeneric_8be2vx$', {
    get: get_PowerFourdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PowerFivedigitsGeneric_8be2vx$', {
    get: get_PowerFivedigitsGeneric
  });
  Object.defineProperty(package$internal, 'PowerSixdigitsGeneric_8be2vx$', {
    get: get_PowerSixdigitsGeneric
  });
  Object.defineProperty(package$internal, 'PowerAccurateGeneric_8be2vx$', {
    get: get_PowerAccurateGeneric
  });
  Object.defineProperty(package$internal, 'AltitudeFourdigitsMetric_8be2vx$', {
    get: get_AltitudeFourdigitsMetric
  });
  Object.defineProperty(package$internal, 'AltitudeFourdigitsImperial_8be2vx$', {
    get: get_AltitudeFourdigitsImperial
  });
  Object.defineProperty(package$internal, 'AltitudeFivedigitsMetric_8be2vx$', {
    get: get_AltitudeFivedigitsMetric
  });
  Object.defineProperty(package$internal, 'AltitudeFivedigitsImperial_8be2vx$', {
    get: get_AltitudeFivedigitsImperial
  });
  Object.defineProperty(package$internal, 'AltitudeSixdigitsMetric_8be2vx$', {
    get: get_AltitudeSixdigitsMetric
  });
  Object.defineProperty(package$internal, 'AltitudeSixdigitsImperial_8be2vx$', {
    get: get_AltitudeSixdigitsImperial
  });
  package$internal.ensureTwoDigits_7efafi$ = ensureTwoDigits;
  package$internal.ensureThreeDigits_7efafi$ = ensureThreeDigits;
  package$internal.ensureTwoDigits_8e50z4$ = ensureTwoDigits_0;
  package$internal.ensureThreeDigits_8e50z4$ = ensureThreeDigits_0;
  package$internal.ensureTwoDigits_nzsbcz$ = ensureTwoDigits_1;
  var package$time = package$internal.time || (package$internal.time = {});
  package$time.formatDuration_giv47c$ = formatDuration;
  package$time.TimeFormatToken = TimeFormatToken;
  package$time.Delimiter = Delimiter;
  Object.defineProperty(package$time, '_a', {
    get: _a_getInstance
  });
  Object.defineProperty(package$time, '_d', {
    get: _d_getInstance
  });
  Object.defineProperty(package$time, '_hh', {
    get: _hh_getInstance
  });
  Object.defineProperty(package$time, '_h', {
    get: _h_getInstance
  });
  Object.defineProperty(package$time, 'HH', {
    get: HH_getInstance
  });
  Object.defineProperty(package$time, 'H', {
    get: H_getInstance
  });
  Object.defineProperty(package$time, '_mm', {
    get: _mm_getInstance
  });
  Object.defineProperty(package$time, '_m', {
    get: _m_getInstance
  });
  Object.defineProperty(package$time, '_ss', {
    get: _ss_getInstance
  });
  Object.defineProperty(package$time, '_s', {
    get: _s_getInstance
  });
  Object.defineProperty(package$time, '_f', {
    get: _f_getInstance
  });
  Object.defineProperty(package$time, '_ff', {
    get: _ff_getInstance
  });
  Object.defineProperty(package$time, '_fff', {
    get: _fff_getInstance
  });
  Object.defineProperty(Dimension, 'OTHER', {
    get: Dimension$OTHER_getInstance
  });
  Object.defineProperty(Dimension, 'LENGTH', {
    get: Dimension$LENGTH_getInstance
  });
  Object.defineProperty(Dimension, 'TEMPERATURE', {
    get: Dimension$TEMPERATURE_getInstance
  });
  Object.defineProperty(Dimension, 'VELOCITY', {
    get: Dimension$VELOCITY_getInstance
  });
  Object.defineProperty(Dimension, 'PRESSURE', {
    get: Dimension$PRESSURE_getInstance
  });
  Object.defineProperty(Dimension, 'ENERGY', {
    get: Dimension$ENERGY_getInstance
  });
  Object.defineProperty(Dimension, 'FREQUENCY', {
    get: Dimension$FREQUENCY_getInstance
  });
  Object.defineProperty(Dimension, 'VOLUMETRIC_FLOW', {
    get: Dimension$VOLUMETRIC_FLOW_getInstance
  });
  Object.defineProperty(Dimension, 'ANGLE', {
    get: Dimension$ANGLE_getInstance
  });
  Object.defineProperty(Dimension, 'WEIGHT', {
    get: Dimension$WEIGHT_getInstance
  });
  Object.defineProperty(Dimension, 'TIME', {
    get: Dimension$TIME_getInstance
  });
  Object.defineProperty(Dimension, 'POWER', {
    get: Dimension$POWER_getInstance
  });
  var package$unitconversion = package$internal.unitconversion || (package$internal.unitconversion = {});
  package$unitconversion.Dimension = Dimension;
  package$internal.DecimalFormatter = DecimalFormatter;
  SwimPaceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimPaceFourdigitsMetric$lambda);
  SwimPaceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimPaceFourdigitsImperial$lambda);
  SwimPaceFixedNoLeadingZeroMetric = lazy(LazyThreadSafetyMode.NONE, SwimPaceFixedNoLeadingZeroMetric$lambda);
  SwimPaceFixedNoLeadingZeroImperial = lazy(LazyThreadSafetyMode.NONE, SwimPaceFixedNoLeadingZeroImperial$lambda);
  SwimPaceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimPaceFivedigitsMetric$lambda);
  SwimPaceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimPaceFivedigitsImperial$lambda);
  SwimPaceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimPaceSixdigitsMetric$lambda);
  SwimPaceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimPaceSixdigitsImperial$lambda);
  HeartRateBpmFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRateBpmFourdigitsGeneric$lambda);
  HeartRateBpmFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRateBpmFivedigitsGeneric$lambda);
  DiveGasPressureNodecimalMetric = lazy(LazyThreadSafetyMode.NONE, DiveGasPressureNodecimalMetric$lambda);
  DiveGasPressureNodecimalImperial = lazy(LazyThreadSafetyMode.NONE, DiveGasPressureNodecimalImperial$lambda);
  DownhillSpeedFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedFourdigitsMetric$lambda);
  DownhillSpeedFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedFourdigitsImperial$lambda);
  DownhillSpeedThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedThreedigitsMetric$lambda);
  DownhillSpeedThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedThreedigitsImperial$lambda);
  DownhillSpeedApproximateMetric = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedApproximateMetric$lambda);
  DownhillSpeedApproximateImperial = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedApproximateImperial$lambda);
  DownhillSpeedFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedFivedigitsMetric$lambda);
  DownhillSpeedFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedFivedigitsImperial$lambda);
  DownhillSpeedSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedSixdigitsMetric$lambda);
  DownhillSpeedSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillSpeedSixdigitsImperial$lambda);
  CadenceFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceFourdigitsGeneric$lambda);
  CadenceFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceFivedigitsGeneric$lambda);
  CadenceSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceSixdigitsGeneric$lambda);
  StrokesFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokesFourdigitsGeneric$lambda);
  StrokesThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokesThreedigitsGeneric$lambda);
  StrokesFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokesFivedigitsGeneric$lambda);
  StrokesSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokesSixdigitsGeneric$lambda);
  PeakTrainingEffectFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PeakTrainingEffectFourdigitsGeneric$lambda);
  PeakTrainingEffectFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PeakTrainingEffectFivedigitsGeneric$lambda);
  PeakTrainingEffectSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PeakTrainingEffectSixdigitsGeneric$lambda);
  SwimDistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimDistanceFourdigitsMetric$lambda);
  SwimDistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimDistanceFourdigitsImperial$lambda);
  SwimDistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimDistanceFivedigitsMetric$lambda);
  SwimDistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimDistanceFivedigitsImperial$lambda);
  SwimDistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SwimDistanceSixdigitsMetric$lambda);
  SwimDistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SwimDistanceSixdigitsImperial$lambda);
  CountFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CountFourdigitsGeneric$lambda);
  CountTwodigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CountTwodigitsGeneric$lambda);
  CountThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CountThreedigitsGeneric$lambda);
  CountFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CountFivedigitsGeneric$lambda);
  CountSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CountSixdigitsGeneric$lambda);
  DownhillDurationTrainingGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillDurationTrainingGeneric$lambda);
  DownhillDurationFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillDurationFourdigitsGeneric$lambda);
  DownhillDurationApproximateGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillDurationApproximateGeneric$lambda);
  DownhillDurationFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillDurationFivedigitsGeneric$lambda);
  DownhillDurationSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillDurationSixdigitsGeneric$lambda);
  FlightTimeFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, FlightTimeFourdigitsGeneric$lambda);
  RecoveryTimeFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, RecoveryTimeFourdigitsGeneric$lambda);
  RecoveryTimeFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, RecoveryTimeFivedigitsGeneric$lambda);
  RecoveryTimeSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, RecoveryTimeSixdigitsGeneric$lambda);
  AscentFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AscentFourdigitsMetric$lambda);
  AscentFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AscentFourdigitsImperial$lambda);
  AscentFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, AscentFivedigitsMetric$lambda);
  AscentFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, AscentFivedigitsImperial$lambda);
  AscentSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AscentSixdigitsMetric$lambda);
  AscentSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AscentSixdigitsImperial$lambda);
  PaceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, PaceFourdigitsMetric$lambda);
  PaceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, PaceFourdigitsImperial$lambda);
  PaceFixedNoLeadingZeroMetric = lazy(LazyThreadSafetyMode.NONE, PaceFixedNoLeadingZeroMetric$lambda);
  PaceFixedNoLeadingZeroImperial = lazy(LazyThreadSafetyMode.NONE, PaceFixedNoLeadingZeroImperial$lambda);
  PaceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, PaceFivedigitsMetric$lambda);
  PaceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, PaceFivedigitsImperial$lambda);
  PaceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, PaceSixdigitsMetric$lambda);
  PaceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, PaceSixdigitsImperial$lambda);
  VO2FourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, VO2FourdigitsGeneric$lambda);
  VO2FivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, VO2FivedigitsGeneric$lambda);
  VO2SixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, VO2SixdigitsGeneric$lambda);
  NavigationRouteDistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceFourdigitsMetric$lambda);
  NavigationRouteDistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceFourdigitsImperial$lambda);
  NavigationRouteDistanceThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceThreedigitsMetric$lambda);
  NavigationRouteDistanceThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceThreedigitsImperial$lambda);
  NavigationRouteDistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceFivedigitsMetric$lambda);
  NavigationRouteDistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceFivedigitsImperial$lambda);
  NavigationRouteDistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceSixdigitsMetric$lambda);
  NavigationRouteDistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceSixdigitsImperial$lambda);
  NavigationRouteDistanceAccurateMetric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceAccurateMetric$lambda);
  NavigationRouteDistanceAccurateImperial = lazy(LazyThreadSafetyMode.NONE, NavigationRouteDistanceAccurateImperial$lambda);
  StiffnessTwodigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StiffnessTwodigitsGeneric$lambda);
  DownhillAltitudeFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeFourdigitsMetric$lambda);
  DownhillAltitudeFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeFourdigitsImperial$lambda);
  DownhillAltitudeFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeFivedigitsMetric$lambda);
  DownhillAltitudeFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeFivedigitsImperial$lambda);
  DownhillAltitudeSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeSixdigitsMetric$lambda);
  DownhillAltitudeSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillAltitudeSixdigitsImperial$lambda);
  CompassHeadingDegFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingDegFourdigitsGeneric$lambda);
  CompassHeadingDegFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingDegFivedigitsGeneric$lambda);
  CompassHeadingDegSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingDegSixdigitsGeneric$lambda);
  CompassHeadingDegAccurateGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingDegAccurateGeneric$lambda);
  DistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DistanceFourdigitsMetric$lambda);
  DistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DistanceFourdigitsImperial$lambda);
  DistanceAccumulatedMetric = lazy(LazyThreadSafetyMode.NONE, DistanceAccumulatedMetric$lambda);
  DistanceAccumulatedImperial = lazy(LazyThreadSafetyMode.NONE, DistanceAccumulatedImperial$lambda);
  DistanceThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DistanceThreedigitsMetric$lambda);
  DistanceThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DistanceThreedigitsImperial$lambda);
  DistanceMapscaleMetric = lazy(LazyThreadSafetyMode.NONE, DistanceMapscaleMetric$lambda);
  DistanceMapscaleImperial = lazy(LazyThreadSafetyMode.NONE, DistanceMapscaleImperial$lambda);
  DistanceApproximateMetric = lazy(LazyThreadSafetyMode.NONE, DistanceApproximateMetric$lambda);
  DistanceApproximateImperial = lazy(LazyThreadSafetyMode.NONE, DistanceApproximateImperial$lambda);
  DistanceNodecimalMetric = lazy(LazyThreadSafetyMode.NONE, DistanceNodecimalMetric$lambda);
  DistanceNodecimalImperial = lazy(LazyThreadSafetyMode.NONE, DistanceNodecimalImperial$lambda);
  DistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DistanceFivedigitsMetric$lambda);
  DistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DistanceFivedigitsImperial$lambda);
  DistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DistanceSixdigitsMetric$lambda);
  DistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DistanceSixdigitsImperial$lambda);
  DistanceOnedecimalMetric = lazy(LazyThreadSafetyMode.NONE, DistanceOnedecimalMetric$lambda);
  DistanceOnedecimalImperial = lazy(LazyThreadSafetyMode.NONE, DistanceOnedecimalImperial$lambda);
  DistanceAccurateMetric = lazy(LazyThreadSafetyMode.NONE, DistanceAccurateMetric$lambda);
  DistanceAccurateImperial = lazy(LazyThreadSafetyMode.NONE, DistanceAccurateImperial$lambda);
  TrackAndFieldDistanceFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, TrackAndFieldDistanceFivedigitsGeneric$lambda);
  DescentFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DescentFourdigitsMetric$lambda);
  DescentFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DescentFourdigitsImperial$lambda);
  DescentFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DescentFivedigitsMetric$lambda);
  DescentFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DescentFivedigitsImperial$lambda);
  DescentSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DescentSixdigitsMetric$lambda);
  DescentSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DescentSixdigitsImperial$lambda);
  EPOCFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EPOCFourdigitsGeneric$lambda);
  EPOCFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EPOCFivedigitsGeneric$lambda);
  EPOCSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EPOCSixdigitsGeneric$lambda);
  PoolSwimDistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceFourdigitsMetric$lambda);
  PoolSwimDistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceFourdigitsImperial$lambda);
  PoolSwimDistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceFivedigitsMetric$lambda);
  PoolSwimDistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceFivedigitsImperial$lambda);
  PoolSwimDistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceSixdigitsMetric$lambda);
  PoolSwimDistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, PoolSwimDistanceSixdigitsImperial$lambda);
  StepLengthThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StepLengthThreedigitsGeneric$lambda);
  NavigationPoiETEFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEFourdigitsGeneric$lambda);
  NavigationPoiETEFourdigitsFixedGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEFourdigitsFixedGeneric$lambda);
  NavigationPoiETEHoursGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEHoursGeneric$lambda);
  NavigationPoiETEFixedNoLeadingZeroGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEFixedNoLeadingZeroGeneric$lambda);
  NavigationPoiETEHumaneGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEHumaneGeneric$lambda);
  NavigationPoiETEFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEFivedigitsGeneric$lambda);
  NavigationPoiETESixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETESixdigitsGeneric$lambda);
  NavigationPoiETEMinutesGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEMinutesGeneric$lambda);
  NavigationPoiETEFixedGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETEFixedGeneric$lambda);
  NavigationPOIDistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceFourdigitsMetric$lambda);
  NavigationPOIDistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceFourdigitsImperial$lambda);
  NavigationPOIDistanceThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceThreedigitsMetric$lambda);
  NavigationPOIDistanceThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceThreedigitsImperial$lambda);
  NavigationPOIDistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceFivedigitsMetric$lambda);
  NavigationPOIDistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceFivedigitsImperial$lambda);
  NavigationPOIDistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceSixdigitsMetric$lambda);
  NavigationPOIDistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceSixdigitsImperial$lambda);
  NavigationPOIDistanceAccurateMetric = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceAccurateMetric$lambda);
  NavigationPOIDistanceAccurateImperial = lazy(LazyThreadSafetyMode.NONE, NavigationPOIDistanceAccurateImperial$lambda);
  DurationFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFourdigitsGeneric$lambda);
  DurationAccumulatedGeneric = lazy(LazyThreadSafetyMode.NONE, DurationAccumulatedGeneric$lambda);
  DurationHoursGeneric = lazy(LazyThreadSafetyMode.NONE, DurationHoursGeneric$lambda);
  DurationFixedNoLeadingZeroGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFixedNoLeadingZeroGeneric$lambda);
  DurationApproximateGeneric = lazy(LazyThreadSafetyMode.NONE, DurationApproximateGeneric$lambda);
  DurationMinutesGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMinutesGeneric$lambda);
  DurationTrainingGeneric = lazy(LazyThreadSafetyMode.NONE, DurationTrainingGeneric$lambda);
  DurationApproximateNoLeadingZeroGeneric = lazy(LazyThreadSafetyMode.NONE, DurationApproximateNoLeadingZeroGeneric$lambda);
  DurationFourdigitsFixedGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFourdigitsFixedGeneric$lambda);
  DurationHumaneGeneric = lazy(LazyThreadSafetyMode.NONE, DurationHumaneGeneric$lambda);
  DurationNodecimalGeneric = lazy(LazyThreadSafetyMode.NONE, DurationNodecimalGeneric$lambda);
  DurationFourdigitsFixedRoundedGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFourdigitsFixedRoundedGeneric$lambda);
  DurationFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFivedigitsGeneric$lambda);
  DurationSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationSixdigitsGeneric$lambda);
  DurationAccurateGeneric = lazy(LazyThreadSafetyMode.NONE, DurationAccurateGeneric$lambda);
  DurationFixedGeneric = lazy(LazyThreadSafetyMode.NONE, DurationFixedGeneric$lambda);
  CompassHeadingMilFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingMilFourdigitsGeneric$lambda);
  CompassHeadingMilFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingMilFivedigitsGeneric$lambda);
  CompassHeadingMilSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CompassHeadingMilSixdigitsGeneric$lambda);
  DownhillDescentFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDescentFourdigitsMetric$lambda);
  DownhillDescentFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDescentFourdigitsImperial$lambda);
  DownhillDescentFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDescentFivedigitsMetric$lambda);
  DownhillDescentFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDescentFivedigitsImperial$lambda);
  DownhillDescentSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDescentSixdigitsMetric$lambda);
  DownhillDescentSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDescentSixdigitsImperial$lambda);
  ReactivityOnedigitGeneric = lazy(LazyThreadSafetyMode.NONE, ReactivityOnedigitGeneric$lambda);
  ContactTimeFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, ContactTimeFourdigitsGeneric$lambda);
  WeigthFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, WeigthFourdigitsMetric$lambda);
  WeigthFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, WeigthFourdigitsImperial$lambda);
  WeigthFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, WeigthFivedigitsMetric$lambda);
  WeigthFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, WeigthFivedigitsImperial$lambda);
  WeigthSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, WeigthSixdigitsMetric$lambda);
  WeigthSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, WeigthSixdigitsImperial$lambda);
  CadenceSpmFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceSpmFourdigitsGeneric$lambda);
  CadenceSpmFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceSpmFivedigitsGeneric$lambda);
  CadenceSpmSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, CadenceSpmSixdigitsGeneric$lambda);
  DeclinationFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DeclinationFourdigitsGeneric$lambda);
  DeclinationFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DeclinationFivedigitsGeneric$lambda);
  DeclinationSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DeclinationSixdigitsGeneric$lambda);
  PerformanceFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PerformanceFourdigitsGeneric$lambda);
  PerformanceFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PerformanceFivedigitsGeneric$lambda);
  PerformanceSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PerformanceSixdigitsGeneric$lambda);
  DiveGasConsumptionOnedecimalMetric = lazy(LazyThreadSafetyMode.NONE, DiveGasConsumptionOnedecimalMetric$lambda);
  DiveGasConsumptionOnedecimalImperial = lazy(LazyThreadSafetyMode.NONE, DiveGasConsumptionOnedecimalImperial$lambda);
  DiveDurationAccurateGeneric = lazy(LazyThreadSafetyMode.NONE, DiveDurationAccurateGeneric$lambda);
  DownhillDistanceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceFourdigitsMetric$lambda);
  DownhillDistanceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceFourdigitsImperial$lambda);
  DownhillDistanceThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceThreedigitsMetric$lambda);
  DownhillDistanceThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceThreedigitsImperial$lambda);
  DownhillDistanceApproximateMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceApproximateMetric$lambda);
  DownhillDistanceApproximateImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceApproximateImperial$lambda);
  DownhillDistanceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceFivedigitsMetric$lambda);
  DownhillDistanceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceFivedigitsImperial$lambda);
  DownhillDistanceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceSixdigitsMetric$lambda);
  DownhillDistanceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceSixdigitsImperial$lambda);
  DownhillDistanceAccurateMetric = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceAccurateMetric$lambda);
  DownhillDistanceAccurateImperial = lazy(LazyThreadSafetyMode.NONE, DownhillDistanceAccurateImperial$lambda);
  HeartRateFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRateFourdigitsGeneric$lambda);
  HeartRateFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRateFivedigitsGeneric$lambda);
  HeartRateSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRateSixdigitsGeneric$lambda);
  TemperatureFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, TemperatureFourdigitsMetric$lambda);
  TemperatureFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, TemperatureFourdigitsImperial$lambda);
  TemperatureFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, TemperatureFivedigitsMetric$lambda);
  TemperatureFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, TemperatureFivedigitsImperial$lambda);
  TemperatureSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, TemperatureSixdigitsMetric$lambda);
  TemperatureSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, TemperatureSixdigitsImperial$lambda);
  UndulationThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, UndulationThreedigitsGeneric$lambda);
  DurationMsFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMsFourdigitsGeneric$lambda);
  DurationMsApproximateGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMsApproximateGeneric$lambda);
  DurationMsFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMsFivedigitsGeneric$lambda);
  DurationMsSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMsSixdigitsGeneric$lambda);
  DurationMsAccurateGeneric = lazy(LazyThreadSafetyMode.NONE, DurationMsAccurateGeneric$lambda);
  NavigationRouteETEFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEFourdigitsGeneric$lambda);
  NavigationRouteETEFourdigitsFixedGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEFourdigitsFixedGeneric$lambda);
  NavigationRouteETEHoursGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEHoursGeneric$lambda);
  NavigationRouteETEFixedNoLeadingZeroGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEFixedNoLeadingZeroGeneric$lambda);
  NavigationRouteETEHumaneGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEHumaneGeneric$lambda);
  NavigationRouteETEFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEFivedigitsGeneric$lambda);
  NavigationRouteETESixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETESixdigitsGeneric$lambda);
  NavigationRouteETEMinutesGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEMinutesGeneric$lambda);
  NavigationRouteETEFixedGeneric = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETEFixedGeneric$lambda);
  RowingPaceFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, RowingPaceFourdigitsMetric$lambda);
  RowingPaceFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, RowingPaceFourdigitsImperial$lambda);
  RowingPaceFixedNoLeadingZeroMetric = lazy(LazyThreadSafetyMode.NONE, RowingPaceFixedNoLeadingZeroMetric$lambda);
  RowingPaceFixedNoLeadingZeroImperial = lazy(LazyThreadSafetyMode.NONE, RowingPaceFixedNoLeadingZeroImperial$lambda);
  RowingPaceFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, RowingPaceFivedigitsMetric$lambda);
  RowingPaceFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, RowingPaceFivedigitsImperial$lambda);
  RowingPaceSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, RowingPaceSixdigitsMetric$lambda);
  RowingPaceSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, RowingPaceSixdigitsImperial$lambda);
  DiveDistanceAccurateMetric = lazy(LazyThreadSafetyMode.NONE, DiveDistanceAccurateMetric$lambda);
  DiveDistanceAccurateImperial = lazy(LazyThreadSafetyMode.NONE, DiveDistanceAccurateImperial$lambda);
  NauticalDistanceFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalDistanceFourdigitsGeneric$lambda);
  NauticalDistanceFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalDistanceFivedigitsGeneric$lambda);
  NauticalDistanceSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalDistanceSixdigitsGeneric$lambda);
  EnergyFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EnergyFourdigitsGeneric$lambda);
  EnergyAccumulatedGeneric = lazy(LazyThreadSafetyMode.NONE, EnergyAccumulatedGeneric$lambda);
  EnergyFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EnergyFivedigitsGeneric$lambda);
  EnergySixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, EnergySixdigitsGeneric$lambda);
  NavigationPoiETAFourdigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETAFourdigits24$lambda);
  NavigationPoiETAFourdigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETAFourdigits12$lambda);
  NavigationPoiETAFivedigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETAFivedigits24$lambda);
  NavigationPoiETAFivedigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETAFivedigits12$lambda);
  NavigationPoiETASixdigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETASixdigits24$lambda);
  NavigationPoiETASixdigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationPoiETASixdigits12$lambda);
  StrokeRateFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokeRateFourdigitsGeneric$lambda);
  StrokeRateFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokeRateFivedigitsGeneric$lambda);
  StrokeRateSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, StrokeRateSixdigitsGeneric$lambda);
  SpeedFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SpeedFourdigitsMetric$lambda);
  SpeedFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SpeedFourdigitsImperial$lambda);
  SpeedThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, SpeedThreedigitsMetric$lambda);
  SpeedThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, SpeedThreedigitsImperial$lambda);
  SpeedApproximateMetric = lazy(LazyThreadSafetyMode.NONE, SpeedApproximateMetric$lambda);
  SpeedApproximateImperial = lazy(LazyThreadSafetyMode.NONE, SpeedApproximateImperial$lambda);
  SpeedFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, SpeedFivedigitsMetric$lambda);
  SpeedFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, SpeedFivedigitsImperial$lambda);
  SpeedSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, SpeedSixdigitsMetric$lambda);
  SpeedSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, SpeedSixdigitsImperial$lambda);
  HeartRatePercentageFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRatePercentageFourdigitsGeneric$lambda);
  HeartRatePercentageThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRatePercentageThreedigitsGeneric$lambda);
  HeartRatePercentageFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRatePercentageFivedigitsGeneric$lambda);
  HeartRatePercentageSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, HeartRatePercentageSixdigitsGeneric$lambda);
  TrainingEffectFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, TrainingEffectFourdigitsGeneric$lambda);
  TrainingEffectFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, TrainingEffectFivedigitsGeneric$lambda);
  TrainingEffectSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, TrainingEffectSixdigitsGeneric$lambda);
  TimeOfDayFourdigits24 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayFourdigits24$lambda);
  TimeOfDayFourdigits12 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayFourdigits12$lambda);
  TimeOfDayFivedigits24 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayFivedigits24$lambda);
  TimeOfDayFivedigits12 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayFivedigits12$lambda);
  TimeOfDaySixdigits24 = lazy(LazyThreadSafetyMode.NONE, TimeOfDaySixdigits24$lambda);
  TimeOfDaySixdigits12 = lazy(LazyThreadSafetyMode.NONE, TimeOfDaySixdigits12$lambda);
  TimeOfDayAccurate24 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayAccurate24$lambda);
  TimeOfDayAccurate12 = lazy(LazyThreadSafetyMode.NONE, TimeOfDayAccurate12$lambda);
  DownhillLapCountFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillLapCountFourdigitsGeneric$lambda);
  DownhillLapCountThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillLapCountThreedigitsGeneric$lambda);
  DownhillLapCountFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillLapCountFivedigitsGeneric$lambda);
  DownhillLapCountSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillLapCountSixdigitsGeneric$lambda);
  PercentageFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PercentageFourdigitsGeneric$lambda);
  PercentageThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PercentageThreedigitsGeneric$lambda);
  PercentageFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PercentageFivedigitsGeneric$lambda);
  PercentageSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PercentageSixdigitsGeneric$lambda);
  VerticalSpeedMountainFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainFourdigitsMetric$lambda);
  VerticalSpeedMountainFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainFourdigitsImperial$lambda);
  VerticalSpeedMountainThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainThreedigitsMetric$lambda);
  VerticalSpeedMountainThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainThreedigitsImperial$lambda);
  VerticalSpeedMountainFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainFivedigitsMetric$lambda);
  VerticalSpeedMountainFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainFivedigitsImperial$lambda);
  VerticalSpeedMountainSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainSixdigitsMetric$lambda);
  VerticalSpeedMountainSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedMountainSixdigitsImperial$lambda);
  NauticalSpeedFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalSpeedFourdigitsGeneric$lambda);
  NauticalSpeedThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalSpeedThreedigitsGeneric$lambda);
  NauticalSpeedApproximateGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalSpeedApproximateGeneric$lambda);
  NauticalSpeedFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalSpeedFivedigitsGeneric$lambda);
  NauticalSpeedSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, NauticalSpeedSixdigitsGeneric$lambda);
  SwolfFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, SwolfFourdigitsGeneric$lambda);
  SwolfThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, SwolfThreedigitsGeneric$lambda);
  SwolfFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, SwolfFivedigitsGeneric$lambda);
  SwolfSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, SwolfSixdigitsGeneric$lambda);
  DownhillGradeFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillGradeFourdigitsGeneric$lambda);
  DownhillGradeTwodigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillGradeTwodigitsGeneric$lambda);
  DownhillGradeThreedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillGradeThreedigitsGeneric$lambda);
  DownhillGradeFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillGradeFivedigitsGeneric$lambda);
  DownhillGradeSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, DownhillGradeSixdigitsGeneric$lambda);
  SunsetFourdigits24 = lazy(LazyThreadSafetyMode.NONE, SunsetFourdigits24$lambda);
  SunsetFourdigits12 = lazy(LazyThreadSafetyMode.NONE, SunsetFourdigits12$lambda);
  SunsetFivedigits24 = lazy(LazyThreadSafetyMode.NONE, SunsetFivedigits24$lambda);
  SunsetFivedigits12 = lazy(LazyThreadSafetyMode.NONE, SunsetFivedigits12$lambda);
  SunsetSixdigits24 = lazy(LazyThreadSafetyMode.NONE, SunsetSixdigits24$lambda);
  SunsetSixdigits12 = lazy(LazyThreadSafetyMode.NONE, SunsetSixdigits12$lambda);
  SunsetAccurate24 = lazy(LazyThreadSafetyMode.NONE, SunsetAccurate24$lambda);
  SunsetAccurate12 = lazy(LazyThreadSafetyMode.NONE, SunsetAccurate12$lambda);
  NavigationRouteETAFourdigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETAFourdigits24$lambda);
  NavigationRouteETAFourdigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETAFourdigits12$lambda);
  NavigationRouteETAFivedigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETAFivedigits24$lambda);
  NavigationRouteETAFivedigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETAFivedigits12$lambda);
  NavigationRouteETASixdigits24 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETASixdigits24$lambda);
  NavigationRouteETASixdigits12 = lazy(LazyThreadSafetyMode.NONE, NavigationRouteETASixdigits12$lambda);
  SunriseFourdigits24 = lazy(LazyThreadSafetyMode.NONE, SunriseFourdigits24$lambda);
  SunriseFourdigits12 = lazy(LazyThreadSafetyMode.NONE, SunriseFourdigits12$lambda);
  SunriseFivedigits24 = lazy(LazyThreadSafetyMode.NONE, SunriseFivedigits24$lambda);
  SunriseFivedigits12 = lazy(LazyThreadSafetyMode.NONE, SunriseFivedigits12$lambda);
  SunriseSixdigits24 = lazy(LazyThreadSafetyMode.NONE, SunriseSixdigits24$lambda);
  SunriseSixdigits12 = lazy(LazyThreadSafetyMode.NONE, SunriseSixdigits12$lambda);
  SunriseAccurate24 = lazy(LazyThreadSafetyMode.NONE, SunriseAccurate24$lambda);
  SunriseAccurate12 = lazy(LazyThreadSafetyMode.NONE, SunriseAccurate12$lambda);
  VerticalSpeedFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedFourdigitsMetric$lambda);
  VerticalSpeedFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedFourdigitsImperial$lambda);
  VerticalSpeedThreedigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedThreedigitsMetric$lambda);
  VerticalSpeedThreedigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedThreedigitsImperial$lambda);
  VerticalSpeedFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedFivedigitsMetric$lambda);
  VerticalSpeedFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedFivedigitsImperial$lambda);
  VerticalSpeedSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedSixdigitsMetric$lambda);
  VerticalSpeedSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, VerticalSpeedSixdigitsImperial$lambda);
  AirPressureFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AirPressureFourdigitsMetric$lambda);
  AirPressureFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AirPressureFourdigitsImperial$lambda);
  AirPressureFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, AirPressureFivedigitsMetric$lambda);
  AirPressureFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, AirPressureFivedigitsImperial$lambda);
  AirPressureSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AirPressureSixdigitsMetric$lambda);
  AirPressureSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AirPressureSixdigitsImperial$lambda);
  PowerFourdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PowerFourdigitsGeneric$lambda);
  PowerFivedigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PowerFivedigitsGeneric$lambda);
  PowerSixdigitsGeneric = lazy(LazyThreadSafetyMode.NONE, PowerSixdigitsGeneric$lambda);
  PowerAccurateGeneric = lazy(LazyThreadSafetyMode.NONE, PowerAccurateGeneric$lambda);
  AltitudeFourdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AltitudeFourdigitsMetric$lambda);
  AltitudeFourdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AltitudeFourdigitsImperial$lambda);
  AltitudeFivedigitsMetric = lazy(LazyThreadSafetyMode.NONE, AltitudeFivedigitsMetric$lambda);
  AltitudeFivedigitsImperial = lazy(LazyThreadSafetyMode.NONE, AltitudeFivedigitsImperial$lambda);
  AltitudeSixdigitsMetric = lazy(LazyThreadSafetyMode.NONE, AltitudeSixdigitsMetric$lambda);
  AltitudeSixdigitsImperial = lazy(LazyThreadSafetyMode.NONE, AltitudeSixdigitsImperial$lambda);
  regex = lazy_0(regex$lambda);
  locale = 'en-US';
  Kotlin.defineModule('sim_shared-sim_formatting', _);
  return _;
}));

//# sourceMappingURL=sim_shared-sim_formatting.js.map
