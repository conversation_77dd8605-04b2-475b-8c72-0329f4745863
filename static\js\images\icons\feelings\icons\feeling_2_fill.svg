var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgFeeling2Fill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M6 24C6 33.9415 14.0592 42 24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24ZM16.5 19.65C16.5 20.3807 17.1044 20.9843 17.85 20.9843C18.5956 20.9843 19.2 20.3807 19.2 19.65V18.1483C19.2 17.4036 18.5956 16.8 17.85 16.8C17.1044 16.8 16.5 17.4036 16.5 18.1483V19.65ZM28.8 19.65V18.1483C28.8 17.4036 29.4044 16.8 30.15 16.8C30.8956 16.8 31.5 17.4036 31.5 18.1483V19.65C31.5 20.3807 30.8956 20.9843 30.15 21C29.4044 20.9843 28.8 20.3807 28.8 19.65ZM30.6 27C30.6 27.6627 30.1016 28.2 29.4867 28.2H18.5133C17.8984 28.2 17.4 27.6627 17.4 27C17.4 26.3373 17.8984 25.8 18.5133 25.8H29.4867C30.1016 25.8 30.6 26.3373 30.6 27Z",
    fill: "#FFAA00"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgFeeling2Fill);
export default __webpack_public_path__ + "static/media/feeling_2_fill.c71f0a3a.svg";
export { ForwardRef as ReactComponent };