var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgFeeling4Fill(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M24.0004 42C33.9408 42 42 33.9415 42 24C42 14.0585 33.9408 6 24.0004 6C14.0592 6 6 14.0585 6 24C6 33.9415 14.0592 42 24.0004 42ZM28.8 18.1483V19.65C28.8 20.3807 29.4044 20.9843 30.15 21C30.8956 20.9843 31.5 20.3807 31.5 19.65V18.1483C31.5 17.4036 30.8956 16.8 30.15 16.8C29.4044 16.8 28.8 17.4036 28.8 18.1483ZM17.85 20.9843C17.1044 20.9843 16.5 20.3807 16.5 19.65V18.1483C16.5 17.4036 17.1044 16.8 17.85 16.8C18.5956 16.8 19.2 17.4036 19.2 18.1483V19.65C19.2 20.3807 18.5956 20.9843 17.85 20.9843ZM32.8661 25.5551C33.4093 25.7581 33.6851 26.3631 33.482 26.9063C30.2489 35.5568 17.999 35.5568 14.7667 26.9062C14.5637 26.363 14.8396 25.758 15.3828 25.5551C15.926 25.3521 16.5309 25.6279 16.7339 26.1712C19.2861 33.0019 28.9619 33.0019 31.5149 26.1711C31.718 25.6279 32.3229 25.3521 32.8661 25.5551Z",
    fill: "#ADE539"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgFeeling4Fill);
export default __webpack_public_path__ + "static/media/feeling_4_fill.8b965576.svg";
export { ForwardRef as ReactComponent };