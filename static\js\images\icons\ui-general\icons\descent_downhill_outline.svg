var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgDescentDownhillOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M10.3176 25.2905C10.9648 25.2905 11.4971 25.7824 11.5611 26.4127L11.5676 26.5405L11.567 38.29L14.4249 34.8619C14.8374 34.3669 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.932 3.91945 35.1438 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.29L9.0676 26.5405C9.0676 25.9365 9.49608 25.4325 10.0657 25.3159L10.1898 25.297L10.3176 25.2905ZM13.5322 6.21761L13.6233 6.11612C14.0789 5.6605 14.7987 5.63013 15.2896 6.02499L15.3911 6.11612L38.789 29.514L36.4202 16.4808C36.2967 15.8015 36.7472 15.1508 37.4264 15.0273C38.0632 14.9115 38.6749 15.3002 38.8507 15.9089L38.8799 16.0335L42.2886 34.7814L23.5407 31.3727C22.8615 31.2492 22.411 30.5985 22.5345 29.9193C22.6503 29.2825 23.2294 28.8467 23.861 28.8965L23.9879 28.913L37.021 31.282L13.6233 7.88388C13.1677 7.42827 13.1373 6.70845 13.5322 6.21761L13.6233 6.11612L13.5322 6.21761Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgDescentDownhillOutline);
export default __webpack_public_path__ + "static/media/descent_downhill_outline.ea130534.svg";
export { ForwardRef as ReactComponent };