import React from 'react';
import { emphasize } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import { SpeedDial } from '@mui/material';
import { SpeedDialAction } from '@mui/material';
import { useTranslation } from 'react-i18next';

import Satellite from '../../images/styles/satellite.png';
import Offroad from '../../images/styles/offroad.png';
import Ski from '../../images/styles/ski.png';

export const enum MapStyles {
  satellite = 'satellite',
  offroad = 'offroad',
  ski = 'ski',
}

type MapStyleSelectorProps = {
  classes?: Record<string, string>;
  value: string | null;
  onChange: (value: string) => void;
  styles: string[];
};

const useStyles = makeStyles(
  (theme) => ({
    root: {},
    fab: {
      color: theme.palette.text.secondary,
      backgroundColor: theme.palette.background.default,
      '&:hover': {
        backgroundColor: emphasize(theme.palette.background.default, 0.15),
      },
      transition: `${theme.transitions.create('transform', {
        duration: theme.transitions.duration.shorter,
      })}, opacity 0.8s`,
      opacity: 1,
    },
    icon: {
      width: '3rem',
      height: '3rem',
      color: theme.palette.text.primary,
    },
    iconSmall: {
      width: '2rem',
      height: '2rem',
      color: theme.palette.text.primary,
    },
    tooltip: {
      display: 'none',
    },
    mainIconWrapper: {
      width: '100%',
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    mainIcon: {
      width: '100%',
      height: '100%',
      borderRadius: '50%',
    },
  }),
  { name: 'PathColorDataSelector' },
);

const getIconByStyleName = (name: string | null) => {
  switch (name) {
    case MapStyles.satellite:
      return Satellite;
    case MapStyles.offroad:
      return Offroad;
    case MapStyles.ski:
      return Ski;
  }
};

function MapStyleSelector(props: MapStyleSelectorProps): React.ReactElement | null {
  const classes = useStyles(props);
  const { value, onChange, styles } = props;
  const [open, setOpen] = React.useState(false);
  const [hidden] = React.useState(false);
  const { t } = useTranslation();
  const handleClose = () => setOpen(false);
  const handleOpen = () => setOpen(true);

  const Icon = getIconByStyleName(value);
  if (!styles || !value || !Icon) return null;
  return (
    <SpeedDial
      className={classes.root}
      ariaLabel=""
      hidden={hidden}
      icon={
        <div className={classes.mainIconWrapper}>
          <img className={classes.mainIcon} src={Icon} />
        </div>
      }
      onClose={handleClose}
      onOpen={handleOpen}
      open={open}
      FabProps={{ classes: { root: classes.fab } }}
    >
      {styles
        .filter((styleName) => styleName !== value)
        .map((styleName) => {
          const GraphIcon = getIconByStyleName(styleName);
          if (!GraphIcon) return null;
          return (
            <SpeedDialAction
              key={styleName}
              TooltipClasses={{ tooltip: classes.tooltip }}
              icon={<img className={classes.mainIcon} src={GraphIcon} />}
              FabProps={{ classes: { root: classes.fab } }}
              onClick={() => {
                handleClose();
                onChange(styleName);
              }}
            />
          );
        })}
    </SpeedDial>
  );
}

export default MapStyleSelector;
