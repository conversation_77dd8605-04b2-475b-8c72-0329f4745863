var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgTemperatureOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M23.5001 4C26.684 4 29.3752 6.61885 29.4959 9.77869L29.5001 10V27.111C30.3344 27.7789 31.0248 28.6521 31.6801 29.7872C34.2941 34.3148 32.7429 40.1042 28.2152 42.7183C23.6876 45.3323 17.8982 43.781 15.2842 39.2534C12.9359 35.1861 13.8085 30.1565 17.2798 27.2865L17.5001 27.1099V10C17.5001 6.74208 20.2422 4 23.5001 4ZM23.5001 6.5C21.6854 6.5 20.1086 8.01641 20.0054 9.81318L20.0001 10V28.4206L19.4414 28.7914C16.4478 30.7787 15.5847 34.7739 17.4492 38.0034C19.3729 41.3353 23.6334 42.4769 26.9652 40.5532C30.2971 38.6295 31.4387 34.3691 29.5151 31.0372C28.9495 30.0575 28.3962 29.3847 27.7525 28.9226L27.5746 28.8018L27.0001 28.4328V10C27.0001 8.12279 25.3773 6.5 23.5001 6.5ZM24.7321 11.6689L24.7318 29.6445C26.931 30.1744 28.5001 32.1059 28.5001 34.5C28.5001 37.3211 26.3212 39.5 23.5001 39.5C20.679 39.5 18.5001 37.3211 18.5001 34.5C18.5001 32.1193 20.0518 30.1959 22.2317 29.6534L22.2321 11.6689H24.7321Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgTemperatureOutline);
export default __webpack_public_path__ + "static/media/temperature_outline.3af93ec4.svg";
export { ForwardRef as ReactComponent };