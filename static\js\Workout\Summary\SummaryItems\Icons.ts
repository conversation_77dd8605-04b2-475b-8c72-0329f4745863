import { ReactComponent as Duration } from '../../../images/icons/ui-general/icons/duration_outline.svg';
import { ReactComponent as HeartRate } from '../../../images/icons/software-actions/icons/heart_pulse_outline.svg';
import { ReactComponent as Distance } from '../../../images/icons/ui-general/icons/distance_outline.svg';
import { ReactComponent as Speed } from '../../../images/icons/software-features/gauge_outline.svg';
import { ReactComponent as DownArrow } from '../../../images/icons/ui-general/icons/down_arrow_outline.svg';
import { ReactComponent as Stroke } from '../../../images/icons/ui-general/icons/swim_stroke_rate_outline.svg';
import { ReactComponent as Swolf } from '../../../images/icons/ui-general/icons/swolf_outline.svg';
import { ReactComponent as Power } from '../../../images/icons/ui-general/icons/electricity_outline.svg';
import { ReactComponent as Calories } from '../../../images/icons/software-features/calories_outline.svg';
import { ReactComponent as Cadence } from '../../../images/icons/ui-general/icons/cadence_outline.svg';
import { ReactComponent as AscendingArrow } from '../../../images/icons/ui-general/icons/rising_arrow_outline.svg';
import { ReactComponent as DescendingArrow } from '../../../images/icons/ui-general/icons/down_arrow_outline.svg';
import { ReactComponent as DurationDescent } from '../../../images/icons/ui-general/icons/duration_downhill_outline.svg';
import { ReactComponent as Temperature } from '../../../images/icons/software-features/temperature_outline.svg';
import { ReactComponent as EPOC } from '../../../images/icons/ui-general/icons/epoc_outline.svg';
import { ReactComponent as VO2 } from '../../../images/icons/ui-general/icons/vo2_outline.svg';
import { ReactComponent as PTE } from '../../../images/icons/ui-general/icons/pte_outline.svg';
import { ReactComponent as Altitude } from '../../../images/icons/ui-general/icons/altitude_outline.svg';
import { ReactComponent as Pressure } from '../../../images/icons/software-features/sea_level_pressure_outline.svg';
import { ReactComponent as Performance } from '../../../images/icons/ui-general/icons/performance_outline.svg';
import { ReactComponent as Recovery } from '../../../images/icons/software-features/recovery_outline.svg';
import { ReactComponent as VerticalSpeed } from '../../../images/icons/ui-general/icons/vertical_speed_outline.svg';
import { ReactComponent as Steps } from '../../../images/icons/software-features/steps_outline.svg';
import { ReactComponent as Time } from '../../../images/icons/software-features/time_outline.svg';
import { ReactComponent as Percentage } from '../../../images/icons/ui-general/icons/percentage_outline.svg';
import { ReactComponent as Sunrise } from '../../../images/icons/software-features/sunrise_outline.svg';
import { ReactComponent as Sunset } from '../../../images/icons/software-features/sunset_outline.svg';
import { ReactComponent as DownhillSpeed } from '../../../images/icons/ui-general/icons/speed_downhill_outline.svg';
import { ReactComponent as DownhillDistance } from '../../../images/icons/ui-general/icons/distance_downhill_outline.svg';
import { ReactComponent as DownhillDuration } from '../../../images/icons/ui-general/icons/duration_downhill_outline.svg';
import { ReactComponent as DownhillGrade } from '../../../images/icons/ui-general/icons/grade_downhill_outline.svg';
import { ReactComponent as DownhillAltitude } from '../../../images/icons/ui-general/icons/altitude_downhill_outline.svg';
import { ReactComponent as DownhillDescent } from '../../../images/icons/ui-general/icons/descent_downhill_outline.svg';
import { ReactComponent as DownhillLapCount } from '../../../images/icons/ui-general/icons/lap_count_downhill_outline.svg';

const Icons: Record<string, typeof Duration | null> = {
  ICON_DURATION: Duration,
  ICON_HEART_RATE: HeartRate,
  ICON_HEART_RATE_PERCENTAGE: HeartRate,
  ICON_DISTANCE: Distance,
  ICON_NAUTICAL_DISTANCE: Distance,
  ICON_SWIM_DISTANCE: Distance,
  ICON_SPEED: Speed,
  ICON_NAUTICAL_SPEED: Speed,
  ICON_PACE: Speed,
  ICON_SWIM_PACE: Speed,
  ICON_ROWING_PACE: Speed,
  ICON_MAX_DEPTH: DownArrow,
  ICON_STROKES: Stroke,
  ICON_SWOLF: Swolf,
  ICON_EMG: null,
  ICON_POWER: Power,
  ICON_CALORIES: Calories,
  ICON_CADENCE: Cadence,
  ICON_ASCENT: AscendingArrow,
  ICON_DESCENT: DescendingArrow,
  ICON_ASCENT_TIME: null,
  ICON_DESCENT_TIME: DurationDescent,
  ICON_FLAT_TIME: null,
  ICON_HIGHEST_POINT: null,
  ICON_LOWEST_POINT: null,
  ICON_TEMPERATURE: Temperature,
  ICON_WATER_TEMPERATURE: Temperature,
  ICON_EPOC: EPOC,
  ICON_VO2: VO2,
  ICON_TRAINING_EFFECT: PTE,
  ICON_ALTITUDE: Altitude,
  ICON_AIR_PRESSURE: Pressure,
  ICON_PERFORMANCE: Performance,
  ICON_RECOVERY_TIME: Recovery,
  ICON_VERTICAL_SPEED: VerticalSpeed,
  ICON_STEPS: Steps,
  ICON_TIME_OF_DAY: Time,
  ICON_COUNT: null,
  ICON_PERCENTAGE: Percentage,
  ICON_SUNRISE: Sunrise,
  ICON_SUNSET: Sunset,
  ICON_DOWNHILL_SPEED: DownhillSpeed,
  ICON_DOWNHILL_DISTANCE: DownhillDistance,
  ICON_DOWNHILL_DURATION: DownhillDuration,
  ICON_DOWNHILL_GRADE: DownhillGrade,
  ICON_DOWNHILL_ALTITUDE: DownhillAltitude,
  ICON_DOWNHILL_DESCENT: DownhillDescent,
  ICON_DOWNHILL_LAP_COUNT: DownhillLapCount,
};
export default Icons;
