var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgSwimStrokeRateOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M36.5 32.75C37.3685 32.75 38.0927 33.0477 38.8896 33.621L39.218 33.8689L39.4516 34.0569L39.9839 34.4991L40.2378 34.7052C41.1892 35.4589 41.8453 35.7534 42.75 35.7534C43.4404 35.7534 44 36.313 44 37.0034C44 37.6937 43.4404 38.2534 42.75 38.2534C41.1731 38.2534 40.0522 37.7452 38.6973 36.6742L38.4404 36.4668L37.6748 35.8359L37.43 35.6506C37.0285 35.3618 36.7554 35.25 36.5 35.25C36.2446 35.25 35.9715 35.3618 35.57 35.6506L35.4133 35.7673L35.2312 35.9108L34.3027 36.6742C32.9478 37.7452 31.8269 38.2534 30.25 38.2534C28.6731 38.2534 27.5522 37.7452 26.1973 36.6742L25.9404 36.4668L25.1748 35.8359L24.93 35.6506C24.5285 35.3618 24.2554 35.25 24 35.25C23.7446 35.25 23.4715 35.3618 23.07 35.6506L22.9133 35.7673L22.7312 35.9108L21.8027 36.6742C20.4478 37.7452 19.3269 38.2534 17.75 38.2534C16.1731 38.2534 15.0522 37.7452 13.6973 36.6742L13.4404 36.4668L12.6748 35.8359L12.43 35.6506C12.0285 35.3618 11.7554 35.25 11.5 35.25C11.2446 35.25 10.9715 35.3618 10.57 35.6506L10.4133 35.7673L10.2312 35.9108L9.30266 36.6742C7.94777 37.7452 6.82687 38.2534 5.25 38.2534C4.55964 38.2534 4 37.6937 4 37.0034C4 36.313 4.55964 35.7534 5.25 35.7534C6.15467 35.7534 6.81082 35.4589 7.7622 34.7052L7.97079 34.5366L8.54841 34.0569L8.78205 33.8689L9.1104 33.621C9.90732 33.0477 10.6315 32.75 11.5 32.75C12.3685 32.75 13.0927 33.0477 13.8896 33.621L14.218 33.8689L14.4516 34.0569L14.9839 34.4991L15.2378 34.7052C16.1892 35.4589 16.8453 35.7534 17.75 35.7534C18.6547 35.7534 19.3108 35.4589 20.2622 34.7052L20.4708 34.5366L21.0484 34.0569L21.282 33.8689L21.6104 33.621C22.4073 33.0477 23.1315 32.75 24 32.75C24.8685 32.75 25.5927 33.0477 26.3896 33.621L26.718 33.8689L26.9516 34.0569L27.4839 34.4991L27.7378 34.7052C28.6892 35.4589 29.3453 35.7534 30.25 35.7534C31.1547 35.7534 31.8108 35.4589 32.7622 34.7052L32.9708 34.5366L33.5484 34.0569L33.782 33.8689L34.1104 33.621C34.9073 33.0477 35.6315 32.75 36.5 32.75ZM23.5 10C31.1938 10 37.9234 14.8787 40.4427 21.8275L41.5373 17.4501C41.7049 16.7804 42.3836 16.3732 43.0533 16.5407C43.6812 16.6978 44.0782 17.3041 43.9874 17.9311L43.9626 18.0567L41.5796 27.5849L32.4267 22.8608C31.8132 22.5441 31.5726 21.7902 31.8892 21.1767C32.1847 20.6041 32.8613 20.3563 33.4489 20.5833L33.5733 20.6392L38.2162 23.0353C36.154 16.8727 30.2576 12.5 23.5 12.5C16.088 12.5 9.70788 17.7689 8.29288 24.8866L8.23112 25.2165L5.76888 24.7835C7.25962 16.3047 14.7596 10 23.5 10Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSwimStrokeRateOutline);
export default __webpack_public_path__ + "static/media/swim_stroke_rate_outline.756c9009.svg";
export { ForwardRef as ReactComponent };