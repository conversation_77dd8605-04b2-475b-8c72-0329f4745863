var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgExploreOutline1(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M19.512 3.48065L19.4221 3.50043C10.0256 5.58956 3 13.9743 3 24C3 34.0257 10.0256 42.4104 19.4221 44.4996C20.5561 44.7517 21.7245 44.9121 22.9193 44.9727L23.0008 44.9766C23.3319 44.9922 23.6651 45 24 45C24.335 45 24.6681 44.9922 24.9992 44.9766C26.1908 44.9208 27.3564 44.7657 28.488 44.5194L28.5779 44.4996C37.9744 42.4104 45 34.0257 45 24C45 13.9743 37.9744 5.58955 28.5779 3.50043C27.8685 3.34272 27.1457 3.22089 26.4114 3.13694C26.2731 3.12114 26.1345 3.10668 25.9955 3.09357C25.9134 3.08584 25.8313 3.07858 25.7489 3.07179C25.1723 3.02425 24.589 3 24 3C23.2212 3 22.4523 3.04239 21.6955 3.125C20.9554 3.20578 20.2269 3.32501 19.512 3.48065ZM22.2119 42.3695L22.1657 42.4102C13.219 41.5296 6.14375 34.2756 5.54156 25.25H15.0284C15.3293 31.8517 18.0009 37.8354 22.2119 42.3695ZM15.0284 22.75C15.3293 16.1483 18.0009 10.1646 22.2119 5.63052L22.1657 5.58979C13.219 6.4704 6.14375 13.7244 5.54156 22.75H15.0284ZM17.5313 22.75C17.8289 16.8284 20.2289 11.461 24 7.37905C27.7711 11.461 30.1711 16.8284 30.4687 22.75H17.5313ZM17.5313 25.25H30.4687C30.1711 31.1716 27.7711 36.539 24 40.6209C20.2289 36.539 17.8289 31.1716 17.5313 25.25ZM32.9716 25.25C32.6707 31.8517 29.9991 37.8354 25.7881 42.3695L25.8343 42.4102C34.781 41.5296 41.8563 34.2756 42.4584 25.25H32.9716ZM42.4584 22.75C41.8563 13.7244 34.781 6.4704 25.8343 5.58979L25.7881 5.63051C29.9991 10.1646 32.6707 16.1483 32.9716 22.75H42.4584Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgExploreOutline1);
export default __webpack_public_path__ + "static/media/explore_outline-1.1759053d.svg";
export { ForwardRef as ReactComponent };