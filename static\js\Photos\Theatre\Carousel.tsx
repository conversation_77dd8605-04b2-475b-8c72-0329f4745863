import makeStyles from '@mui/styles/makeStyles';
import React from 'react';
import AliceCarousel from '../../AliceCarousel';
import { Props as AliceCarouselProps } from 'react-alice-carousel/lib/types';
import NavButton from '../../AliceCarousel/NavButton';
import { Photo } from '../../types/WorkoutPayload';
import PhotoSlide from './PhotoSlide';

interface CarouselProps {
  classes?: Record<string, string>;
  photos: Photo[];
  onChange: (photo: Photo | null) => void;
  selectedPhoto?: Photo;
}

const useStyles = makeStyles(
  {
    root: {
      width: '100%',
      '& .alice-carousel__stage-item': {
        display: 'inline-flex',
      },
    },
  },
  { name: 'Carousel' },
);

function Carousel(props: CarouselProps & AliceCarouselProps): React.ReactElement {
  const classes = useStyles(props);
  const { photos, selectedPhoto, onChange, ...passTroughProps } = props;

  return (
    <div className={classes.root}>
      <AliceCarousel
        onSlideChanged={({ item }) => onChange(photos[item] || null)}
        keyboardNavigation
        activeIndex={selectedPhoto ? photos.indexOf(selectedPhoto) : 0}
        mouseTracking
        renderNextButton={({ isDisabled }) => <NavButton disabled={isDisabled} next />}
        renderPrevButton={({ isDisabled }) => <NavButton disabled={isDisabled} prev />}
        items={photos.map((photo) => (
          <PhotoSlide photo={photo} />
        ))}
        {...passTroughProps}
      />
    </div>
  );
}

export default Carousel;
