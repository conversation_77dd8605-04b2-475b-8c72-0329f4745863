var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgLapCountDownhillOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M10.3176 25.2906C10.9648 25.2906 11.4971 25.7824 11.5611 26.4127L11.5676 26.5406L11.567 38.2907L14.4249 34.8619C14.8374 34.367 15.5515 34.2715 16.0761 34.6203L16.1854 34.7019C16.6804 35.1144 16.7758 35.8285 16.427 36.3531L16.3454 36.4624L10.3176 43.6958L4.28975 36.4624C3.8478 35.9321 3.91945 35.1439 4.4498 34.7019C4.94479 34.2894 5.6644 34.3243 6.11748 34.762L6.21031 34.8619L9.067 38.2907L9.0676 26.5406C9.0676 25.9365 9.49608 25.4325 10.0657 25.316L10.1898 25.297L10.3176 25.2906ZM36.5214 4.76965L38.9786 5.23037L37.662 12.2497L42.25 12.25V14.75L37.193 14.7497L35.599 23.2497L40 23.25V25.75L35.131 25.7497L33.7286 33.2304L31.2714 32.7697L32.587 25.7497H27.631L26.2286 33.2304L23.7714 32.7697L25.087 25.7497L20.5 25.75V23.25L25.556 23.2497L27.15 14.7497L22.75 14.75V12.25L27.619 12.2497L29.0214 4.76965L31.4786 5.23037L30.162 12.2497H35.118L36.5214 4.76965ZM34.65 14.7497H29.693L28.099 23.2497H33.056L34.65 14.7497Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgLapCountDownhillOutline);
export default __webpack_public_path__ + "static/media/lap_count_downhill_outline.e64b0e06.svg";
export { ForwardRef as ReactComponent };