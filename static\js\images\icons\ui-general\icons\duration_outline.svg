var _path;

var _excluded = ["title", "titleId"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

import * as React from "react";

function SvgDurationOutline(_ref, svgRef) {
  var title = _ref.title,
      titleId = _ref.titleId,
      props = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/React.createElement("svg", _extends({
    width: 48,
    height: 48,
    viewBox: "0 0 48 48",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M26.75 4C27.4404 4 28 4.55964 28 5.25C28 5.89721 27.5081 6.42953 26.8778 6.49355L26.75 6.5H25.249L25.2494 8.54184C34.5059 9.16422 41.75 16.6555 41.75 26C41.75 35.8447 33.7619 44 24 44C14.2381 44 6.25 35.8447 6.25 26C6.25 16.6558 13.4936 9.16475 22.7496 8.54191L22.749 6.5H21.25C20.5596 6.5 20 5.94036 20 5.25C20 4.60279 20.4919 4.07047 21.1222 4.00645L21.25 4H26.75ZM24 11C15.5234 11 8.75 17.6316 8.75 26C8.75 34.4755 15.6305 41.5 24 41.5C32.3695 41.5 39.25 34.4755 39.25 26C39.25 17.6316 32.4766 11 24 11ZM24 12.75C31.373 12.75 37.25 18.627 37.25 26C37.25 29.6694 35.8346 33.1979 33.3812 35.6365C32.8916 36.1232 32.1001 36.1208 31.6135 35.6312C31.1268 35.1416 31.1292 34.3501 31.6188 33.8635C33.5917 31.9024 34.75 29.0148 34.75 26C34.75 20.0077 29.9923 15.25 24 15.25C23.3096 15.25 22.75 14.6904 22.75 14C22.75 13.3096 23.3096 12.75 24 12.75ZM24 17.25C28.9429 17.25 33 21.3071 33 26.25C33 28.6808 32.0463 30.9815 30.3812 32.6365C29.8916 33.1232 29.1001 33.1208 28.6135 32.6312C28.1268 32.1416 28.1292 31.3501 28.6188 30.8635C29.811 29.6784 30.5 28.0162 30.5 26.25C30.5 22.6878 27.5622 19.75 24 19.75C23.3096 19.75 22.75 19.1904 22.75 18.5C22.75 17.8096 23.3096 17.25 24 17.25ZM24 21.75C26.5129 21.75 28.5 23.7371 28.5 26.25C28.5 27.4622 28.0063 28.5167 27.1312 29.3865C26.6416 29.8732 25.8501 29.8708 25.3635 29.3812C24.8768 28.8916 24.8792 28.1001 25.3688 27.6135C25.7963 27.1885 26 26.7534 26 26.25C26 25.1693 25.2093 24.3294 24.1527 24.2553L23.8722 24.2435C23.2419 24.1795 22.75 23.6472 22.75 23C22.75 22.3096 23.3096 21.75 24 21.75ZM37.2824 8.02499L37.3839 8.11612L41.3839 12.1161C41.872 12.6043 41.872 13.3957 41.3839 13.8839C40.9283 14.3395 40.2085 14.3699 39.7176 13.975L39.6161 13.8839L35.6161 9.88388C35.128 9.39573 35.128 8.60427 35.6161 8.11612C36.0717 7.6605 36.7915 7.63013 37.2824 8.02499Z",
    fill: "#303030"
  })));
}

var ForwardRef = /*#__PURE__*/React.forwardRef(SvgDurationOutline);
export default __webpack_public_path__ + "static/media/duration_outline.08e574a2.svg";
export { ForwardRef as ReactComponent };