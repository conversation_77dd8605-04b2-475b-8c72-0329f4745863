(this["webpackJsonpbig-screen"]=this["webpackJsonpbig-screen"]||[]).push([[59],{604:function(_){_.exports=JSON.parse('{"ASCENT_TIME":"Ascent time","DESCENT_TIME":"Descent time","TOTAL_DESCENT":"Total descent","PHOTOS_TITLE":"Images from the route","JUMP_TO_SUMMARY":"Jump to summary","ERROR_VIEW_404_TITLE":"Page not found","ERROR_VIEW_404_SUBTITLE":"Sorry! The page you are looking for does not exist or is missing.","ERROR_VIEW_UNSPECIFIC_TITLE":"Something went wrong","ERROR_VIEW_UNSPECIFIC_SUBTITLE":"","ERROR_VIEW_STATUS_CODE_ERROR":"{{statusCode}} error","DIVE_VISIBILITY":"Visibility","DIVE_NUMBER_IN_SERIES":"No. in series","SKI_TIME":"Downhill time","SKI_RUNS":"Downhill count","SKI_DISTANCE":"Downhill distance","AVG_SKI_SPEED":"Avg. downhill speed","MAX_SKI_SPEED":"Max. downhill speed","MOST_POPULAR_PRODUCTS_TITLE":"Our most popular products","VIEW_PRODUCT":"View product","GO_TO_HOMEPAGE":"Go to homepage","SHOW_MORE":"Show more","SHOW_LESS":"Show less","N_ITEMS_one":"{{count}} item","N_ITEMS_two":"{{count}} items","N_ITEMS_few":"{{count}} items","N_ITEMS_many":"{{count}} items","N_ITEMS_plural":"{{count}} items","N_ITEMS_other":"{{count}} items"}')}}]);
//# sourceMappingURL=59.10c8a30a.chunk.js.map