import { ActivityIds, getActivityConfigBySTId } from '../activities/helper';
import { Workout } from '../models/Workout';
import PathConfiguration from './PathConfiguration';
import { getGraphsByActivityName } from './Summary/helpers';

const FALLBACK_ACTIVITY_NAME = 'Fallback';

export const getGraphs = (workout: Workout, filterDuplicates = false): string[] => {
  const activityConfig =
    getActivityConfigBySTId(workout.workout.activityId) ||
    getActivityConfigBySTId(ActivityIds.UNSPECIFIED);

  if (!activityConfig) {
    throw new Error('Unspecified activity not found');
  }
  let graphs =
    getGraphsByActivityName(activityConfig.Key) || getGraphsByActivityName(FALLBACK_ACTIVITY_NAME);

  if (!graphs) throw new Error(`${FALLBACK_ACTIVITY_NAME} graphs not found`);

  if (filterDuplicates) {
    graphs = graphs.filter(
      (v, i, a) =>
        a.findIndex(
          (t) => PathConfiguration[t]?.getExtension === PathConfiguration[v]?.getExtension,
        ) === i,
    );
  }
  return graphs;
};
