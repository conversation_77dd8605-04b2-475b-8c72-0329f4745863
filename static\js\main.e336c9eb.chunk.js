(this["webpackJsonpbig-screen"]=this["webpackJsonpbig-screen"]||[]).push([[4],{124:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return c}));var a,r=n(0),o=n.n(r);!function(t){t.metric="METRIC",t.imperial="IMPERIAL"}(a||(a={}));var i=function(t){return"en-US"===t?a.imperial:a.metric},s=function(t){return["United States","Bahamas"].includes(t)?a.imperial:a.metric},u=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(null===e||void 0===e?void 0:e.AutoDetectedCountry)t=s(e.AutoDetectedCountry);else{var n=navigator,a=n.language;t=i(a)}return t},c=o.a.createContext([u(),function(){}]);c.displayName="MeasurementSystemContext"},125:function(t,e,n){"use strict";n.d(e,"d",(function(){return p})),n.d(e,"c",(function(){return b})),n.d(e,"e",(function(){return x})),n.d(e,"b",(function(){return y})),n.d(e,"a",(function(){return S}));var a,r=n(29),o=n(272),i=n(34),s=n.n(i),u=n(70),c=n(193),l=n(268),d=["Url"];function f(t){return h.apply(this,arguments)}function h(){return(h=Object(u.a)(s.a.mark((function t(e){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,fetch(e);case 2:if((n=t.sent).ok){t.next=5;break}throw n;case 5:return t.abrupt("return",n.json());case 6:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function p(t,e){return m.apply(this,arguments)}function m(){return(m=Object(u.a)(s.a.mark((function t(e,n){var a,r;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a="https://maps.suunto.com",a+="/api/camera/".concat(e,"/").concat(n).concat(window.location.search),t.next=4,f(a);case 4:return(r=t.sent).error&&console.error(r.error),t.abrupt("return",new l.a(r.workout,r,e,n));case 7:case"end":return t.stop()}}),t)})))).apply(this,arguments)}!function(t){t.public="PUBLIC"}(a||(a={}));var v,g=function(t){return(Object.values(c.a).find((function(e){var n=e.language;return t===n}))||c.a.enUS).code};function b(t){return j.apply(this,arguments)}function j(){return(j=Object(u.a)(s.a.mark((function t(e){var n,a,r;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return"https://www.suunto.com",n=g(e),a="".concat("https://www.suunto.com","/").concat(n,"/404"),t.next=5,fetch(a);case 5:return r=t.sent,t.abrupt("return",r.text());case 7:case"end":return t.stop()}}),t)})))).apply(this,arguments)}!function(t){t[t.WATCH=3]="WATCH"}(v||(v={}));var x=function(t){var e;return function(){return e||(e=t.apply(void 0,arguments)),e}}(function(){var t=Object(u.a)(s.a.mark((function t(){var e;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return"https://www.suunto.com",t.next=3,fetch("".concat("https://www.suunto.com","/api/detectculture"));case 3:return e=t.sent,t.next=6,e.json();case 6:return t.abrupt("return",t.sent);case 7:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}()),y=function(){var t=Object(u.a)(s.a.mark((function t(e){var n,a,r,o,i,u;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,x();case 2:return n=t.sent,a=n.AutoDetectedCulture,r=new URLSearchParams(window.location.search),o=r.get("token"),i=o?"?token="+o:"",t.next=9,fetch("/price/".concat((null===a||void 0===a?void 0:a.toLowerCase())||"","/").concat(e,"/").concat(i));case 9:return u=t.sent,t.next=12,u.json();case 12:return t.abrupt("return",t.sent);case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),S=function(){var t=Object(u.a)(s.a.mark((function t(){var e,n,a,i,u;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e="https://www.suunto.com",t.next=3,x();case 3:return n=t.sent,a=n.AutoDetectedCulture,t.next=7,fetch("".concat(e,"/api/search/products?activecategory=0&languageId=").concat(a,"&page=1&pageid=16098"));case 7:return i=t.sent,t.next=10,i.json();case 10:return u=t.sent.ProductLines,t.abrupt("return",u.filter((function(t){return t.Categories.includes(v.WATCH)})).map((function(t){var e=t.Url,n=Object(o.a)(t,d);return Object(r.a)(Object(r.a)({},n),{},{Url:"https://www.suunto.com/".concat(e)})})));case 12:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}()},152:function(t,e,n){"use strict";var a=n(11),r=n(29),o=n(499),i=n(526),s=function(t){t?(this.globalConfig=Object(r.a)(Object(r.a)({},this.globalConfig),t),this.eventConfig.client=t.client||"",this.loaded=!0):this.loaded=!1};s.prototype.init=function(t){this.loaded||(this.globalConfig=Object(r.a)(Object(r.a)({},this.globalConfig),t),this.eventConfig.client=t.client||"",this.loaded=!0)},s.prototype.utils={safeCall:function(t,e){t&&"function"===typeof t&&t(e)},requestSuccess:function(t){return t&&200===t.status},getLanguageFromQuery:function(){var t=window.location.search,e=null;return t.replace(/\b(lang|locale)=([a-zA-Z]+)(?:[_-]([a-zA-Z]+))?/gi,(function(t,n,a,r){return(e={}).language=a.toLowerCase(),e.country=r?r.toUpperCase():null,null})),e},getLanguageFromNavigator:function(){var t=navigator.userLanguage||navigator.language;if(t){var e=null;return t.replace(/([a-zA-Z]+)(?:[_-]([a-zA-Z]+))?$/gi,(function(t,n,a){return(e={}).language=n.toLowerCase(),e.country=a?a.toUpperCase():null,null})),e}return null},getLanguageAndRegion:function(){var t=this.getLanguageFromQuery()||this.getLanguageFromNavigator();return t||{language:"",country:""}},getDeviceInfo:function(){return(new i).getResult()},objectEncode:function(t){var e="",n=!0;for(var a in t)t.hasOwnProperty(a)&&(n?(e+=a+"="+encodeURI(t[a]),n=!1):e+="&"+a+"="+encodeURI(t[a]));return e},getUnixTimestamp:function(){return(new Date).getTime()},generateJwtToken:function(t,e){return function(t,e){var n=JSON.stringify({typ:"JWT",alg:"HS256"}),a=Math.floor((new Date).getTime()/1e3),r=JSON.stringify({jti:e.jwtId,sub:e.subject,aud:e.audience,iat:a,iss:e.issuer,exp:a+t.duration}),i=o.enc.Base64url.stringify(o.enc.Utf8.parse(n))+"."+o.enc.Base64url.stringify(o.enc.Utf8.parse(r)),s=function(t,e){var n=o.enc.Utf8.parse(e),a=o.HmacSHA256(t,n);return o.enc.Base64url.stringify(a)}(i,t.secretKey);return"".concat(i,".").concat(s)}(t,e)}},s.prototype.eventUtils={calculateChecksum:function(t){var e=t.client+","+t.userId+","+t.uploadTime+","+t.device+","+t.events;return o.MD5(e).toString(o.enc.Hex)},buildDeviceInfo:function(){var t=this.utils().getDeviceInfo(),e=this.utils().getLanguageAndRegion();return{language:e.language,country:e.country,deviceManufacturer:t.browser.name,deviceModel:t.browser.name,versionName:t.browser.version,osName:t.os.name,platform:"Web"}},buildEventInfo:function(t,e,n){var r={};return r.eventType=t,r.timestamp=e,r.eventProperties=n.event||Object.entries(n).reduce((function(t,e){var n=Object(a.a)(e,2),r=n[0],o=n[1];return"type"!==r&&(t[r]=o),t}),{}),r.userProperties=n.user||{},[r]},buildEvent:function(t,e,n,a){var r=this.utils().getUnixTimestamp(),o={client:e.client,userId:t.userId||"-1",uploadTime:r};return o.device=JSON.stringify(this.buildDeviceInfo()),o.events=JSON.stringify(this.buildEventInfo(n,r,a)),o.checksum=this.calculateChecksum(o),o},utils:function(){return s.prototype.utils}},s.prototype.globalConfig={domain:"https://event.suunto.com",uri:"event/submit",retry:3,duration:3600,tokenCache:!1},s.prototype.eventConfig={client:"apikeyst",jwtId:"suunto-event",subject:"suunto-line",audience:"suuntoapp",issuer:"suunto"},s.prototype.logEvent=function(t,e){this.submit(Object(r.a)({type:t},e))},s.prototype.submit=function(t){try{if(!this.loaded)throw new Error("AnalysisEvent is not init");if(!t||!t.type)throw new Error("type is missing");var e=this.globalConfig,n=this.eventConfig,a=this,r=t.type,o=this.utils.generateJwtToken(e,n),i=this.eventUtils.buildEvent(e,n,r,t),s=this.utils.objectEncode(i);function u(t,n,r,i,s,c){var l=!1;t<=1&&(l=!0),fetch("".concat(e.domain,"/").concat(e.uri),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:"Bearer ".concat(o)},body:s}).then((function(e){if(a.utils.requestSuccess(e))e.json().then((function(e){if(e.code>=200&&e.code<400)a.utils.safeCall(c.onSuccess,e);else{if(l)return void a.utils.safeCall(c.onFailure,e);u(--t,n,r,i,s,c)}})).catch((function(t){}));else{if(l)return void a.utils.safeCall(c.onFailure,e);u(--t,n,r,i,s,c)}})).catch((function(e){l?a.utils.safeCall(c.onError,e):u(--t,n,r,i,s,c)}))}u(e.retry,r,e,n,s,t)}catch(u){console.log(u)}};var u=function(){var t;return function(){return t||(t=new s)}}(),c={getInstance:function(){return u()}};e.a=c},192:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return c})),n.d(e,"c",(function(){return l}));var a,r,o=n(29),i=n.p+"static/media/proximanova-bold.c43519e2.otf",s=n.p+"static/media/proximanova-regular.fd4e0c28.otf";!function(t){t.dark="#303030",t.light="#f1f1f1"}(a||(a={})),function(t){t.CloudyGrey="#D8DBDD",t.DarkGrey="#7E8084",t.DarkestGrey="#3D3D3D"}(r||(r={}));var u={spacing:function(t){return"".concat(t,"rem")},suuntoColors:{CloudyGrey:r.CloudyGrey,DarkestGrey:r.DarkestGrey,DarkGrey:r.DarkGrey},typography:{fontFamily:["Proxima Nova","sans-serif"].join(","),fontSize:10,h1:{fontSize:"4rem"},h2:{fontSize:"2.4rem"},subtitle2:{fontSize:"1.2rem"},h4:{fontSize:"2.4rem",fontWeight:"bold",lineHeight:9/8},h5:{fontSize:"1.8rem",lineHeight:7/8},h6:{fontSize:"1.6rem"},body1:{fontSize:"1.4rem"},body2:{fontSize:"1.2rem"},button:{fontSize:"1.6rem"}},breakpoints:{values:{xs:0,sm:600,md:960,lg:1280,xl:1920}},palette:{action:{disabledBackground:r.CloudyGrey},background:{paper:"#fff",default:"#fff"},body:{main:a.dark},primary:{main:"#16B4EA",dark:"#1190BB",light:"#8ED2EA",contrastText:"#fff"},neutral:{main:"#ACAFB6"},text:{primary:a.dark},divider:"#ECEDF0"},components:{MuiButton:{defaultProps:{disableElevation:!0},styleOverrides:{root:{padding:"0.6em 1em",textTransform:"none"}}},MuiBackdrop:{styleOverrides:{root:{backgroundColor:"rgba(0, 0, 0, 0.82)"}}},MuiCssBaseline:{styleOverrides:{"@font-face":{fontFamily:"Proxima Nova",fontWeight:"normal",src:'url("'.concat(s,'") format("opentype")')},fallbacks:{"@font-face":[{fontFamily:"Proxima Nova",fontWeight:"bold",src:'url("'.concat(i,'") format("opentype")')}]},html:{fontSize:"62.5%",maxWidth:"100vw",height:"100%"},body:{maxWidth:"100%",margin:0,height:"fill-available",fallbacks:[{height:"100vh"}],position:"relative"},"svg, path":{fill:"currentColor"},"#root":{height:"fill-available",fallbacks:[{height:"100vh"}]},"*":{boxSizing:"border-box"}}}}},c=Object(o.a)(Object(o.a)({},u),{},{palette:{mode:"dark",action:{disabledBackground:r.DarkestGrey},background:{paper:a.dark,default:a.dark},text:{primary:a.light},body:{main:a.light},neutral:{main:"#ACAFB6"},primary:{main:"#1190BB",light:"#16B4EA",contrastText:"#fff"}}}),l=u},193:function(t,e,n){"use strict";e.a={fiFI:{language:"fi",code:"fi-FI",label:"Suomi"},enUS:{language:"en",code:"en-US",label:"English"},frFR:{language:"fr",code:"fr-FR",label:"Fran\xe7aise"},itIT:{language:"it",code:"it-IT",label:"Italiano"},ruRU:{language:"ru",code:"ru-RU",label:"P\u0443\u0441\u0441\u043a\u0438\u0439"},koKR:{language:"ko",code:"ko-KR",label:"Korea"},svSE:{language:"sv",code:"sv-SE",label:"Svenska"},csCZ:{language:"cs",code:"cs-CZ",label:"\u010ce\u0161tina"},slSI:{language:"sl",code:"sl-SI",label:"Sloven\u0161\u010dina"},plPL:{language:"pl",code:"pl-PL",label:"Polski"},elGR:{language:"el",code:"el-GR",label:"\u0395\u03bb\u03bb\u03b7\u03bd\u03b9\u03ba\u03ac"},skSK:{language:"sk",code:"sk-SK",label:"Sloven\u010dina"},nbNO:{language:"nb",code:"nb-NO",label:"Norsk bokm\xe5l"},deDE:{language:"de",code:"de-DE",label:"Deutsch"},huHU:{language:"hu",code:"hu-HU",label:"Magyar"},nlNL:{language:"nl",code:"nl-NL",label:"Nederlands"},ptPT:{language:"pt",code:"pt-PT",label:"Portugu\xeas"},daDK:{language:"da",code:"da-DK",label:"Dansk"},thTH:{language:"th",code:"th-TH",label:"\u0e44\u0e17\u0e22"},zhCHS:{language:"zh",code:"zh-CHS",label:"\u4e2d\u6587"},idID:{language:"id",code:"id-ID",label:"Bahasa Indonesia"},jaJP:{language:"ja",code:"ja-JP",label:"\u65e5\u672c\u8a9e"},esES:{language:"es",code:"es-ES",label:"Espa\xf1ol"},trTR:{language:"tr",code:"tr-TR",label:"T\xfcrk\xe7e"},viVI:{language:"vi",code:"vi-VN",label:"Vi\u1ec7t Nam"}}},194:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var a=n(53),r=n(54),o=function(){function t(e){Object(a.a)(this,t),this.points=e,this.bounds=void 0,this.tStart=void 0,this.tEnd=void 0,this.duration=void 0,this.bounds=t.getBounds(e),this.tStart=e[0].timestamp,this.tEnd=e[e.length-1].timestamp,this.duration=this.tEnd-this.tStart}return Object(r.a)(t,[{key:"getLookAtPoints",value:function(t,e){var n=this,a=this.duration/(t-1),r=[],o=this.tStart;return this.points.forEach((function(t){if(t.timestamp>=o){var i=(t.timestamp-n.tStart)/n.duration,s=e.getValue(i);r.push({lat:t.latitude*(1-s)+n.bounds.latCenter*s,lon:t.longitude*(1-s)+n.bounds.lonCenter*s,alt:t.altitude*(1-s)+n.bounds.altCenter*s,t:(o-n.tStart)/n.duration}),o+=a}})),r}}],[{key:"getBounds",value:function(t){var e=t[0].latitude,n=t[0].latitude,a=t[0].longitude,r=t[0].longitude,o=t[0].altitude,i=t[0].altitude;return t.forEach((function(t){e=Math.min(e,t.latitude),n=Math.max(n,t.latitude),a=Math.min(a,t.longitude),r=Math.max(r,t.longitude),o=Math.min(o,t.altitude),i=Math.max(i,t.altitude)})),{latMin:e,latMax:n,latCenter:(e+n)/2,lonMin:a,lonMax:r,lonCenter:(a+r)/2,altMin:o,altMax:i,altCenter:(o+i)/2}}}]),t}()},207:function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return l}));var a=n(11),r=n(0),o=n.n(r),i=n(3),s=o.a.createContext(!1);s.displayName="DeveloperContext";var u=function(t){window.localStorage.setItem("isDeveloper",t.toString()),window.dispatchEvent(new Event("storage"))},c=function(){return"true"===window.localStorage.getItem("isDeveloper")},l=function(t){var e=t.children,n=o.a.useState(c()),r=Object(a.a)(n,2),l=r[0],d=r[1];return o.a.useEffect((function(){return window.isDeveloper=u,function(){delete window.isDeveloper}}),[]),o.a.useEffect((function(){var t=function(){d(c())};return window.addEventListener("storage",t),function(){window.removeEventListener("storage",t)}}),[]),Object(i.jsx)(s.Provider,{value:l,children:e})}},208:function(t,e,n){"use strict";e.a=n.p+"static/media/suunto_logo.38de6591.svg"},209:function(t,e,n){"use strict";e.a=n.p+"static/media/suunto_logo_white.fe1bbd52.svg"},211:function(t,e,n){"use strict";var a=function(t){return t.getSpeed()},r={HeartRate:{getExtension:function(t){return t.getHeartRate()}},Pace:{getExtension:a},Speed:{getExtension:a},Altitude:{getExtension:function(t){return t.getAltitude()}},Cadence:void 0,Epoc:void 0,Temperature:void 0,SeaLevelPressure:void 0,BikeCadence:void 0,StrokeRate:void 0,SwimPace:void 0,Swolf:void 0,SpeedKnots:void 0,Depth:void 0,VerticalSpeed:void 0};e.a=r},213:function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var a=n(11),r=n(0),o=n.n(r),i=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=o.a.useState(t),n=Object(a.a)(e,2),r=n[0],i=n[1],s=function(){return i(!0),function(){return i(!1)}};return[r,s]}},214:function(t,e,n){"use strict";n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return l}));var a=n(11),r=n(0),o=n.n(r),i=n(93),s=n(98),u=n(3),c=o.a.createContext({position:0,setPosition:s.a,isPlaying:!1,setIsPlaying:s.a,isMapInitialized:!1,setIsMapInitialized:s.a});c.displayName="PositionContext";var l=function(t){var e=t.children,n=o.a.useState(0),r=Object(a.a)(n,2),s=r[0],l=r[1],d=o.a.useState(!1),f=Object(a.a)(d,2),h=f[0],p=f[1],m=o.a.useState(0),v=Object(a.a)(m,2),g=v[0],b=v[1],j=o.a.useState(!1),x=Object(a.a)(j,2),y=x[0],S=x[1],k=Object(i.c)().logEvent;return o.a.useEffect((function(){y&&h?(k(i.b.SharedWorkoutPlaybackStarted),b(Date.now()/4e4-s)):b(0)}),[y,h,k]),o.a.useEffect((function(){p(y)}),[y]),o.a.useEffect((function(){if(y&&g){var t=requestAnimationFrame((function(){if(h){var e=Math.min(Date.now()/4e4-g,1);1===e&&(k(i.b.SharedWorkoutPlaybackCompleted),p(!1)),l(e),t=0}}));return function(){t&&cancelAnimationFrame(t)}}}),[h,s,g,y,k]),Object(u.jsx)(c.Provider,{value:{position:s,isPlaying:h,setPosition:l,setIsPlaying:p,isMapInitialized:y,setIsMapInitialized:S},children:e})}},268:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var a=n(53),r=n(54),o=n(57),i=n(211),s=n(161),u=n(273);var c={Pace:!0,Speed:!0},l=function(){function t(e,n,r,o){Object(a.a)(this,t),this.user=r,this.id=o,this.workout=void 0,this.center=void 0,this.cameraPath=void 0,this.workout=e,this.center=[e.centerPosition.x,e.centerPosition.y],this.cameraPath=u.a.create(this,n)}return Object(r.a)(t,[{key:"showMap",value:function(){var t=this.workout,e=t.availableExtensions,n=t.centerPosition,a=(n=void 0===n?{}:n).x,r=n.y;return Boolean(e.includes(o.b.LocationStreamExtension)||a&&r)}},{key:"getSummary",value:function(){return t.getExtension(this.workout.extensions,o.b.SummaryExtension)}},{key:"getFitness",value:function(){return t.getExtension(this.workout.extensions,o.b.FitnessExtension)}},{key:"getSpeed",value:function(){return t.getExtension(this.workout.extensions,o.b.SpeedStreamExtension)}},{key:"getAltitude",value:function(){return t.getExtension(this.workout.extensions,o.b.AltitudeStreamExtension)}},{key:"getLocation",value:function(){return t.getExtension(this.workout.extensions,o.b.LocationStreamExtension)}},{key:"getHeartRate",value:function(){return t.getExtension(this.workout.extensions,o.b.HeartrateStreamExtension)}},{key:"getDiveHeaderExtension",value:function(){return t.getExtension(this.workout.extensions,o.b.DiveHeaderExtension)}},{key:"getSwimmingHeaderExtension",value:function(){return t.getExtension(this.workout.extensions,o.b.SwimmingHeaderExtension)}},{key:"getSkiExtension",value:function(){return t.getExtension(this.workout.extensions,o.b.SkiExtension)}},{key:"getMultisportMarkerExtension",value:function(){return t.getExtension(this.workout.extensions,"MultisportMarker")}}],[{key:"getExtension",value:function(t,e){return t.find(function(t){return function(e){return e.type===t}}(e))}},{key:"getRoute",value:function(t,e,n){var a,r=e.getLocation(),o=null===(a=i.a[t])||void 0===a?void 0:a.getExtension(e),u={label:n.min,value:1/0,lon:0,lat:0,position:0},l={label:n.max,value:-1/0,lon:0,lat:0,position:0},d=[l];c[t]||d.push(u);var f=[];if(!r)return{poi:d,pts:f};var h=r.locationPoints,p=[];if(o)if(o.points)p=o.points;else if(o.timestamps&&o.values)for(var m=0;m<o.timestamps.length;++m)p.push({timestamp:o.timestamps[m],value:o.values[m]});var v,g=h.length,b=p.length,j=0,x=0,y=h[0].timestamp,S=h[h.length-1].timestamp;if(!b||p[0].timestamp>y)v=null;else do{v=p[x++]}while(x<b&&v.timestamp<y);var k,E,w=h[j],O=w,C=v;for(v=p[x];y<S;)if(x>=b||w.timestamp<v.timestamp){y=w.timestamp;var P=null;if(null!==C&&x<b){var _=(y-C.timestamp)/(v.timestamp-C.timestamp||1);P=C.value+(v.value-C.value)*_}for(f.push([w.latitude,w.longitude,P,y]),O=w;j<g&&w.timestamp==O.timestamp;)w=h[++j]}else if(w.timestamp>v.timestamp){for(C=v;x<b&&v.timestamp==C.timestamp;)v=p[++x]}else{for(y=w.timestamp,f.push([w.latitude,w.longitude,v.value,y]),O=w,C=v;j<g&&w.timestamp==O.timestamp;)w=h[++j];for(;x<b&&v.timestamp==C.timestamp;)v=p[++x]}for(var L=0,I=f;L<I.length;L++){var A=I[L];null!==A[2]&&(A[2]<u.value&&(k=A,u.value=A[2]),A[2]>l.value&&(E=A,l.value=A[2]))}for(var M=0,D=f;M<D.length;M++){var T=D[M];T[2]=null===T[2]?1:Math.floor(254*(T[2]-u.value)/(l.value-u.value))}for(var R=f[0],N=s.MercatorCoordinate.fromLngLat({lng:R[1],lat:R[0]}),z=0,F=0,H=f;F<H.length;F++){R=H[F];var B=s.MercatorCoordinate.fromLngLat({lng:R[1],lat:R[0]}),U=B.x-N.x,W=B.y-N.y;z+=Math.sqrt(U*U+W*W),R[4]=z,N=B}return k&&(u.lon=k[1],u.lat=k[0],u.position=k[4]),E&&(l.lon=E[1],l.lat=E[0],l.position=E[4]),{poi:d,pts:f}}}]),t}()},273:function(t,e,n){"use strict";n.d(e,"b",(function(){return d})),n.d(e,"a",(function(){return f}));var a=n(29),r=n(53),o=n(54),i=n(65),s=function(){function t(e){Object(r.a)(this,t),this.segments=[];for(var n=0;n<e.length-3;n++)this.segments.push(new u(e[n],e[n+1],e[n+2],e[n+3]))}return Object(o.a)(t,[{key:"getPoint",value:function(t){var e=t*this.segments.length,n=Math.min(Math.floor(e),this.segments.length-1);return this.segments[n].getPoint(e-n)}}]),t}(),u=function(){function t(e,n,a,o){Object(r.a)(this,t),this.a=void 0,this.b=void 0,this.c=void 0,this.d=void 0;var i=Math.pow(e.distance(n),.5)+0,s=Math.pow(n.distance(a),.5)+i,u=Math.pow(a.distance(o),.5)+s,c=n.subtract(e).divide(i-0),l=a.subtract(e).divide(s-0),d=a.subtract(n).divide(s-i),f=c.subtract(l).add(d).multiply(s-i).multiply(1),h=a.subtract(n).divide(s-i),p=o.subtract(n).divide(u-i),m=o.subtract(a).divide(u-s),v=h.subtract(p).add(m).multiply(s-i).multiply(1);this.a=n.subtract(a).multiply(2).add(f).add(v),this.b=n.subtract(a).multiply(-3).subtract(f).subtract(f).subtract(v),this.c=f,this.d=n}return Object(o.a)(t,[{key:"getPoint",value:function(t){return this.a.multiply(t*t*t).add(this.b.multiply(t*t)).add(this.c.multiply(t)).add(this.d)}}]),t}(),c=n(194),l=function(){function t(e){Object(r.a)(this,t),this.array=e}return Object(o.a)(t,[{key:"getValue",value:function(t){var e=t*(this.array.length-1),n=Math.floor(e),a=Math.ceil(e),r=this.array[a]-this.array[n],o=e-n;return this.array[n]+r*o}}]),t}(),d=1.1,f=function(){function t(e){Object(r.a)(this,t),this.numberOfLookAtKeys=16,this.numberOfNarrowLookAtKeys=64,this.numberOfCameraKeys=12,this.lookAtPointCenteringBehaviour=new l([0,0,0,0,0,0,0,.1,.2,.5,1]),this.playDuration=4e4,this.currentProgress=0,this.cameraEasing=function(t){return 1.2*(t*=.92)*(1-Math.pow(t,10))+Math.pow(t,12)},this.track=void 0,this.bounds=void 0,this.lookAtPoints=void 0,this.narrowLookAtPoints=void 0,this.scale={x:1,y:1},this.lookAtCurve=void 0,this.narrowLookAtCurve=void 0,this.cameraPath=void 0;var n=e.getLocation(),a=n?n.locationPoints:[];if(a.length){var o=c.a.getBounds(a);this.scale=this.getPseudoCartesianScale({lon:o.lonCenter,lat:o.latCenter,alt:o.altCenter}),this.track=new c.a(this.timesFromDistances(a)),this.bounds=o,this.lookAtPoints=this.track.getLookAtPoints(this.numberOfLookAtKeys,this.lookAtPointCenteringBehaviour),this.narrowLookAtPoints=this.track.getLookAtPoints(this.numberOfNarrowLookAtKeys,this.lookAtPointCenteringBehaviour),this.scale=this.getPseudoCartesianScale(this.lookAtPoints[0]),this.lookAtCurve=this.getLookAtCurve(this.lookAtPoints),this.narrowLookAtCurve=this.getLookAtCurve(this.narrowLookAtPoints)}else this.lookAtCurve=new s([]),this.narrowLookAtCurve=new s([]);this.cameraPath=new s([])}return Object(o.a)(t,[{key:"timesFromDistances",value:function(t){var e,n=this,r=0;return t.map((function(t){var o=Object(a.a)({},t),i=n.getPseudoCartesianCoordinatesFromLatLonAlt({lon:o.longitude,lat:o.latitude,alt:o.altitude});if(e){var s=i.x-e.x,u=i.y-e.y;r+=Math.sqrt(s*s+u*u)}return o.timestamp=r,e=i,o}))}},{key:"getPseudoCartesianScale",value:function(t){var e=40075017;return{x:Math.cos(Object(i.c)(t.lat))*e/360,y:e/360}}},{key:"getPseudoCartesianCoordinatesFromLatLonAlt",value:function(t){var e=t.lon*this.scale.x,n=t.lat*this.scale.y,a=t.alt;return new i.a(e,n,a)}},{key:"getLatLonAltFromPseudoCartesianCoordinates",value:function(t){return{lat:t.y/this.scale.y,lon:t.x/this.scale.x,alt:t.z}}},{key:"getLookAtCurve",value:function(t){var e=this,n=t.length-1,a={lat:2*t[0].lat-t[1].lat,lon:2*t[0].lon-t[1].lon,alt:2*t[0].alt-t[1].alt,t:2*t[0].t-t[1].t},r={lat:2*t[n].lat-t[n-1].lat,lon:2*t[n].lon-t[n-1].lon,alt:2*t[n].alt-t[n-1].alt,t:2*t[n].t-t[n-1].t},o=[];return o.push(this.getPseudoCartesianCoordinatesFromLatLonAlt(a)),t.forEach((function(t){o.push(e.getPseudoCartesianCoordinatesFromLatLonAlt(t))})),o.push(this.getPseudoCartesianCoordinatesFromLatLonAlt(r)),new s(o)}},{key:"getCameraPosition",value:function(t,e,n,a){var r=Object(i.c)(n),o=Object(i.c)(a),s=Math.cos(o),u=e*s*Math.sin(r),c=e*s*Math.cos(r),l=e*Math.sin(o);return new i.a(t.x+u,t.y+c,t.z+l)}}],[{key:"create",value:function(e,n){var a=new t(e);if(n)for(var r=n.camera,o=a.cameraPath,s=0;s<r.length;s+=12){var c=new i.a(r[s+0],r[s+1],r[s+2]),l=new i.a(r[s+3],r[s+4],r[s+5]),d=new i.a(r[s+6],r[s+7],r[s+8]),f=new i.a(r[s+9],r[s+10],r[s+11]),h=new u(c,l,d,f);h.a=c,h.b=l,h.c=d,h.d=f,o.segments.push(h)}return a}}]),t}()},500:function(t,e){},528:function(t,e,n){var a={"./controls_cs.json":[557,12],"./controls_da.json":[558,13],"./controls_de.json":[559,14],"./controls_el.json":[560,15],"./controls_en.json":[561,16],"./controls_es.json":[562,17],"./controls_fi.json":[563,18],"./controls_fr.json":[564,19],"./controls_it.json":[565,20],"./controls_ja.json":[566,21],"./controls_ko.json":[567,22],"./controls_nb.json":[568,23],"./controls_nl.json":[569,24],"./controls_pl.json":[570,25],"./controls_pt.json":[571,26],"./controls_ru.json":[572,27],"./controls_sv.json":[573,28],"./controls_th.json":[574,29],"./controls_tr.json":[575,30],"./controls_vi.json":[576,31],"./controls_zh.json":[577,32],"./phrases_cs.json":[578,33],"./phrases_da.json":[579,34],"./phrases_de.json":[580,35],"./phrases_el.json":[581,36],"./phrases_en.json":[582,37],"./phrases_es.json":[583,38],"./phrases_fi.json":[584,39],"./phrases_fr.json":[585,40],"./phrases_he.json":[586,41],"./phrases_it.json":[587,42],"./phrases_ja.json":[588,43],"./phrases_ko.json":[589,44],"./phrases_nb.json":[590,45],"./phrases_nl.json":[591,46],"./phrases_pl.json":[592,47],"./phrases_pt.json":[593,48],"./phrases_ru.json":[594,49],"./phrases_sv.json":[595,50],"./phrases_th.json":[596,51],"./phrases_tr.json":[597,52],"./phrases_vi.json":[598,53],"./phrases_zh.json":[599,54],"./translations_cs.json":[600,55],"./translations_da.json":[601,56],"./translations_de.json":[602,57],"./translations_el.json":[603,58],"./translations_en.json":[604,59],"./translations_es.json":[605,60],"./translations_fi.json":[606,61],"./translations_fr.json":[607,62],"./translations_it.json":[608,63],"./translations_ja.json":[609,64],"./translations_ko.json":[610,65],"./translations_nb.json":[611,66],"./translations_nl.json":[612,67],"./translations_pl.json":[613,68],"./translations_pt.json":[614,69],"./translations_ru.json":[615,70],"./translations_sv.json":[616,71],"./translations_th.json":[617,72],"./translations_tr.json":[618,73],"./translations_vi.json":[619,74],"./translations_zh.json":[620,75],"./units_cs.json":[621,76],"./units_da.json":[622,77],"./units_de.json":[623,78],"./units_el.json":[624,79],"./units_en.json":[625,80],"./units_es.json":[626,81],"./units_fi.json":[627,82],"./units_fr.json":[628,83],"./units_it.json":[629,84],"./units_ja.json":[630,85],"./units_ko.json":[631,86],"./units_nb.json":[632,87],"./units_nl.json":[633,88],"./units_pl.json":[634,89],"./units_pt.json":[635,90],"./units_ru.json":[636,91],"./units_sv.json":[637,92],"./units_th.json":[638,93],"./units_tr.json":[639,94],"./units_vi.json":[640,95],"./units_zh.json":[641,96]};function r(t){if(!n.o(a,t))return Promise.resolve().then((function(){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}));var e=a[t],r=e[0];return n.e(e[1]).then((function(){return n(r)}))}r.keys=function(){return Object.keys(a)},r.id=528,t.exports=r},542:function(t,e,n){"use strict";n.r(e);n(291);var a=n(0),r=n.n(a),o=n(210),i=n.n(o),s=n(658),u=n(660),c=n(198),l=n(659),d=n(666),f=n(152),h=n(197),p=n(23),m=n(11),v=n(205),g=n(123),b=n(275),j=n(277),x=n(60),y=n(34),S=n.n(y),k=n(70),E=function(){var t=Object(k.a)(S.a.mark((function t(e){var a;return S.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=n(528),t.abrupt("return",a(e));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),w={loadPath:function(t,e){var n=Object(m.a)(t,1)[0];return function(t,e){return"./".concat(t,"_").concat(e,".json")}(Object(m.a)(e,1)[0],n)},request:function(t,e,n,a){E(e).then((function(t){return a(null,{data:t,status:200})})).catch((function(){return a("Error",null)}))},addPath:"/locales/add/{{lng}}/{{ns}}",allowMultiLoading:!1,parse:function(t){return JSON.parse(t)},parsePayload:function(t,e){return{key:e}},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"},reloadInterval:!1};v.a.use(b.a).use(j.a).use(g.e).init({fallbackLng:"en",defaultNS:x.a.PHRASES,ns:[x.a.PHRASES],debug:!1,react:{useSuspense:!1},interpolation:{escapeValue:!1},backend:w});v.a;var O=n(93),C=n(207),P=n(125),_=n(98),L=n(124),I=n(3);var A,M=function(t){var e=t.children,n=r.a.useState(Object(L.c)()),a=Object(m.a)(n,2),o=a[0],i=a[1];r.a.useEffect((function(){Object(P.e)().then(L.c).then(i).catch(_.a)}),[]);var s=r.a.useMemo((function(){return[o,i]}),[o]);return Object(I.jsx)(L.b.Provider,{value:s,children:e})},D="/move/:userName/:workoutId",T=n(192),R=n(663),N=n(664),z=n(661),F=n(665),H=["title","titleId"];function B(){return(B=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t}).apply(this,arguments)}function U(t,e){if(null==t)return{};var n,a,r=function(t,e){if(null==t)return{};var n,a,r={},o=Object.keys(t);for(a=0;a<o.length;a++)n=o[a],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(a=0;a<o.length;a++)n=o[a],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function W(t,e){var n=t.title,r=t.titleId,o=U(t,H);return a.createElement("svg",B({width:48,height:48,viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:e,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,A||(A=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M25.3039 5.25899L25.2241 5.13273L25.1351 5.01795C24.482 4.25466 23.2206 4.335 22.6956 5.25899L3.94558 38.259L3.8857 38.3754C3.43361 39.3526 4.1439 40.5 5.24976 40.5H42.7498L42.8805 40.4946C43.9535 40.4049 44.6003 39.2205 44.0539 38.259L25.3039 5.25899ZM25.5 14H22.5V29H25.5V14ZM24 31.5C22.6193 31.5 21.5 32.6193 21.5 34C21.5 35.3807 22.6193 36.5 24 36.5C25.3807 36.5 26.5 35.3807 26.5 34C26.5 32.6193 25.3807 31.5 24 31.5Z",fill:"#303030"})))}var V=a.forwardRef(W),G=(n.p,Object(z.a)((function(t){return{root:{position:"fixed",height:"100%",width:"100%",left:0,top:0,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",background:t.palette.background.default},alertIcon:{width:"4rem",height:"4rem",marginBottom:"-0.5rem"},title:{textTransform:"uppercase",marginTop:"4rem"},button:{marginTop:"4rem",padding:"1.2rem 2.5rem"}}}),{name:"ErrorView"}));var K=function(t){var e=t.status,n=G(t),a=Object(F.a)([x.a.TRANSLATIONS]).t;return Object(I.jsxs)("div",{className:n.root,children:[Object(I.jsx)(V,{className:n.alertIcon}),"number"===typeof e&&Object(I.jsx)(R.a,{variant:"body1",children:a("".concat(x.a.TRANSLATIONS,":ERROR_VIEW_STATUS_CODE_ERROR"),{statusCode:e})}),Object(I.jsx)(R.a,{variant:"h1",className:n.title,children:Object(I.jsx)("strong",{children:a(["".concat(x.a.TRANSLATIONS,":ERROR_VIEW_").concat(e,"_TITLE"),"".concat(x.a.TRANSLATIONS,":ERROR_VIEW_UNSPECIFIC_TITLE")])})}),Object(I.jsx)(R.a,{component:"p",variant:"h6",children:a(["".concat(x.a.TRANSLATIONS,":ERROR_VIEW_").concat(e,"_SUBTITLE"),"".concat(x.a.TRANSLATIONS,":ERROR_VIEW_UNSPECIFIC_SUBTITLE")])}),Object(I.jsx)(N.a,{href:"https://suunto.com",variant:"contained",color:"primary",className:n.button,children:Object(I.jsx)("strong",{children:a("GO_TO_HOMEPAGE")})})]})},J=n(213),q=n(551),Z=n(208),Q=n(209),$=Object(z.a)((function(t){return{root:{position:"fixed",height:"100%",width:"100%",left:0,top:0,display:"flex",alignItems:"center",justifyContent:"center",background:t.palette.background.default,zIndex:1e3}}}),{name:"LoadingView"});var X=function(t){var e=$(t),n=Object(q.a)();return Object(I.jsx)("div",{className:e.root,children:Object(I.jsx)("img",{src:"dark"===n.palette.mode?Q.a:Z.a,alt:"Suunto logo"})})},Y=n(214),tt=r.a.lazy((function(){return Promise.all([n.e(7),n.e(3)]).then(n.bind(null,784))}));var et=function(){var t=Object(p.f)(),e=t.workoutId,n=t.userName,o=r.a.useState(void 0),i=Object(m.a)(o,2),s=i[0],u=i[1],c=r.a.useState(null),l=Object(m.a)(c,2),d=l[0],f=l[1],h=Object(J.a)(!0),v=Object(m.a)(h,2),g=v[0],b=v[1];return r.a.useEffect((function(){Object(P.d)(n,e).then(u).catch((function(t){return f(t.status||!0)})).finally(b())}),[e,n]),d?Object(I.jsx)(K,{status:d}):Object(I.jsxs)(I.Fragment,{children:[Object(I.jsx)(a.Suspense,{fallback:Object(I.jsx)(X,{}),children:Object(I.jsx)(Y.b,{children:Object(I.jsx)(tt,{workout:s,workoutLoading:g},e)},e)}),g&&Object(I.jsx)(X,{})]})};f.a.getInstance().init({domain:"https://event.suunto.com",secretKey:"TQ9Wu1t|rIpD4*p",userId:function(){try{return window.location.href.replace(/https?:\/\/[^/]+\/(?:workout|move)\/([^/]+)\/.*/g,"$1")}catch(t){return"undefined"}}(),client:"738f766a4d54401d976c49bceb331ab5"});var nt=function(){var t=Object(s.a)("(prefers-color-scheme: dark)"),e=r.a.useMemo((function(){return Object(c.a)(t?T.b:T.c)}),[t]);return Object(I.jsx)(M,{children:Object(I.jsx)(O.a.Provider,{value:{},children:Object(I.jsx)(C.b,{children:Object(I.jsx)(l.a,{injectFirst:!0,children:Object(I.jsxs)(d.a,{theme:e,children:[Object(I.jsx)(u.a,{}),Object(I.jsx)(h.a,{children:Object(I.jsx)(p.c,{children:Object(I.jsx)(p.a,{path:D,component:et})})})]})})})})})},at=function(t){t&&t instanceof Function&&n.e(11).then(n.bind(null,782)).then((function(e){var n=e.getCLS,a=e.getFID,r=e.getFCP,o=e.getLCP,i=e.getTTFB;n(t),a(t),r(t),o(t),i(t)}))};i.a.render(Object(I.jsx)(r.a.StrictMode,{children:Object(I.jsx)(nt,{})}),document.getElementById("root")),at()},57:function(t,e,n){"use strict";var a,r;n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return r})),function(t){t.AltitudeStreamExtension="AltitudeStreamExtension",t.AppBundleIDExtension="AppBundleIDExtension",t.CadenceStreamExtension="CadenceStreamExtension",t.DepthStreamExtension="DepthStreamExtension",t.DistanceDeltaStreamExtension="DistanceDeltaStreamExtension",t.DistanceLapExtension="DistanceLapExtension",t.DiveHeaderExtension="DiveHeaderExtension",t.DurationLapExtension="DurationLapExtension",t.EnergyConsumptionStreamExtension="EnergyConsumptionStreamExtension",t.EpocStreamExtension="EpocStreamExtension",t.FitnessExtension="FitnessExtension",t.HeartRateExtension="HeartRateExtension",t.HeartrateStreamExtension="HeartrateStreamExtension",t.IBIStreamExtension="IBIStreamExtension",t.IntervalStreamExtension="IntervalStreamExtension",t.IntensityExtension="IntensityExtension",t.LocationStreamExtension="LocationStreamExtension",t.ManualLapStreamExtension="ManualLapStreamExtension",t.MultisportMarkerExtension="MultisportMarker",t.PauseMarkerExtension="PauseMarkerExtension",t.PoolLengthExtension="PoolLengthExtension",t.PowerStreamExtension="PowerStreamExtension",t.RelativePerformanceLevelStreamExtension="RelativePerformanceLevelStreamExtension",t.SeaLevelPressureStreamExtension="SeaLevelPressureStreamExtension",t.SkiExtension="SkiExtension",t.SkiHeaderExtension="SkiHeaderExtension",t.SkiTurnExtension="SkiTurnExtension",t.SMLExtension="SMLExtension",t.SpeedStreamExtension="SpeedStreamExtension",t.StepCountDeltaStreamExtension="StepCountDeltaStreamExtension",t.StrokeRateStreamExtension="StrokeRateStreamExtension",t.StrokesDeltaStreamExtension="StrokesDeltaStreamExtension",t.SummaryExtension="SummaryExtension",t.SwimmingHeaderExtension="SwimmingHeaderExtension",t.SwolfStreamExtension="SwolfStreamExtension",t.TemperatureStreamExtension="TemperatureStreamExtension",t.VerticalLapExtension="VerticalLapExtension",t.VerticalSpeedStreamExtension="VerticalSpeedStreamExtension"}(a||(a={})),function(t){t.small="s",t.medium="m",t.large="l"}(r||(r={}))},60:function(t,e,n){"use strict";e.a={CONTROLS:"controls",PHRASES:"phrases",UNITS:"units",TRANSLATIONS:"translations"}},65:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return s}));var a=n(53),r=n(54),o=function(){function t(e,n,r){Object(a.a)(this,t),this.x=e,this.y=n,this.z=r}return Object(r.a)(t,[{key:"multiply",value:function(e){return new t(this.x*e,this.y*e,this.z*e)}},{key:"divide",value:function(e){return new t(this.x/e,this.y/e,this.z/e)}},{key:"add",value:function(e){return new t(this.x+e.x,this.y+e.y,this.z+e.z)}},{key:"subtract",value:function(e){return new t(this.x-e.x,this.y-e.y,this.z-e.z)}},{key:"length",value:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}},{key:"normalize",value:function(){var e=this.length();return 0==e?new t(0,0,0):this.multiply(1/e)}},{key:"distance",value:function(t){var e=t.x-this.x,n=t.y-this.y,a=t.z-this.z;return Math.sqrt(e*e+n*n+a*a)}}]),t}();function i(t){return t*(Math.PI/180)}function s(t){return t/(Math.PI/180)}},93:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return u})),n.d(e,"c",(function(){return c}));var a,r=n(29),o=n(0),i=n.n(o),s=n(152);!function(t){t.SharedWorkoutScreen="SharedWorkoutScreen",t.SharedWorkoutPopularProductClicked="SharedWorkoutPopularProductClicked",t.SharedWorkoutPlaybackCompleted="SharedWorkoutPlaybackCompleted",t.SharedWorkoutPlaybackStarted="SharedWorkoutPlaybackStarted",t.SharedWorkoutAttachedMediaViewed="SharedWorkoutAttachedMediaViewed"}(a||(a={}));var u=i.a.createContext({});u.displayName="AmplitudeContext";var c=function(){var t=i.a.useContext(u);return{logEvent:i.a.useCallback((function(e,n){s.a.getInstance().logEvent(e,Object(r.a)(Object(r.a)({},t),n))}),[t])}}},98:function(t,e,n){"use strict";e.a=function(){}}},[[542,5,6]]]);
//# sourceMappingURL=main.e336c9eb.chunk.js.map